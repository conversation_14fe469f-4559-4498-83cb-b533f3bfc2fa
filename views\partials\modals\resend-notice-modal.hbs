<div id="resend-notice-confirm-modal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Confirm</h4>
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
      </div>
      <div class="modal-body">
        <div class="col-md-12 p-1">
          <p id="resend-notice-email-text">You are about to resend the decline notice to <span class="font-weight-bold"
              id="resend-email-address"></span>.</p>
          <p>Do you want to update the email address?</p>

          <div class="form-group mt-3">
            <div class="custom-control custom-radio">
              <input type="radio" id="update-email-yes" name="update-email-option" class="custom-control-input">
              <label class="custom-control-label" for="update-email-yes">Yes</label>
            </div>
            <div class="custom-control custom-radio mt-2">
              <input type="radio" id="update-email-no" name="update-email-option" class="custom-control-input">
              <label class="custom-control-label" for="update-email-no">No</label>
            </div>
          </div>

          <div id="new-email-container" class="form-group mt-3" style="display: none;">
            <label for="new-email-input">New Email Address:</label>
            <input type="email" id="new-email-input" class="form-control" placeholder="Enter new email address">
            <div class="invalid-feedback">Please enter a valid email address</div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-light waves-effect" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary waves-effect waves-light" id="confirm-resend-btn">
          <i class="fa fa-share mr-2"></i>Resend
        </button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  // Show/hide email input based on radio selection
  $('input[name="update-email-option"]').on('change', function () {
    if ($('#update-email-yes').is(':checked')) {
      $('#new-email-container').show();
    } else {
      $('#new-email-container').hide();
    }
  });

  // Reset form when modal is closed
  $('#resend-notice-confirm-modal').on('hidden.bs.modal', function () {
    $('input[name="update-email-option"]').prop('checked', false);
    $('#new-email-input').val('');
    $('#new-email-container').hide();
    $('#new-email-input').removeClass('is-invalid');
  });

  // Handle resend button click
  $('#confirm-resend-btn').on('click', function () {
    // Check if an option is selected
    if (!$('input[name="update-email-option"]:checked').length) {
      toastr["warning"]('Please select whether you want to update the email address', 'Warning!');
      return;
    }

    // If "Yes" is selected, validate the email
    let newEmail = null;
    if ($('#update-email-yes').is(':checked')) {
      newEmail = $('#new-email-input').val().trim();
      if (!newEmail || !isValidEmail(newEmail)) {
        $('#new-email-input').addClass('is-invalid');
        return;
      } else {
        $('#new-email-input').removeClass('is-invalid');
      }
    }

    // Disable button to prevent multiple clicks
    $(this).prop('disabled', true);
    $(this).html('<i class="fa fa-spinner fa-spin"></i> Sending...');

    // Send request to resend notice
    $.ajax({
      type: "POST",
      url: "/decline-notices/" + currentSelectedApplicationId + "/resend",
      data: JSON.stringify({
        email: newEmail
      }),
      contentType: "application/json; charset=utf-8",
      success: function (response) {
        if (response.success) {
          $('#resend-notice-confirm-modal').modal('hide');
          if (response.emailSent) {
            toastr["success"](response.message, 'Success!');
          } else {
            toastr["error"](response.message, 'Error!');
          }
          // Refresh the modal data
          getDeclineNotice();
        } else {
          toastr["warning"](response.message, 'Warning!');
        }
        $('#confirm-resend-btn').prop('disabled', false);
        $('#confirm-resend-btn').text('Resend');
      },
      error: function (error) {
        toastr["error"]('Failed to resend notice', 'Error!');
        $('#confirm-resend-btn').prop('disabled', false);
        $('#confirm-resend-btn').text('Resend');
      }
    });
  });

  // Email validation helper function
  function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
</script>