<div class="modal fade" id="applicant-{{applicant._id}}-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-applicant-title" class="modal-title">Applicant</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-applicant-body" class="modal-body p-3">
                <form action="" class="form" id="applicantForm" style="border: none;padding: 5px;">
                    <!-- GENDER -->
                    <div class="row mt-2">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="genderApplicant">
                                    Gender*
                                </label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <div class="col-12 d-flex justify-content-end">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input type="radio" class="custom-control-input"
                                                    id="genderMaleApplicant" name="genderApplicant" required
                                                    value="Male" {{#ifEquals applicant.gender 'Male'
                                                    }}checked{{/ifEquals}} disabled />
                                                <label class="custom-control-label"
                                                    for="genderMaleApplicant">Male</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <div class="col-12 d-flex justify-content-end">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input type="radio" class="custom-control-input"
                                                    id="genderFemaleApplicant" name="genderApplicant" required
                                                    value="Female" {{#ifEquals applicant.gender 'Female'
                                                    }}checked{{/ifEquals}} disabled />
                                                <label class="custom-control-label"
                                                    for="genderFemaleApplicant">Female</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- FIRST NAME -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="firstNameApplicant">First
                                    Name</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="text" name="firstNameApplicant" id="firstNameApplicant"
                                    class="form-control" value="{{applicant.firstName}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- LAST NAME -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="lastNameApplicant">Last
                                    Name</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="text" name="lastNameApplicant" id="lastNameApplicant" class="form-control"
                                    value="{{applicant.lastName}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- DATE OF BIRTH -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-0">
                                <label for="dateOfBirthDayControlApplicant">Date
                                    of
                                    Birth</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="date" name="dateOfBirthApplicant" id="dateOfBirthApplicant"
                                    class="form-control" value="{{formatDate applicant.dateOfBirth "YYYY-MM-DD"}}"
                                    disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- ADDRESS -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="addressApplicant">Address</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="text" name="addressApplicant" id="addressApplicant" class="form-control"
                                    value="{{applicant.address}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- ADDRESS OPTIONAL -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="addressOptionalApplicant">Address
                                    (optional)</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="text" name="addressOptionalApplicant" id="addressOptionalApplicant"
                                    value="{{applicant.addressOptional}}" disabled class="form-control">
                            </div>
                        </div>
                    </div>
                    <!-- ISLAND -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="islandApplicant">Island</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <select class="custom-select" id="islandApplicant" name="islandApplicant"
                                    aria-required="true" disabled required>
                                    {{#each islands}}
                                    <option {{#ifEquals ../applicant.island name}} selected{{/ifEquals}}>{{name}}
                                    </option>
                                    {{/each}}
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- PHONE -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="phoneApplicant">Phone
                                    number</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3 w-100">
                                <input type="text" id="phoneApplicant" class="form-control" name="phoneApplicant"
                                    value="{{applicant.phone}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- EMAIL -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="emailApplicant">E-mail</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input name="emailApplicant" id="emailApplicant" class="form-control" type="email"
                                    value="{{applicant.email}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- IS SPOUSE? -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="isSpouseApplicant">
                                    Is this applicant your spouse?*
                                </label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <div class="col-12 d-flex justify-content-end">
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" class="custom-control-input" id="isSpouseApplicantYes"
                                            name="isSpouseApplicant" value="Yes" {{#if applicant.isSpouse
                                            }}checked{{/if}} disabled required />
                                        <label class="custom-control-label" for="isSpouseApplicantYes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" class="custom-control-input" id="isSpouseApplicantNo"
                                            value="No" name="isSpouseApplicant" {{#unless applicant.isSpouse
                                            }}checked{{/unless}} disabled />
                                        <label class="custom-control-label" for="isSpouseApplicantNo">No</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    for (i = new Date().getFullYear(); i > 1900; i--) {
        $('#dateOfBirthYearControlApplicant').append($('<option />').val(i).html(i));
    }

    $('#applicantModal').on('hidden.bs.modal', function () {
        $('#applicantModal input[type="text"]').val('').toggleClass('is-invalid', false);
        $('#applicantModal input[type="email"]').val('').toggleClass('is-invalid', false);
        $('#applicantModal input[type="radio"]').prop('checked', false).toggleClass('is-invalid', false);
        $('#applicantModal select').val('').toggleClass('is-invalid', false);
        selectedApplicantRow = null;
    });
</script>