<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>Dashboard Finance Officer</h2>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12 ml-1">
                            <h4>Search filters:</h4>
                        </div>

                    </div>
                    <div class="row p-1">
                        <div class="col-md-11">
                            <form method="POST" id="searchForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="row">
                                            <div class="col-md-5">
                                                <select name="exemptionType" id='exemptionType' class="custom-select"
                                                        onchange="">
                                                    <option value="" selected>Select...</option>
                                                    <option id="all" value=""
                                                        {{#ifEquals filters.exemptionType 'all'}} selected {{/ifEquals}}
                                                    >All
                                                    </option>
                                                    <option id="home-owner-policy"
                                                        {{#ifEquals filters.exemptionType 'home-owner-policy'}}
                                                            selected {{/ifEquals}}>
                                                        Home Owner Policy
                                                    </option>
                                                    <option id="import-duty-waiver"
                                                        {{#ifEquals filters.exemptionType 'import-duty-waiver'}}
                                                            selected {{/ifEquals}}>
                                                        Import Duty Waiver
                                                    </option>
                                                    <label for="exemptionType"></label>
                                                </select>
                                            </div>
                                            <div class="col-md-7">
                                                <input class='form-control' type='text' name='searchFilter'
                                                       placeholder="Enter at least 3 characters" id='search_filter'
                                                       value="{{filters.searchFilter}}"
                                                />
                                                <label for="search_filter"></label>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-3 text-left">
                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchDeclined"
                                                           class="custom-control-input"
                                                           id="declinedCheck" form="searchForm"
                                                        {{#if filters.searchDeclined}} checked {{/if}}
                                                    >
                                                    <label class="custom-control-label mt-1"
                                                           for="declinedCheck">Show declined</label>
                                                </div>

                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchCompleted"
                                                           class="custom-control-input"
                                                           id="searchCompleted" form="searchForm"
                                                        {{#if filters.searchCompleted}} checked {{/if}}
                                                    >
                                                    <label class="custom-control-label mt-1" for="searchCompleted">Show
                                                        completed</label>
                                                </div>
                                            </div>
                                            <div class="col-md-9 text-left">
                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchSaved"
                                                           class="custom-control-input"
                                                           id="searchSaved" form="searchForm"
                                                        {{#if filters.searchSaved}} checked {{/if}}
                                                    >
                                                    <label class="custom-control-label mt-1" for="searchSaved">Show
                                                        saved</label>
                                                </div>

                                                <div class="custom-control custom-checkbox text-left">
                                                    <input type="checkbox" name="searchRequested"
                                                           class="custom-control-input"
                                                           id="searchRequested" form="searchForm"
                                                        {{#if filters.searchRequested}} checked {{/if}}
                                                    >
                                                    <label class="custom-control-label mt-1" for="searchRequested">Show
                                                        information requested</label>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="col-md-4">
                                        <input type='SUBMIT' class='btn btn-light btn-sm waves-effect '
                                               value='Search'/>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">

                        <!-- AUTOMATICALY ASSIGNED TABLE -->
                        <div id="automatic-assign-table" class="row">
                            <div class="table">
                                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                                    <thead>
                                    <tr>
                                        <th class="tableYellow">Id</th>
                                        <th class="tableYellow">Timestamp</th>
                                        <th class="tableYellow">Created At</th>
                                        <th class="tableYellow">Reference Nr</th>
                                        <th class="tableYellow">Applicant</th>
                                        <th class="tableYellow">Parcel #</th>
                                        <th class="tableYellow">District</th>
                                        <th class="tableYellow">Island</th>
                                        <th class="tableYellow">Revenue forgone</th>
                                        <th class="tableYellow">Exemption type</th>
                                        <th class="tableYellow">Status</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each financeOfficers}}
                                        <tr class="exemptions-row">
                                            <td>{{ _id }}</td>
                                            <td>{{formatDate createdAt "x"}}</td>
                                            <td>{{formatDate createdAt "DD/MM/YYYY"}}</td>
                                            <td></td>
                                            <td>{{ transferorName }}</td>
                                            <td>{{ parcelNumber }}</td>
                                            <td>{{ district }}</td>
                                            <td>{{ island }}</td>
                                            <td>$ {{formatNumber value }}</td>
                                            <td>{{ exemptionType }}</td>
                                            <td>{{ status }}</td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2 justify-content-between ">
                        <a href="/../" class="btn btn-secondary width-lg waves-effect waves-light">
                            Back
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript" src="/javascripts/export-xls.js"></script>
<script type="text/javascript">
    let table;
    let currentSelectedApplicationId;
    $(document).ready(function () {
        table = $("#scroll-horizontal-datatable").DataTable({
            "columnDefs": [{"visible": false, "targets": [0, 1]}],
            "order": [[1, "asc"]],
            scrollX: !0,
            select: {style: "single"},
            language: {
                paginate: {
                    previous: "<i class='mdi mdi-chevron-left'>",
                    next: "<i class='mdi mdi-chevron-right'>"
                }
            },
            drawCallback: function () {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
            }
        });
        table.on('select', function (e, dt, type, indexes) {
            if (type === 'row') {
                let selectedRowData = table.rows(indexes).data();
                currentSelectedApplicationId = selectedRowData[0][0];
                if (table.row(indexes[0]).node().className.includes('exemptions-row')) {
                    window.location.href = 'exemptions/' + currentSelectedApplicationId + "/update";
                }
            }
        });
    });

    $('#btn-export-xls').click(function () {
        exportXlsFile("financeOfficer");
    });
</script>
