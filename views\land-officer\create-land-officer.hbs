<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submitForm" method="POST" autocomplete="off">
                            <div class="form-group">
                                <label for="relationType">Select Type of</label>
                                <select class="custom-select" id="relationType" name="relationType">
                                    <option id="empty-option" value="" disabled {{#ifEquals group ""}} selected
                                        {{/ifEquals}}>Select Type
                                    </option>
                                    <option id="natural-love-and-affection" value="natural-love-and-affection"
                                        {{#ifEquals group "natural-love-and-affection"}} selected {{/ifEquals}}>Natural
                                        Love & Affection
                                    </option>
                                    <option id="section-exemptions" value="section-exemptions"
                                        {{#ifEquals group "section-exemptions"}} selected {{/ifEquals}}>
                                        Section 23- Companies & 28- Bodies Corporate
                                    </option>
                                    <option id="charitable-institution" value="charitable-institution"
                                        {{#ifEquals group "charitable-institution"}} selected {{/ifEquals}}>Transfers to
                                        Charitable Institutions
                                    </option>
                                     <option id="transmission" value="transmission"
                                        {{#ifEquals group "transmission"}} selected {{/ifEquals}}>Transmission
                                    </option>
                                    <option id="refunds" value="refunds"
                                        {{#ifEquals group "refunds"}} selected {{/ifEquals}}>Refunds
                                    </option>
                                    <option id="remissions" value="remissions"
                                        {{#ifEquals group "remissions"}} selected {{/ifEquals}}>Remissions
                                    </option>
                                </select>
                                <br>
                                <br>
                                <a href="/land-officer/dashboard"
                                    class="btn btn-secondary width-lg waves-effect waves-light" id="backButton">
                                    Back
                                </a>
                            </div>

                            <div>
                                {{#if group}}
                                    {{>exemption-types-form files=templateFiles isLandSumission=true}}
                                {{/if}}
                            </div>

                        </form>
                    </div>
                    <div class="form-group">

                    </div>
                    <div class="row mt-2 justify-content-between">
                        <div class="col-1">

                        </div>
                        <div class="col-3">
                            <a style="color: rgb(255, 255, 255); background-color: rgb(230, 7, 7); border-color: #d10f0f;"
                                href="/land-officer/dashboard" id="cancelButton"
                                class="btn btn-secondary width-lg waves-effect waves-light">
                                Cancel
                            </a>
                        </div>
                        <div class="col-3">
                            <button
                                style="color: rgb(255, 255, 255); background-color: rgb(243, 161, 8); border-color: #f0b906;"
                                id="saveButton" data-toggle="modal" data-target="#submitApplicationModal"
                                data-status="save-application"
                                class="btn btn-secondary width-lg waves-effect waves-light">
                                Save
                            </button>
                        </div>
                        <div class="col-3">
                            <button
                                style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                type="button" data-status="submit-application" data-toggle="modal" id="submitFormBtn"
                                data-target="#submitApplicationModal" data-app-id="{{landOfficer._id}}"
                                class="btn btn-secondary width-lg waves-effect waves-light">
                                Submit
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>modals/upload-file-modal}}
{{>modals/submit-application-modal}}
<script type="text/javascript">
    //$('#submitFormBtn').prop('disabled', true);
    let typeSelected = $('#relationType option:selected').val();
    if (typeSelected === '') {
        $('#backButton').show();
        $('#cancelButton').hide();
        $('#submitFormBtn').hide();
        $('#saveButton').hide();
    } else {
        //$('#relationType').prop('disabled', true);
        $('#submitFormBtn').show();
        $('#saveButton').show();
        $('#cancelButton').show();
        $('#backButton').hide();
    }
    $('#relationType').on('change', function () {
        typeSelected = $('#relationType option:selected').val();
        $('#empty-relation-option').prop('selected', true);
        window.location.href = window.location.origin + window.location.pathname + "?group=" + typeSelected;
    });

</script>
