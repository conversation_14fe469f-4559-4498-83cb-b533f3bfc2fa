<div id="upload-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="uploadModal"
    style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="upload-modal-temp-title">
                    Upload file: <span id="upload-modal-temp-label" class="font-weight-bold"></span>
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <p>
                                Maximum of <span id="maxUploadTemp"></span> File(s), PDF only. File must not be
                                password protected.
                            </p>
                            <div id="uploadModalTempForm" class="dropzone">
                                <div class="fallback">
                                    <input name="fileUploaded" type="file" multiple />
                                </div>
                                <div class="dz-message needsclick">
                                    <i class="h1 text-muted dripicons-cloud-upload"></i>
                                    <h3>Drop files here or click to upload.</h3>
                                    <span class="text-muted">Files will be automatically uploaded</span>
                                </div>
                            </div>
                            <div id="uploadedTempFiles" class="mt-2 text-center text-muted"></div>
                        </div>
                        <!-- end card-body-->
                        <div class="modal-footer justify-content-end pb-0">
                            <button type="button" class="btn solid royal-blue" data-dismiss="modal">
                                <i class="mdi mdi-send mr-1"></i>Close
                            </button>
                        </div>
                    </div>
                    <!-- end card-->
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->
        </div>
    </div>
</div>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/uploadedfiles.precompiled.js"></script>

<script type="text/javascript">
    Dropzone.autoDiscover = false;
    $("#uploadedTempFiles").html('');
    let rowindex;
    let button = '';
    let fileGroup = '';
    let fileTypeId = '';
    let landOfficer = '';
    $(function () {
        let field = '';
        let name = '';
        let tempDropzone = new Dropzone('#uploadModalTempForm', {
            url: '/',
            acceptedFiles: 'application/pdf',
            autoProcessQueue: true,
            parallelUploads: 3,
            maxFilesize: 10,
            paramName: function () {
                return 'fileUploaded';
            },
            uploadMultiple: true,
            init: function () {
                this.on('processing', function () {
                    this.options.url = '/land-officer/upload-document';
                });
                this.on('success', function () {
                    refreshUploadedFiles(fileTypeId, field);
                    let $presentField = $('#' +field);
                    if ($presentField) {
                        $presentField.prop('checked', true);
                    }
                    let $fileUpload = $('#btn-' + field);
                    $fileUpload.text('Modify');
                    $fileUpload.css({
                        'background-color': '#0AC292',
                        'border-color': '#0AC292',
                    });
                });
                this.on('sending', function (file, xhr, formData) {
                    if (!formData.has('fileName')) {
                        formData.append('fileName', field);
                    }
                    if (!formData.has('fileTypeId')) {
                        formData.append('fileTypeId', fileTypeId);
                    }
                });

                this.on('errormultiple', function (files, response) {
                });

                this.on('maxfilesexceeded', function (file) {
                });

                this.on('resetFiles', function () {
                    if (this.files.length !== 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                    }
                    $('#maxUploadTemp').text(this.options.maxFiles);
                });
            },
        });

        $('#upload-modal').on('show.bs.modal', function (event) {
            button = $(event.relatedTarget); // Button that triggered the modal
            name = button.data('field'); //name of the file
            field = name.replace(/[\s\’\'\/\(\)]/g, ''); //formatted name with no special chars
            rowindex = button.data('row'); // row index
            fileTypeId = button.data('file-type-id');
            fileGroup = button.data('file-group');
            landOfficer = button.data('land-officer-id') ? button.data('land-officer-id') : '';
            $('#upload-modal-temp-label').text(name);
            refreshUploadedFiles(fileTypeId, field);
            var modal = $(this);
            const objDZ = Dropzone.forElement('#uploadModalTempForm');
            objDZ.emit('resetFiles');
        });

        $('#upload-temp-modal').on('hide.bs.modal', function (event) {
            Dropzone.forElement('#uploadModalTempForm').removeAllFiles(true);
            $('#uploadedTempFiles').html('');

        });
    });

    function deleteFile(officerId, fileTypeId, fileId, field, blobName) {
        $.ajax({
            type: 'DELETE',
            url: '/land-officer/files',
            data: {
                landOfficer: officerId,
                fileTypeId: fileTypeId,
                fileId: fileId
            },
            success: function (res) {
                if (res.result) {
                    refreshUploadedFiles(fileTypeId, field);
                    const objDZ = Dropzone.forElement('#uploadModalTempForm');
                    const index = objDZ.files.findIndex((file) => file.blobName === blobName);
                    if (index > -1) {
                        objDZ.files[index].pop();
                    }

                }
            },
            dataType: 'json',
        });
        return false;
    }

    function downloadFile(officerId, fileType, fileId) {
        let url = '';
        if (officerId) {
            url = "/land-officer/" + officerId + "/files/" + fileType + "/" +fileId + "/download";
        }
        else {
            url = "/land-officer/files/" + fileType + "/" + fileId + "/download";
        }

        window.open(url, "_blank");
        return false;
    }

    function refreshUploadedFiles(fileTypeId, field) {
        $.get('/land-officer/files', { fileTypeId: fileTypeId, landOfficer: landOfficer}, function (data) {
            let template = Handlebars.templates.uploadedfiles;
            let d = {
                fileTypeId: fileTypeId,
                files: data.files ? data.files : [],
                landOfficer: landOfficer,
                field: field,
            };
            let html = template(d);
            $('#uploadedTempFiles').html(html);

        if (data.length === 0) {
            let $presentField = $('#' + field);
            if ($presentField) {
                $presentField.prop('checked', false);
            }
            let $fileUpload = $('#btn-' + field);
            $fileUpload.text('Upload');
            $fileUpload.css({
                'background-color': '#0081b4',
                'border-color': '#0081b4',
            });
        }
            }
        );
    }

    $("#upload-modal").on("hidden.bs.modal", function(){
        $("#uploadedTempFiles").html('');
    });
</script>
