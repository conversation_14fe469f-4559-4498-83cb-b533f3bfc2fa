const mongoose = require('mongoose');
const ImportDutyWaiverApplicationModel = require('./models/importDutyWaiverApplication').ImportDutyWaiverApplicationModel;
const StampDutyApplicationModel = require('./models/stampDutyExemptionApplication').StampDutyApplicationModel;
const ReductionApplicationModel = require('./models/stampDutyReductionApplication').ReductionApplicationModel;
const dotenv = require('dotenv');

dotenv.config();

async function runScript() {
  try {
    await mongoose.connect(process.env.MONGODB, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      useFindAndModify: false,
    });

    const result = await updateMissingUsers();

    console.log('Script executed successfully', result);
  } catch (error) {
    console.error('Error in the script:', error);
  } finally {
    mongoose.disconnect();
  }
}

async function updateMissingUsers() {
  try {
    const applicationsExemption = await StampDutyApplicationModel.find({
      $or: [
        { 'stampDutyOfficerTier.comments.user': { $exists: false } },
        { 'psOfficerTier.comments.user': { $exists: false } }
      ]
    });
    console.log('Found exemption applications:', applicationsExemption.length);

    const applicationsReduction = await ReductionApplicationModel.find({
      $or: [
        { 'stampDutyOfficerTier.comments.user': { $exists: false } },
        { 'psOfficerTier.comments.user': { $exists: false } }
      ]
    });
    console.log('Found reduction applications:', applicationsReduction.length);

    const applicationsImportDuty = await ImportDutyWaiverApplicationModel.find({
      $or: [
        { 'financeOfficerTier.comments.user': { $exists: false } },
        { 'customsOfficerTier.comments.user': { $exists: false } },
        { 'deputyCommissionerTier.comments.user': { $exists: false } }
      ]
    });
    console.log('Found import duty applications:', applicationsImportDuty.length);

    for (let app of applicationsExemption) {
      // Update stampDutyOfficerTier comments
      if (app.stampDutyOfficerTier && app.stampDutyOfficerTier.comments) {
        app.stampDutyOfficerTier.comments.forEach(comment => {
          if (!comment.user) {
            comment.user = '<EMAIL>';
          }
        });
      }

      // Update psOfficerTier comments
      if (app.psOfficerTier && app.psOfficerTier.comments) {
        app.psOfficerTier.comments.forEach(comment => {
          if (!comment.user) {
            comment.user = '<EMAIL>';
          }
        });
      }

      await app.save({ validateBeforeSave: false });
    }
    for (let app of applicationsReduction) {
      // Update stampDutyOfficerTier comments
      if (app.stampDutyOfficerTier && app.stampDutyOfficerTier.comments) {
        app.stampDutyOfficerTier.comments.forEach(comment => {
          if (!comment.user) {
            comment.user = '<EMAIL>';
          }
        });
      }

      // Update psOfficerTier comments
      if (app.psOfficerTier && app.psOfficerTier.comments) {
        app.psOfficerTier.comments.forEach(comment => {
          if (!comment.user) {
            comment.user = '<EMAIL>';
          }
        });
      }

      await app.save({ validateBeforeSave: false });
    }
    for (let app of applicationsImportDuty) {
      // Update financeOfficerTier comments
      if (app.financeOfficerTier && app.financeOfficerTier.comments) {
        app.financeOfficerTier.comments.forEach(comment => {
          if (!comment.user) {
            comment.user = '<EMAIL>';
          }
        });
      }

      // Update customsOfficerTier comments
      if (app.customsOfficerTier && app.customsOfficerTier.comments) {
        app.customsOfficerTier.comments.forEach(comment => {
          if (!comment.user) {
            comment.user = '<EMAIL>';
          }
        });
      }

      // Update deputyCommissionerTier comments
      if (app.deputyCommissionerTier && app.deputyCommissionerTier.comments) {
        app.deputyCommissionerTier.comments.forEach(comment => {
          if (!comment.user) {
            comment.user = '<EMAIL>';
          }
        });
      }

      await app.save({ validateBeforeSave: false });
    }

    console.log('Updated missing user entries successfully.');
    return { success: true, total: applicationsExemption.length + applicationsReduction.length + applicationsImportDuty.length };
  } catch (error) {
    console.error('Failed to update documents:', error);
    throw error;
  }
}

runScript();