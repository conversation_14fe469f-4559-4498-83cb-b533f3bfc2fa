<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submissionForm">
                            <div class="row">
                                <div class="col-12">
                                    <h3>Audit</h3>
                                </div>
                            </div>
                            <div class="row mt-2 justify-content-between">
                                <div class="col-6">
                                    <div class="custom-control custom-checkbox custom-control-inline mt-2">
                                        <input type="checkbox" class="custom-control-input"
                                            id="auditReady" name="auditReady" {{#if
                                            landOfficer.auditReady}}checked{{/if}} value="Yes" />
                                        <label class="custom-control-label" for="auditReady">Application
                                            is audit ready?</label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="row justify-content-end">
                                        <button onclick="saveProgress()" type="button"
                                            class="btn btn-primary action-button width-xl">
                                            Save
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            {{>stamp-duty-officer/open-stamp-duty-officer files=landOfficer.files}}

                        </form>
                        <div class="row justify-content-between">
                            <div class="col-2">
                                <a href="javascript:history.back()"
                                   class="btn btn-secondary width-lg waves-effect waves-light">
                                    Back
                                </a>
                            </div>

                            {{#if canComplete}}
                                <div class="col-2">
                                    <div class="col-2">
                                        <div class="col-2">
                                            <button
                                                    style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                                    type="button" data-status="complete-button" data-toggle="modal"
                                                    data-officer="stamp-duty-officer"
                                                    data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                                    class="btn btn-secondary width-lg waves-effect waves-light">
                                                COMPLETE
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            {{else}}
                                {{#unless cantEdit}}
                                    <div class="col-3">
                                        <button
                                                style="color: rgb(255, 255, 255); background-color: rgb(243, 161, 8); border-color: #f0b906;"
                                                id="additionalButton" type="button" data-status="additional-button"
                                                data-toggle="modal" data-officer="stamp-duty-officer"
                                                data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                                class="btn btn-secondary width-lg waves-effect waves-light">
                                            Additional Information
                                        </button>
                                    </div>
                                    <div class="col-2">
                                        <button
                                                style="color: rgb(255, 255, 255); background-color: rgb(230, 7, 7); border-color: #d10f0f;"
                                                type="button" id="declineButton" data-status="decline-button"
                                                data-toggle="modal" data-officer="stamp-duty-officer"
                                                data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                                class="btn btn-secondary width-lg waves-effect waves-light">
                                            Decline
                                        </button>
                                    </div>
                                    <div class="col-2">
                                        <button
                                            style="color: rgb(255, 255, 255); background-color: rgb(243, 8, 8); border-color: #d10f0f;"
                                            id="conflictButton" type="button" data-status="conflict-button"
                                            data-toggle="modal" data-officer="stamp-duty-officer"
                                            data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                            class="btn btn-secondary width-lg waves-effect waves-light">
                                        Conflict
                                        </button>
                                    </div>
                                    <div class="col-2">
                                        <div class="col-2">
                                            <div class="col-2">
                                                <button
                                                        style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                                        type="button" data-status="pending-ps-button" data-toggle="modal"
                                                        data-officer="stamp-duty-officer"
                                                        data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                                        class="btn btn-secondary width-lg waves-effect waves-light">
                                                    Approve
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                {{/unless}}
                            {{/if}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>modals/upload-documents-modal}}
{{>modals/download-file-modal}}
{{>modals/land-registry-officer-modal}}
<script type="text/javascript">
function saveProgress() {
        $(".action-button").each(function () { $(this).prop('disabled', true) });
        const form = $('#submissionForm').serializeArray();
        let serializedForm = {};
        for (let i = 0; i < form.length; i++) {
            const nameField = form[i]['name'];
            serializedForm[nameField] = form[i]['value'];
        }
        $.ajax({
            type: "PUT",
            url: "./update-auditor",
            data: JSON.stringify(serializedForm),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.success) {
                    toastr["success"]('Your application was updated successfully', 'Application updated!');
                    window.location = './open';
                } else {
                    toastr["warning"](data.message, 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            }
        });
    }
</script>
