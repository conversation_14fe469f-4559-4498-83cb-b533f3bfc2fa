<div id="upload-documents-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="uploadModal"
    style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="upload-documents-modal-temp-title">
                    Upload file: <span id="upload-documents-modal-temp-label" class="font-weight-bold"></span>
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <p>
                                Maximum of <span id="maxUploadTemp"></span> File(s), PDF only. File must not be
                                password protected.
                            </p>
                            <div id="uploadTempModal" class="dropzone">
                                <div class="fallback">
                                    <input name="fileUploaded" type="file" multiple />
                                </div>
                                <div class="dz-message needsclick">
                                    <i class="h1 text-muted dripicons-cloud-upload"></i>
                                    <h3>Drop files here or click to upload.</h3>
                                    <span class="text-muted">Files will be automatically uploaded</span>
                                </div>
                            </div>
                            <div id="uploadedTempFiles" class="mt-2 text-center text-muted"></div>
                        </div>
                        <!-- end card-body-->
                        <div class="modal-footer justify-content-end pb-0">
                            <button type="button" class="btn solid royal-blue" data-dismiss="modal">
                                <i class="mdi mdi-send mr-1"></i>Close
                            </button>
                        </div>
                    </div>
                    <!-- end card-->
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->
        </div>
    </div>
</div>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/uploadedfiles.precompiled.js"></script>

<script type="text/javascript">
    Dropzone.autoDiscover = false;

    let btn = '';
    let fileGroupName = '';
    let fileType = '';
    let submissionId = '';
    $(function () {
        let field = '';
        let name = '';
        let tempDropzone = new Dropzone('#uploadTempModal', {
            url: '/',
            acceptedFiles: 'application/pdf',
            autoProcessQueue: true,
            parallelUploads: 3,
            maxFilesize: 10,
            paramName: function () {
                return 'fileUploaded';
            },
            uploadMultiple: true,
            init: function () {
                this.on('processing', function () {
                    this.options.url = '/ps-submissions/' + submissionId +'/upload-document';
                });
                this.on('success', function () {
                    refreshFiles(fileType, field);
                    let $presentField = $('#' +field);
                    if ($presentField) {
                        $presentField.prop('checked', true);
                    }
                    let $fileUpload = $('#btn-' + field);
                    $fileUpload.text('Modify');
                    $fileUpload.css({
                        'background-color': '#0AC292',
                        'border-color': '#0AC292',
                    });
                });
                this.on('sending', function (file, xhr, formData) {
                    if (!formData.has('fileName')) {
                        formData.append('fileName', field);
                    }
                    if (!formData.has('fileType')) {
                        formData.append('fileType', fileType);
                    }
                    if (!formData.has('fileGroup')) {
                        formData.append('fileGroup', fileGroupName);
                    }
                });

                this.on('errormultiple', function (files, response) {
                });

                this.on('maxfilesexceeded', function (file) {
                });

                this.on('resetFiles', function () {
                    if (this.files.length !== 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                    }
                    $('#maxUploadTemp').text(this.options.maxFiles);
                });
            },
        });

        $('#upload-documents-modal').on('show.bs.modal', function (event) {
            btn = $(event.relatedTarget); // Button that triggered the modal
            name = btn.data('field'); //name of the file
            field = name.replace(/[\s\’\'\/\(\)]/g, ''); //formatted name with no special chars
            fileType = btn.data('file-type-id');
            fileGroupName = btn.data('file-group');
            submissionId = btn.data('submission-id');
            $('#upload-documents-modal-temp-label').text(name);
            refreshFiles(fileType, field);
            var modal = $(this);
            const objDZ = Dropzone.forElement('#uploadTempModal');
            objDZ.emit('resetFiles');
        });

        $('#upload-temp-modal').on('hide.bs.modal', function (event) {
            Dropzone.forElement('#uploadTempModal').removeAllFiles(true);
            $('#uploadedTempFiles').html('');

        });
    });

    function deleteFile(officerId, fileTypeId, fileId, field, blobName) {
        $.ajax({
            type: 'DELETE',
            url: '/land-officer/files',
            data: {
                landOfficer: officerId,
                fileTypeId: fileTypeId,
                fileId: fileId,
                fileGroup: fileGroupName
            },
            success: function (res) {
                if (res.result) {
                    refreshFiles(fileTypeId, field);
                    const objDZ = Dropzone.forElement('#uploadTempModal');
                    const index = objDZ.files.findIndex((file) => file.blobName === blobName);
                    if (index > -1) {
                        objDZ.files[index].pop();
                    }

                }
            },
            dataType: 'json',
        });
        return false;
    }

    function downloadFile(officerId, fileType, fileId) {
        let url = '';
        if (officerId) {
            url = "/land-officer/" + officerId + "/files/" + fileType + "/" +fileId + "/download";
        }
        else {
            url = "/land-officer/" + fileType + "/" + fileId + "/download";
        }

        window.open(url, "_blank");
        return false;
    }

    function refreshFiles(fileType, field) {
        $.get('/land-officer/files', { fileTypeId: fileType, landOfficer: submissionId, fileGroup: fileGroupName}, function (data) {

            let template = Handlebars.templates.uploadedfiles;
            let d = {
                fileTypeId: fileType,
                files: data.files ? data.files : [],
                landOfficer: submissionId,
                field: field,
            };
            let html = template(d);
            $('#uploadedTempFiles').html(html);

            if (data.length === 0) {
                let $presentField = $('#' + field);
                if ($presentField) {
                    $presentField.prop('checked', false);
                }
                let $fileUpload = $('#btn-' + field);
                $fileUpload.text('Upload');
                $fileUpload.css({
                    'background-color': '#0081b4',
                    'border-color': '#0081b4',
                });
            }
        });
    }
</script>
