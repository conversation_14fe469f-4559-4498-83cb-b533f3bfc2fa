<h3>Applicant Details:</h3>
<br>
<!-- GENDER -->
<div class="row mt-2">
    <div class="col-md-3">
        <div class="form-group mb-3">
            <label class="mb-2" for="gender">
                Gender
            </label>
        </div>
    </div>
    <div class="col-md-9">
        <div class="row">
            <div class="col-md-3">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="genderMale" name="gender" required
                                value="Male" {{#ifEquals application.gender 'Male' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="genderMale">Male</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="genderFemale" name="gender" required
                                value="Female" {{#ifEquals application.gender 'Female' }}checked{{/ifEquals}}
                                disabled />
                            <label class="custom-control-label" for="genderFemale">Female</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- FIRST NAME -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="firstName">First
                Name</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="firstName" id="firstName" class="form-control" value="{{application.firstName}}"
                disabled required>
        </div>
    </div>
</div>
<!-- LAST NAME -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="lastName">Last
                Name</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="lastName" id="lastName" class="form-control" value="{{application.lastName}}"
                disabled required>
        </div>
    </div>
</div>
<!-- DATE OF BIRTH -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-0">
            <label for="dateOfBirthDayControl">Date
                of
                Birth</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="date" name="dateOfBirth" id="dateOfBirth" class="form-control"
                value="{{formatDate application.dateOfBirth 'YYYY-MM-DD'}}" disabled required>
        </div>
    </div>
</div>
<!-- ADDRESS -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="address">Address</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="address" id="address" class="form-control" value="{{application.address}}" disabled
                required>
        </div>
    </div>
</div>
<!-- ADDRESS OPTIONAL -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="addressOptional">Address
                (optional)</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="addressOptional" id="addressOptional" class="form-control"
                value="{{application.addressOptional}}" disabled>
        </div>
    </div>
</div>
<!-- ISLAND -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="island">Island</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <select class="custom-select" id="island" name="island" aria-required="true" disabled required>
                {{#each islands}}
                <option {{#ifEquals ../application.island name}} selected{{/ifEquals}}>{{name}}
                </option>
                {{/each}}
            </select>
        </div>
    </div>
</div>
<!-- PHONE -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="phone">Phone
                number</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" id="phone" class="form-control" name="phone" value="{{application.phone}}" disabled>
        </div>
    </div>
</div>
<!-- EMAIL -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="email">E-mail</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input name="email" id="email" class="form-control" type="email" value="{{application.email}}" disabled
                required>
        </div>
    </div>
</div>
<!-- MARITAL STATUS-->
<div class="row mt-2">
    <div class="col-md-3">
        <div class="form-group mb-3">
            <label class="mb-2" for="maritalStatus">
                Marital status
            </label>
        </div>
    </div>
    <div class="col-md-9">
        <div class="row">
            <div class="col-md-3">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="maritalStatusSingle"
                                name="maritalStatus" required value="Single" {{#ifEquals
                                application.maritalStatus 'Single' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="maritalStatusSingle">Single</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="maritalStatusMarried"
                                name="maritalStatus" required value="Married" {{#ifEquals
                                application.maritalStatus 'Married' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="maritalStatusMarried">Married</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="maritalStatusDivorced"
                                name="maritalStatus" required value="Divorced" {{#ifEquals
                                application.maritalStatus 'Divorced' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="maritalStatusDivorced">Divorced</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="maritalStatusWidow"
                                name="maritalStatus" required value="Widow" {{#ifEquals
                                application.maritalStatus 'Widow' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="maritalStatusWidow">Widow</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<br>
{{#unless hideCitizenship}}
{{>client-forms/citizenship}}
{{/unless}}