<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submitForm" method="POST" autocomplete="off">
                            <div class="row mt-2">
                                <div class="col-md-12">
                                    <h2>Stamp Duty Reduction</h2>
                                    <br>
                                </div>
                            </div>
                            <hr>
                            <div class="row mt-2">
                                <div class="col-12">
                                    {{>client-forms/stamp-duty-exemption-log application=application}}
                                </div>
                            </div>
                            <hr>
                            {{>client-forms/stamp-duty-reduction-validate-form
                            application=application
                            islands=islands
                            calendar=calendar
                            validations=validations
                            filesInformation=filesInformation
                            disabledValidations=true}}
                            <hr class="bg-warning text-warning">

                            <h3>
                                Please fill in the remitted amount (USD)
                            </h3>
                            <div>
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <div class="form-group mb-0">
                                            <label for="remittedAmountControl">Remitted amount</label>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="input-group mb-3">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text" id="money-addon">USD</span>
                                            </div>
                                            <input type="number" name="remittedAmount" id="remittedAmountControl"
                                                class="form-control" min="0" value="{{application.remittedAmount}}"
                                                required disabled>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{#unless isNewApplication}}
                            {{#ifCond application.status '!=' 'DECLINED'}}
                            <h3>
                                Document uploads
                            </h3>
                            <div>
                                <div class="row mb-1">
                                    <div class="col-md-9" style="display: table-cell; vertical-align: middle;">
                                        <ul class="ml-3" style="list-style: disc;">
                                            <li>Remission Order</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-3 text-right">
                                        <button id="uploadRemissionOrderBtn" type="button"
                                            class="btn btn-block btn-warning mb-1" data-toggle="modal"
                                            data-target="#upload-files-modal" data-field="Remission Order"
                                            {{#ifCond remissionCount '>' 0}} style="background-color: #159a80; border-color: #148f77"{{else}}
                                            disabled{{/ifCond}}
                                            data-maxfiles="15" data-minfiles="1" data-group="remission-order">
                                            {{#ifCond remissionCount '>' 0}}Uploaded documents{{else}}Upload{{/ifCond}}
                                        </button>
                                    </div>
                                </div>
                                <div class="row mb-1">
                                    <div class="col-md-9" style="display: table-cell; vertical-align: middle;">
                                        <ul class="ml-3" style="list-style: disc;">
                                            <li>Additional information</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-3 text-right">
                                        <button id="uploadAdditionalInformationBtn" type="button"
                                            class="btn btn-block btn-warning mb-1" data-toggle="modal"
                                            data-target="#upload-files-modal" data-field="Additional information"
                                            {{#ifCond additionalInformationCount '>' 0}} style="background-color: #159a80;border-color: #148f77"{{else}}
                                            disabled{{/ifCond}}
                                            data-maxfiles="15" data-minfiles="1" data-group="additional-information">
                                            {{#ifCond additionalInformationCount '>' 0}}Uploaded documents{{else}}Upload{{/ifCond}}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {{/ifCond}}
                            {{/unless}}
                        </form>
                        <hr>
                        <div class="row">
                            <div class="col-md-2">
                                <a href="javascript:history.back()" class="btn btn-secondary btn-block action-button">
                                    Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>modals/upload-application-files-modal application=application stampDutyType='reduction' isComplete=true}}


<script type="text/javascript">
    let remissionCount = {{remissionCount}};
    let additionalInformationCount = {{additionalInformationCount}};

</script>
