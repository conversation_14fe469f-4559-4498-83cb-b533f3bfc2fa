<div class="mt-3">
    <h5>Applicants</h5>
    <div class="table-responsive mt-2">
        <table class="table table-striped nowrap">
            <thead>
                <tr>
                    <th style="width: 27%;">Full name</th>
                    <th style="width: 24%;">Address</th>
                    <th style="width: 10%;">Island</th>
                    <th style="width: 10%;">Phone</th>
                    <th style="width: 15%;">E-mail</th>
                    <th style="width: 14%;"></th>
                </tr>
            </thead>
            <tbody id="applicants-body">
                {{#each application.applicants}}
                <tr>
                    <th>{{firstName}} {{lastName}}</th>
                    <th>{{address}}</th>
                    <th>{{island}}</th>
                    <th>{{phone}}</th>
                    <th>{{email}}</th>
                    <th>
                        <button class="btn btn-primary solid royal-blue" type="button" data-toggle="modal" data-target="#applicant-{{_id}}-modal">
                            <i class=" fa fa-eye mr-2"></i>Open
                        </button>
                    </th>
                </tr>
                {{>modals/applicant-modal applicant=this islands=../islands}}
                {{/each}}
            </tbody>
        </table>
    </div>
</div>


<script type="text/javascript">

    let applicantsRows = 0;
    let applicants = [];
    let selectedApplicantRow;


    function openEditApplicantModal(row) {
        const applicant = applicants.find((applicant) => applicant.row == row);
        selectedApplicantRow = row;
        $('#dateOfBirthDayControlApplicant').val(applicant.dateOfBirth.split('-')[2]);
        $('#dateOfBirthMonthControlApplicant').val(applicant.dateOfBirth.split('-')[1]);
        $('#dateOfBirthYearControlApplicant').val(applicant.dateOfBirth.split('-')[0]);
        $('#firstNameApplicant').val(applicant.firstName);
        $('#lastNameApplicant').val(applicant.lastName);
        $('#addressApplicant').val(applicant.address);
        $('#addressOptionalApplicant').val(applicant.addressOptional);
        $('#islandApplicant').val(applicant.island);
        $('#phoneApplicant').val(applicant.phone);
        $('#emailApplicant').val(applicant.email);
        applicant.gender === 'Male' ? $('#genderMaleApplicant').prop('checked', true) : $('#genderFemaleApplicant').prop('checked', true);
        applicant.isSpouse ? $('#isSpouseApplicantYes').prop('checked', true) : $('#isSpouseApplicantNo').prop('checked', true);
        $('#applicantModal').modal();
    }

</script>