(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['uploadedapplicationfiles'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, alias1=depth0 != null ? depth0 : (container.nullContext || {}), lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"table-responsive\">\r\n        <table class=\"table table-striped mb-0\">\r\n            <thead>\r\n            <tr>\r\n                <th style=\"width: 60%\">Uploaded Files</th>\r\n"
    + ((stack1 = lookupProperty(helpers,"unless").call(alias1,(depth0 != null ? lookupProperty(depth0,"hideDelete") : depth0),{"name":"unless","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":7,"column":16},"end":{"line":9,"column":27}}})) != null ? stack1 : "")
    + "                <th style=\"width: 20%\">Download</th>\r\n            </tr>\r\n            </thead>\r\n            <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(alias1,(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"each","hash":{},"fn":container.program(4, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":14,"column":12},"end":{"line":32,"column":21}}})) != null ? stack1 : "")
    + "            </tbody>\r\n        </table>\r\n    </div> <!-- end .padding -->\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    return "                <th style=\"width: 20%\">Delete</th>\r\n";
},"4":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <tr>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"originalName") || (depth0 != null ? lookupProperty(depth0,"originalName") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"originalName","hash":{},"data":data,"loc":{"start":{"line":16,"column":24},"end":{"line":16,"column":40}}}) : helper)))
    + "</td>\r\n"
    + ((stack1 = lookupProperty(helpers,"unless").call(alias1,(depths[1] != null ? lookupProperty(depths[1],"hideDelete") : depths[1]),{"name":"unless","hash":{},"fn":container.program(5, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":17,"column":20},"end":{"line":24,"column":31}}})) != null ? stack1 : "")
    + "                    <td>\r\n                        <button class=\"demo-download-row btn btn-success btn-xs btn-icon\"\r\n                                onclick=\"downloadFile('"
    + alias4(container.lambda((depths[1] != null ? lookupProperty(depths[1],"group") : depths[1]), depth0))
    + "', '"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileId") || (depth0 != null ? lookupProperty(depth0,"fileId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileId","hash":{},"data":data,"loc":{"start":{"line":27,"column":71},"end":{"line":27,"column":81}}}) : helper)))
    + "')\">\r\n                            <i class=\"fa fa-download\"></i>\r\n                        </button>\r\n                    </td>\r\n                </tr>\r\n";
},"5":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                    <td>\r\n                        <button class=\"demo-delete-row btn btn-danger btn-xs btn-icon\"\r\n                                onclick=\"deleteFile('"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileId") || (depth0 != null ? lookupProperty(depth0,"fileId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileId","hash":{},"data":data,"loc":{"start":{"line":20,"column":53},"end":{"line":20,"column":63}}}) : helper)))
    + "' ,'"
    + alias4(container.lambda((depths[1] != null ? lookupProperty(depths[1],"group") : depths[1]), depth0))
    + "', '"
    + alias4(((helper = (helper = lookupProperty(helpers,"originalName") || (depth0 != null ? lookupProperty(depth0,"originalName") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"originalName","hash":{},"data":data,"loc":{"start":{"line":20,"column":83},"end":{"line":20,"column":101}}}) : helper)))
    + "');return false\">\r\n                            <i class=\"fa fa-times\"></i>\r\n                        </button>\r\n                    </td>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"if","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":36,"column":7}}})) != null ? stack1 : "");
},"useData":true,"useDepths":true});
})();