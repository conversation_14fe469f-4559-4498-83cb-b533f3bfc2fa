const fs = require('fs');
const pdfController = require('./pdfController');

exports.generateEmail = function (submission) {

  const template = submission.type === "LAND SUBMISSION" ? 'email-templates/notification-email.html' :
    'email-templates/notification-finance-email.html';
  const id = submission.type === "LAND SUBMISSION" ? submission.instrumentNumber : submission.transferorName;
  let htmlString = '';
  let textString = '';

  if (submission.type === "LAND SUBMISSION") {
    htmlString = fs
      .readFileSync(template, { encoding: 'utf-8' })
      .replace('##ID##', id)
      .replace('##TRANSFEROR##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##TRANSFEREE##', submission.transfereeName)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear reader, \n' +
      'There is a new submission ' + id + ' that requires your attention. ' +
      'Please visit the Stamp duty exemption portal to view this submission. \n' +
      'TRANSFEROR: ' + submission.transferorName + "\n" +
      'TRANSFEREE: ' + submission.transfereeName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  } else {
    htmlString = fs
      .readFileSync(template, { encoding: 'utf-8' })
      .replace('##ID##', id)
      .replace('##APPLICANT##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear reader, \n' +
      'There is a new submission ' + id + ' that requires your attention. ' +
      'Please visit the Stamp duty exemption portal to view this submission. \n' +
      'APPLICANT: ' + submission.transferorName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';

  }
  return { textString, htmlString };
};

exports.generateEmailConflict = function (submission) {

  const template = submission.type === "LAND SUBMISSION" ? 'email-templates/notification-email-conflict.html' :
    'email-templates/notification-finance-email.html';
  const id = submission.type === "LAND SUBMISSION" ? submission.instrumentNumber : submission.transferorName;
  let htmlString = '';
  let textString = '';

  if (submission.type === "LAND SUBMISSION") {
    htmlString = fs
      .readFileSync(template, { encoding: 'utf-8' })
      .replace('##ID##', id)
      .replace('##TRANSFEROR##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##TRANSFEREE##', submission.transfereeName)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear PS, \n' +
      'A submission ' + id + ' was marked as "Conflict of Interest" and requires your action. \n' +
      'TRANSFEROR: ' + submission.transferorName + "\n" +
      'TRANSFEREE: ' + submission.transfereeName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  } else {
    htmlString = fs
      .readFileSync(template, { encoding: 'utf-8' })
      .replace('##ID##', id)
      .replace('##APPLICANT##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear PS, \n' +
      'A submission ' + id + ' was marked as "Conflict of Interest" and requires your action. \n' +
      'APPLICANT: ' + submission.transferorName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';

  }
  return { textString, htmlString };
};

exports.generateEmailDeclined = function (submission, reason) {
  let htmlString = '';
  let textString = '';

  if (submission.type === "LAND SUBMISSION") {
    htmlString = fs
      .readFileSync('email-templates/notification-email-declined.html',
        { encoding: 'utf-8' })
      .replace('##TRANSFEREE##', submission.transfereeName)
      .replace('##TRANSFEROR##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##INSTRUMENTNUMBER##', submission.instrumentNumber)
      .replace('##REASON##', reason)
      .replace('##TRANSFEROR##', submission.transferorName)
      .replace('##TRANSFEREE##', submission.transfereeName)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear reader, \n' +
      'The application of ' + submission.transfereeName + ' and ' + submission.transferorName +
      ' regarding instrument number ' + submission.instrumentNumber + ' has been declined. \n' +
      'Reason why it was declined: ' + reason + '\n' +
      'TRANSFEROR: ' + submission.transferorName + "\n" +
      'TRANSFEREE: ' + submission.transfereeName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  }
  else {
    htmlString = fs.readFileSync('email-templates/notification-finance-email-declined.html',
      { encoding: 'utf-8' })
      .replace('##ID##', submission.transferorName)
      .replace('##REASON##', reason)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##APPLICANT##', submission.transferorName)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear reader, \n' +
      'The application regarding of applicant name ' + submission.transferorName + ' has been declined. \n' +
      'Reason why it was declined: ' + reason + '\n' +
      'APPLICANT: ' + submission.transferorName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  }
  return { textString, htmlString };
};

exports.generateEmailInformationRequested = function (submission) {
  let htmlString = '';
  let textString = '';

  if (submission.type === "LAND SUBMISSION") {
    htmlString = fs.readFileSync('email-templates/notification-email-information-requested.html',
      { encoding: 'utf-8' })
      .replace('##ID##', submission.instrumentNumber)
      .replace('##TRANSFEROR##', submission.transferorName)
      .replace('##TRANSFEREE##', submission.transfereeName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear reader, \n\n' + 'The application regarding ' + submission.instrumentNumber +
      ' you have submitted has been marked as incomplete and ' +
      'the Stamp Duty Officer has requested additional information to be provided.\n\n' +
      'TRANSFEROR: ' + submission.transferorName + "\n" +
      'TRANSFEREE: ' + submission.transfereeName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  }
  else {
    htmlString = fs.readFileSync('email-templates/notification-finance-email-information-requested.html',
      { encoding: 'utf-8' })
      .replace('##ID##', submission.transferorName)
      .replace('##APPLICANT##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear reader, \n\n' + 'The application regarding applicant name ' + submission.transferorName +
      ' you have submitted has been marked as incomplete and ' +
      'the Stamp Duty Officer has requested additional information to be provided.\n\n' +
      'APPLICANT: ' + submission.transferorName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  }


  return { textString, htmlString };
};


exports.generateEmailApprovedPS = function (submission) {
  const template = submission.type === "LAND SUBMISSION" ?
    'email-templates/notification-email-approved-ps.html' :
    'email-templates/notification-finance-email-approved-ps.html';
  let htmlString = '';

  let textString = '';

  if (submission.type === "LAND SUBMISSION") {
    htmlString = fs.readFileSync(template,
      { encoding: 'utf-8' })
      .replace('##ID##', submission.instrumentNumber)
      .replace('##TRANSFEROR##', submission.transferorName)
      .replace('##TRANSFEREE##', submission.transfereeName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear reader, \n' +
      'This has been approved by the Permanent Secretary, Finance. ' +
      'Kindly forward Transfer to the Collector of Stamp Duty for stamping and signing for ' + submission.instrumentNumber + '.\n' +
      'TRANSFEROR: ' + submission.transferorName + "\n" +
      'TRANSFEREE: ' + submission.transfereeName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  }
  else {
    htmlString = fs.readFileSync(template,
      { encoding: 'utf-8' })
      .replace('##ID##', submission.transferorName)
      .replace('##APPLICANT##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString = 'Dear reader, \n' +
      'This has been approved by the Permanent Secretary, Finance. ' +
      'Kindly forward Transfer to the Collector of Stamp Duty for stamping and signing for ' + submission.transferorName + '.' +
      'APPLICANT: ' + submission.transferorName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  }

  return { textString, htmlString };
};

exports.generateEmailCompleted = function (submission) {
  let htmlString = '';
  let textString = '';

  if (submission.type === "LAND SUBMISSION") {
    htmlString = fs
      .readFileSync('email-templates/notification-email-completed.html', { encoding: 'utf-8' })
      .replace('##ID##', submission.instrumentNumber)
      .replace('##TRANSFEROR##', submission.transferorName)
      .replace('##TRANSFEREE##', submission.transfereeName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString =
      'Dear reader, \n' +
      'The process has been completed for ' + submission.instrumentNumber + '.\n' +
      'TRANSFEROR: ' + submission.transferorName + "\n" +
      'TRANSFEREE: ' + submission.transfereeName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  } else {
    htmlString = fs
      .readFileSync('email-templates/notification-finance-email-completed.html', { encoding: 'utf-8' })
      .replace('##ID##', submission.transferorName)
      .replace('##APPLICANT##', submission.transferorName)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString =
      'Dear reader, \n' +
      'The process has been completed for application ' + submission.transferorName + '.\n' +
      'APPLICANT: ' + submission.transferorName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  }

  return { textString, htmlString };
};

exports.generateEmailTransferCompleted = function (submission) {
  let htmlString = '';
  let textString = '';

  if (submission.type === "LAND SUBMISSION") {
    htmlString = fs
      .readFileSync('email-templates/notification-email-transfer-completed.html',
        { encoding: 'utf-8' })
      .replace('##ID##', submission.instrumentNumber)
      .replace('##TRANSFEROR##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##TRANSFEREE##', submission.transfereeName)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString =
      'Dear reader, \n' +
      'The process has been completed the signed and upload of transfer for ' + submission.instrumentNumber + '.\n' +
      'TRANSFEROR: ' + submission.transferorName + "\n" +
      'TRANSFEREE: ' + submission.transfereeName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  } else {
    htmlString = fs
      .readFileSync('email-templates/notification-finance-email-transfer-completed.html',
        { encoding: 'utf-8' })
      .replace('##ID##', submission.transferorName)
      .replace('##APPLICANT##', submission.transferorName)
      .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
      .replace('##PARCEL_NUMBER##', submission.parcelNumber)
      .replace('##DISTRICT##', submission.district)
      .replace('##ISLAND##', submission.island)
      .replace('##VALUE##', submission.value);

    textString =
      'Dear reader, \n' +
      'The process has been completed the signed and upload of transfer for ' + submission.transferorName + '.\n' +
      'APPLICANT: ' + submission.transferorName + "\n" +
      'PARCEL NUMBER: ' + submission.parcelNumber + "\n" +
      'DISTRICT: ' + submission.district + "\n" +
      'ISLAND: ' + submission.island + "\n" +
      'VALUE: ' + submission.value + "\n" +
      'Kind regards,';
  }

  return { textString, htmlString };
};

exports.generateEmailImportDutyWaiverInformationRequest = function (submission) {
  let htmlString = '';
  let textString = '';
  const lastRequest = submission.informationRequests[submission.informationRequests.length - 1];

  const requestNames = {
    affidavit: "Affidavit",
    marriageCertificates: "Marriage Certificates",
    statusCard: "Status Card",
    birthCertificate: "Birth Certificate",
    governmentIssuedIdentification: "Government issued identification",
    parentsDocuments: "Documentation Parents",
    itemsInvoices: "Invoices",
    buildingPictures: "Photographs building or site",
    buildingPermit: "Building permit",
    landRegister: "Land Register extract",
    valuation: "Valuation report",
    botcCertificate: "BOTC Certificate",
    personalDetails: "Personal details",
    applicantDetails: "Applicant details",
    additionalApplicants: "Additional Applicants",
    propertyDetails: "Property details",
  }
  let missingDocumentsHtml = `<ul style="font-family:'Polaris Book';font-size:15.0pt; color:#0c0c0c">`;
  let missingDocumentsString = '';
  for (let document of lastRequest.requests) {
    missingDocumentsHtml += `<li>${requestNames[document]}</li>`;
    missingDocumentsString += `${requestNames[document]}\n`
  }
  missingDocumentsHtml += '</ul>'

  htmlString = fs
    .readFileSync('email-templates/import-duty-waiver/notification-information-request.html',
      { encoding: 'utf-8' })
    .replace('##NAME##', submission.filingYourself ? `${submission.firstName} ${submission.lastName}` : `${submission.personalInformation.firstName} ${submission.personalInformation.lastName}`)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr)
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##INFORMATION_REQUESTED##', missingDocumentsHtml)
    .replace('##MANAGEMENT_COMMENTS##', lastRequest.managementComments)
    .replace('##REQUEST_URL##', `${process.env.CLIENT_SITE_URL}import-duty-waiver/requested-information/${submission.referenceNr}/${submission._id}`);

  textString = `Your application with reference number ${submission.referenceNr} for the Import Duty Waiver was deferred` +
    `Please provide the following additional information/documentation:` +
    `${missingDocumentsString}\n` +
    `${lastRequest.managementComments}\n` +
    `Please do this in the webpage under this link.\n` +
    'We will continue to take your application into consideration after the additional information has been submitted.\n' +
    'We hope to have informed you sufficiently. '


  return { textString, htmlString };
}


exports.generateEmailStampDutyExemptionApplicationInformationRequest = function (submission) {
  let htmlString = '';
  let textString = '';
  const lastRequest = submission.informationRequests[submission.informationRequests.length - 1];

  const requestNames = {
    affidavit: "Affidavit",
    marriageCertificates: "Marriage Certificates",
    statusCard: "Status Card",
    birthCertificate: "Birth Certificate",
    governmentIssuedIdentification: "Government issued identification",
    parentsDocuments: "Documentation Parents",
    signedAgreement: "Signed agreement of the purchase of the property",
    buildingPictures: "Photographs building or site",
    buildingPermit: "Building permit",
    landRegister: "Land Register extract",
    valuation: "Valuation report",
    botcCertificate: "BOTC Certificate",
    personalDetails: "Personal details",
    applicantDetails: "Applicant details",
    additionalApplicants: "Additional Applicants",
    propertyDetails: "Property details",
    sellerDetails: "Sellers",
  };
  let missingDocumentsHtml = `<ul style="font-family:'Polaris Book';font-size:15.0pt; color:#0c0c0c">`;
  let missingDocumentsString = '';
  for (let document of lastRequest.requests) {
    missingDocumentsHtml += `<li>${requestNames[document]}</li>`;
    missingDocumentsString += `${requestNames[document]}\n`
  }
  missingDocumentsHtml += '</ul>'

  htmlString = fs
    .readFileSync('email-templates/stamp-duty-exemption/notification-information-request.html',
      { encoding: 'utf-8' })
    .replace('##NAME##', submission.filingYourself ? `${submission.firstName} ${submission.lastName}` : `${submission.personalInformation.firstName} ${submission.personalInformation.lastName}`)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr)
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##INFORMATION_REQUESTED##', missingDocumentsHtml)
    .replace('##MANAGEMENT_COMMENTS##', lastRequest.managementComments)
    .replace('##REQUEST_URL##', `${process.env.CLIENT_SITE_URL}stamp-duty-exemption/requested-information/${submission.referenceNr}/${submission._id}`);

  textString = `Your application with reference number ${submission.referenceNr} for the Stamp Duty Exemption was deferred` +
    `Please provide the following additional information/documentation:` +
    `${missingDocumentsString}\n` +
    `${lastRequest.managementComments}\n` +
    `Please do this in the web page under this link.\n` +
    'We will continue to take your application into consideration after the additional information has been submitted.\n' +
    'We hope to have informed you sufficiently. '


  return { textString, htmlString };
}

exports.generateEmailStampDutyExemptionApplicationDeclined = function (submission, reason) {
  let htmlString = '';
  let textString = '';
  let name = submission.filingYourself ? `${submission.firstName} ${submission.lastName}` : `${submission.personalInformation.firstName} ${submission.personalInformation.lastName}`
  htmlString = fs
    .readFileSync('email-templates/stamp-duty-exemption/notification-email-declined.html',
      { encoding: 'utf-8' })
    .replace('##NAME##', name)
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr)
    .replace('##REASON##', reason)
  textString = `Dear ${name}\n` +
    `We regret to inform you that your application for the Stamp Duty Exemption with reference number ${submission.referenceNr} has been denied for the following reason:` +
    `\n` +
    `We hope to have informed you sufficiently.\n` +
    'For questions regarding the conclusion of your application, please contact by replying to this email.\n' +
    'Sincerly \n' +
    'Turks and Caicos Islands'


  return { textString, htmlString };
}

exports.generateEmailStampDutyExemptionApplicationApprove = async function (submission) {
  let htmlString = '';
  let textString = '';

  htmlString = fs
    .readFileSync('email-templates/stamp-duty-exemption/notification-approved.html',
      { encoding: 'utf-8' })
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##NAME##', submission.filingYourself ? `${submission.firstName} ${submission.lastName}` : `${submission.personalInformation.firstName} ${submission.personalInformation.lastName}`)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr);

  textString = `We would like to inform you that your application for the Stamp Duty Exemption with reference number ${submission.referenceNr} has been accepted.\n` +
    `We hope to have informed you sufficiently.\n` +
    `For questions regarding the conclusion of your application, please contact by replying to this email. \n`;

  const pdf = await pdfController.generateStampDutyExemptionApprovalPdf(submission);
  return { textString, htmlString, pdf };
}

exports.generateEmailImportDutyWaiverApprove = async function (submission) {
  let htmlString = '';
  let textString = '';

  htmlString = fs
    .readFileSync('email-templates/import-duty-waiver/notification-approved.html',
      { encoding: 'utf-8' })
    .replace('##NAME##', submission.filingYourself ? `${submission.firstName} ${submission.lastName}` : `${submission.personalInformation.firstName} ${submission.personalInformation.lastName}`)
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr);

  textString = `We would like to inform you that your application for the Import Duty Waiver with reference number ${submission.referenceNr} has been accepted.\n` +
    `We hope to have informed you sufficiently.\n` +
    `For questions regarding the conclusion of your application, please contact by replying to this email. \n`;

  let pdf;
  if (submission.applicationType === 'existing') {
    pdf = await pdfController.generateImportDutyWaiverApprovalPdf(submission, "Re: Customs Import Duty Exemption on Existing Home Owner", "MINISTRY OF FINANCE", "510");
  } else if (submission.applicationType === 'disaster') {
    pdf = await pdfController.generateImportDutyWaiverApprovalPdf(submission, "Re: Customs Import Duty Exemption on Existing Homeowner In The Event of A Manmade or Natural Disaster", "MINISTRY OF FINANCE", "549");
  } else {
    pdf = await pdfController.generateImportDutyWaiverApprovalPdf(submission, "Re: Customs Import Duty Exemption on First Time Home Owners.", "MINISTRY OF IMMIGRATION AND BORDER SERVICES", "499");
  }

  return { textString, htmlString, pdf };
}

exports.generateEmailImportDutyWaiverDecline = function (submission, reason) {
  let htmlString = '';
  let textString = '';

  htmlString = fs
    .readFileSync('email-templates/import-duty-waiver/notification-declined.html',
      { encoding: 'utf-8' })
    .replace('##NAME##', submission.filingYourself ? `${submission.firstName} ${submission.lastName}` : `${submission.personalInformation.firstName} ${submission.personalInformation.lastName}`)
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr)
    .replace('##REASON##', reason);

  textString = `We regret to inform you that your application for the Import Duty Waiver with reference number ${submission.referenceNr} has been denied for the following reasons:\n` +
    reason +
    `We hope to have informed you sufficiently.\n` +
    `For questions regarding the conclusion of your application, please contact by replying to this email.\n`;


  return { textString, htmlString };
}

exports.generateApplicationOfficerEmail = async function (data) {
  let htmlString = '';
  let textString = '';

  htmlString = fs.readFileSync('email-templates/import-duty-waiver/notification-approved-officer.html',
    { encoding: 'utf-8' })
    .replace('##APPLICANT##', data.firstName + " " + data.lastName)
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##REFERENCE_NUMBER##', data.referenceNr);

  let pdf = await pdfController.generateImportWaiverApplicationPdf(data);

  textString = 'Dear Officer,\n' +
    `A new application for Import Duty Waiver has been received for your review. The reference number is ${data.referenceNr}. The name of the applicant is ${data.firstName} ${data.lastName}
    You can find the details of this application in the attached PDF(s).`;

  return { textString, htmlString, pdf };
};


exports.generateEmailReductionApplicationInformationRequest = function (submission) {
  let htmlString = '';
  let textString = '';
  const lastRequest = submission.informationRequests[submission.informationRequests.length - 1];

  const requestNames = {
    affidavit: "Affidavit",
    marriageCertificates: "Marriage Certificates",
    statusCard: "Status Card",
    birthCertificate: "Birth Certificate",
    governmentIssuedIdentification: "Government issued identification",
    parentsDocuments: "Documentation Parents",
    landRegister: "Land Register extract",
    valuation: "Valuation report",
    botcCertificate: "BOTC Certificate",
    personalDetails: "Personal details",
    applicantDetails: "Applicant details",
    companyDetails: "Company details",
    additionalApplicants: "Additional Applicants",
    propertyDetails: "Property details",
    citizenshipDetails: "Citizenship details",
  };
  let missingDocumentsHtml = `<ul style="font-family:'Polaris Book';font-size:15.0pt; color:#0c0c0c">`;
  let missingDocumentsString = '';
  for (let document of lastRequest.requests) {
    missingDocumentsHtml += `<li>${requestNames[document]}</li>`;
    missingDocumentsString += `${requestNames[document]}\n`
  }
  missingDocumentsHtml += '</ul>'

  htmlString = fs
    .readFileSync('email-templates/stamp-duty-reduction/notification-information-request.html',
      { encoding: 'utf-8' })
    .replace('##NAME##', submission.filingBehalf === 'Company' ? `${submission.companyDetails.name}` : `${submission.applicantDetails.firstName} ${submission.applicantDetails.lastName}`)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr)
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##INFORMATION_REQUESTED##', missingDocumentsHtml)
    .replace('##MANAGEMENT_COMMENTS##', lastRequest.managementComments)
    .replace('##REQUEST_URL##', `${process.env.CLIENT_SITE_URL}stamp-duty-rate-reduction/requested-information/${submission.referenceNr}/${submission._id}`);

  textString = `Your application with reference number ${submission.referenceNr} for the Stamp Duty Reduction was deferred` +
    `Please provide the following additional information/documentation:` +
    `${missingDocumentsString}\n` +
    `${lastRequest.managementComments}\n` +
    `Please do this in the web page under this link.\n` +
    'We will continue to take your application into consideration after the additional information has been submitted.\n' +
    'We hope to have informed you sufficiently. '


  return { textString, htmlString };
}

exports.generateEmailReductionApplicationDeclined = function (submission, reason) {
  let htmlString = '';
  let textString = '';
  let name = submission.filingBehalf === 'Company' ? `${submission.companyDetails.name}` : `${submission.applicantDetails.firstName} ${submission.applicantDetails.lastName}`;
  htmlString = fs
    .readFileSync('email-templates/stamp-duty-reduction/notification-email-declined.html',
      { encoding: 'utf-8' })
    .replace('##NAME##', name)
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr)
    .replace('##REASON##', reason)
  textString = `Dear ${name}\n` +
    `We regret to inform you that your application for the Stamp Duty Reduction with reference number ${submission.referenceNr} has been denied for the following reason:` +
    `\n` +
    `We hope to have informed you sufficiently.\n` +
    'For questions regarding the conclusion of your application, please contact by replying to this email.\n' +
    'Sincerly \n' +
    'Turks and Caicos Islands'


  return { textString, htmlString };
}

exports.generateEmailReductionApplicationApprove = async function (submission) {
  let htmlString = '';
  let textString = '';

  htmlString = fs
    .readFileSync('email-templates/stamp-duty-reduction/notification-approved.html',
      { encoding: 'utf-8' })
    .replace(/##CLIENT_URL##/g, process.env.CLIENT_SITE_URL)
    .replace('##NAME##', submission.filingBehalf === 'Company' ? `${submission.companyDetails.name}` : `${submission.applicantDetails.firstName} ${submission.applicantDetails.lastName}`)
    .replace('##REFERENCE_NUMBER##', submission.referenceNr);

  textString = `We would like to inform you that your application for the Stamp Duty Reduction with reference number ${submission.referenceNr} has been accepted.\n` +
    `We hope to have informed you sufficiently.\n` +
    `For questions regarding the conclusion of your application, please contact by replying to this email. \n`;

  const pdf = await pdfController.generateStampDutyReductionApprovalPdf(submission);
  return { textString, htmlString, pdf };
}
