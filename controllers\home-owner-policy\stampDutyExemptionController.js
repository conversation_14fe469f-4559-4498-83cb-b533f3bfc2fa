const ApplicationModel = require('../../models/stampDutyExemptionApplication').StampDutyApplicationModel;


exports.getApplicationFiles = async function (req, res) {
    try {
        const application = await ApplicationModel.findOne({ _id: req.params.id });
        if (!application) {
            return res.status(404).end();
        }
        const filesInformation = {
            affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length },
            marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length },
            parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length },
            signedAgreement: { name: "Signed agreement of the purchase of the property", files: application.signedAgreement, filesCount: application.signedAgreement.length },
            landRegister: { name: "A certified copy of the Land Register Extract in respect of the property", files: application.landRegister, filesCount: application.landRegister.length },
            valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length },
        }
        return res.render('home-owner-policy/file-list', { application, filesInformation, formType: 'stamp-duty-exemption' });
    } catch (error) {
        console.log("error ", error);
        return res.status(500).end();
    }
};