const cardHoldersModel = require('../models/cardHolder').cardHoldersModel;
const moment = require('moment');

exports.getCardHolderStatusReduction = async function (application) {
  let foundCardHolder;
  let fieldMatches = 0;
  let fieldMatchesCompanies = [];
  if (application.statusCardNumber) {
    foundCardHolder = await cardHoldersModel.findOne({
      'cardnr': {
        '$in': [application.statusCardNumber, 'TC' + application.statusCardNumber]
      }
    }).lean();
    if (foundCardHolder) {
      if (application.filingBehalf === 'Natural person') {
        if (foundCardHolder.firstname.toUpperCase() === application.applicantDetails.firstName.toUpperCase()) {
          fieldMatches++;
          foundCardHolder.matchFirstName = true;
        }
        if (foundCardHolder.lastname.toUpperCase() === application.applicantDetails.lastName.toUpperCase()) {
          fieldMatches++;
          foundCardHolder.matchLastName = true;
        }
        if (moment.utc(foundCardHolder.date_of_birth).format('DD/MM/YYYY') === moment.utc(application.applicantDetails.dateOfBirth).format('DD/MM/YYYY')) {
          fieldMatches++;
          foundCardHolder.matchDateOfBirth = true;
        }
      } else {
        for (const bo of application.companyDetails.beneficialOwners) {
          const currentBo = {};
          if (foundCardHolder.firstname.toUpperCase() === bo.firstName.toUpperCase()) {
            fieldMatches++;
            currentBo.matchFirstName = true;
          }
          if (foundCardHolder.lastname.toUpperCase() === bo.lastName.toUpperCase()) {
            fieldMatches++;
            currentBo.matchLastName = true;
          }
          if (moment.utc(foundCardHolder.date_of_birth).format('DD/MM/YYYY') === moment.utc(bo.dateOfBirth).format('DD/MM/YYYY')) {
            fieldMatches++;
            currentBo.matchDateOfBirth = true;
          }
          if (fieldMatches > Math.max(...fieldMatchesCompanies)) {
            foundCardHolder = {
              ...foundCardHolder,
              ...currentBo
            }
          }
          fieldMatchesCompanies.push(fieldMatches);
          fieldMatches = 0;
        }
        fieldMatches = Math.max(...fieldMatchesCompanies);
      }
    }
  }
  let statusInformationColor;
  if (!foundCardHolder || fieldMatches === 0) {
    statusInformationColor = 'text-red';
  } else if (fieldMatches === 1 || fieldMatches === 2) {
    statusInformationColor = 'text-orange';
  } else if (fieldMatches === 3) {
    statusInformationColor = 'text-green';
  }

  return {
    foundCardHolder,
    statusInformationColor
  };
}

exports.getCardHolderStatus = async function (application) {
  let foundCardHolder;
  let fieldMatches = 0;
  if (application.statusCardNumber) {
    foundCardHolder = await cardHoldersModel.findOne({
      'cardnr': {
        '$in': [application.statusCardNumber, 'TC' + application.statusCardNumber]
      }
    });
    if (foundCardHolder) {
      if (foundCardHolder.firstname.toUpperCase() === application.firstName.toUpperCase()) {
        fieldMatches++;
        foundCardHolder.matchFirstName = true;
      }
      if (foundCardHolder.lastname.toUpperCase() === application.lastName.toUpperCase()) {
        fieldMatches++;
        foundCardHolder.matchLastName = true;
      }
      if (moment.utc(foundCardHolder.date_of_birth).format('DD/MM/YYYY') === moment.utc(application.dateOfBirth).format('DD/MM/YYYY')) {
        fieldMatches++;
        foundCardHolder.matchDateOfBirth = true;
      }
    }
  }
  let statusInformationColor;
  if (!foundCardHolder || fieldMatches === 0) {
    statusInformationColor = 'text-red';
  } else if (fieldMatches === 1 || fieldMatches === 2) {
    statusInformationColor = 'text-orange';
  } else if (fieldMatches === 3) {
    statusInformationColor = 'text-green';
  }

  return {
    foundCardHolder,
    statusInformationColor
  };
}