<div>
    <div>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="intentOfProperty">
                        Intent of property
                    </label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="intentOfPropertyResidential"
                                name="intentOfProperty" value="Residential" required {{#ifEquals
                                application.intentOfProperty 'Residential' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="intentOfPropertyResidential">Residential</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="intentOfPropertyInvestment"
                                value="Investment" name="intentOfProperty" {{#ifEquals
                                application.intentOfProperty 'Investment' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="intentOfPropertyInvestment">Investment</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="intentOfPropertyCommercial"
                                value="Commercial" name="intentOfProperty" {{#ifEquals
                                application.intentOfProperty 'Commercial' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="intentOfPropertyCommercial">Commercial</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="filingYourself">
                        Filing this admission on behalf of a?
                    </label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="filingBehalfNaturalPerson"
                                name="filingBehalf" value="Natural person" required {{#ifEquals
                                application.filingBehalf 'Natural person' }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="filingBehalfNaturalPerson">Natural person</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="filingBehalfCompany" value="Company"
                                name="filingBehalf" {{#ifEquals application.filingBehalf 'Company'
                                }}checked{{/ifEquals}} disabled />
                            <label class="custom-control-label" for="filingBehalfCompany">Company</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{#ifEquals application.filingBehalf 'Company'}}
        <div id="company-details">
            <hr>
            {{>client-forms/company-details application=application}}
            {{>client-forms/beneficial-owners application=application}}
            <div class="row justify-content-end">
                <div class="col-3">
                    <div class="custom-control custom-checkbox custom-control-inline">
                        <input type="checkbox" class="custom-control-input" id="validatedCompanyDetails"
                            name="validatedCompanyDetails" {{#if validations.companyDetails}}checked{{/if}} {{#if
                            disabledValidations}}disabled{{/if}} value="Yes" required />
                        <label class="custom-control-label" for="validatedCompanyDetails">Validate
                            Information</label>
                    </div>
                </div>
            </div>
        </div>
        {{/ifEquals}}
        {{#ifEquals application.filingBehalf 'Natural person'}}
        <div id="applicant-details">
            {{>client-forms/applicant-details application=application.applicantDetails hideCitizenship=true
            island=island foundCardHolder=foundCardHolder
            statusInformationColor=statusInformationColor}}
            <div class="row justify-content-end">
                <div class="col-3">
                    <div class="custom-control custom-checkbox custom-control-inline">
                        <input type="checkbox" class="custom-control-input" id="validatedApplicantDetails"
                            name="validatedApplicantDetails" {{#if validations.applicantDetails}}checked{{/if}} {{#if
                            disabledValidations}}disabled{{/if}} value="Yes" required />
                        <label class="custom-control-label" for="validatedApplicantDetails">Validate
                            Information</label>
                    </div>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="additionalApplicants">
                        Are there any additional applicants?
                    </label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="additionalApplicantsYes"
                                name="additionalApplicants" value="Yes" {{#if
                                application.applicantDetails.additionalApplicants}}checked{{/if}} disabled required />
                            <label class="custom-control-label" for="additionalApplicantsYes">Yes</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input" id="additionalApplicantsNo" value="No"
                                name="additionalApplicants" {{#unless application.applicantDetails.additionalApplicants}}checked{{/unless}} disabled />
                            <label class="custom-control-label" for="additionalApplicantsNo">No</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{#if application.applicantDetails.additionalApplicants}}
        <div id="additional-applicants">
            {{>client-forms/additional-applicants application=application.applicantDetails islands=islands}}
            <div class="row justify-content-end">
                <div class="col-3">
                    <div class="custom-control custom-checkbox custom-control-inline">
                        <input type="checkbox" class="custom-control-input" id="validatedAdditionalApplicants" {{#if
                            validations.additionalApplicants}}checked{{/if}} {{#if disabledValidations}}disabled{{/if}}
                            name="validatedAdditionalApplicants" value="Yes" required />
                        <label class="custom-control-label" for="validatedAdditionalApplicants">Validate
                            Information</label>
                    </div>
                </div>
            </div>
        </div>
        {{/if}}
        {{/ifEquals}}
        <hr>
        <div id="property-details">
            {{>client-forms/property-details application=application islands=islands}}
            <div class="row justify-content-end">
                <div class="col-3">
                    <div class="custom-control custom-checkbox custom-control-inline">
                        <input type="checkbox" class="custom-control-input" id="validatedPropertyDetails"
                            name="validatedPropertyDetails" {{#if validations.propertyDetails}}checked{{/if}} {{#if
                            disabledValidations}}disabled{{/if}} value="Yes" required />
                        <label class="custom-control-label" for="validatedPropertyDetails">Validate
                            Information</label>
                    </div>
                </div>
            </div>
        </div>

        <hr>
        <div id="citizenship-details">
            <h3>Citizenship Details:</h3>
            <br>
            {{>client-forms/citizenship}}
        </div>
        <hr>
        <div>
            <div class="row mb-1">
                <div class="col-md-12">
                    <h3>Documents:</h3>
                </div>
            </div>
            <div class="row my-2">
                <div class="col-12">
                    {{#each filesInformation}}
                    {{#if filesCount}}
                    <hr>

                    <div class="row">
                        <div class="col-9">
                            <h4>{{name}}</h4>
                            <h5 class="mt-1">Files:</h5>
                        </div>
                        <div class="col-3">
                            <div class="custom-control custom-checkbox custom-control-inline">
                                <input type="checkbox" class="custom-control-input" id="validated_{{@key}}"
                                    name="validated_{{@key}}" {{#if validated}}checked{{/if}} value="Yes" {{#if
                                    ../disabledValidations}}disabled{{/if}} required />
                                <label class="custom-control-label" for="validated_{{@key}}">Validate
                                    Information</label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <ul style="list-style: disc;" class="ml-3">
                                {{#each files}}
                                <li>
                                    <a href="/home-owner-policy/stamp-duty-reduction/{{../../application._id}}/download/{{@../key}}/{{fileId}}"
                                        target="_blank">{{originalName}}</a>
                                </li>
                                {{/each}}
                            </ul>
                        </div>
                    </div>
                    {{/if}}
                    {{/each}}
                </div>
            </div>

        </div>
    </div>

</div>