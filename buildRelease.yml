# Starter pipeline
# Start with a minimal pipeline that you can customize to build and deploy your code.
# Add steps that build, run tests, deploy, and more:
# https://aka.ms/yaml

trigger:
- none

pool:
  vmImage: windows-latest

steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '18.12.1'
    displayName: 'Install Node.js'

  - script: |
      npm install
    displayName: 'npm install'


  - script: |
      npm run build
    displayName: 'npm build'
      
  - task: CopyFiles@2
    inputs:
      sourceFolder: '$(Build.SourcesDirectory)'
      contents: |
        **
        !scripts\**
        !.env
        !.eslintrc.json
        !.gitignore
        !buildRelease.yml
        !updateCommentUsers.js
      targetFolder: $(Build.ArtifactStagingDirectory)/npm
    displayName: 'Copy artifacts'   
  

  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/npm'
      artifactName: npm
    displayName: 'Publish npm artifact'
