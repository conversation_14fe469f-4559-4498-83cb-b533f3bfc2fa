<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submitForm" method="POST" autocomplete="off">
                            <div>
                                {{>form-review/open-land-officer-form landOfficer=financeOfficer files=financeOfficer.files}}
                            </div>


                        </form>
                        <div class="d-flex justify-content-between">
                            <a href="/finance-officer/dashboard"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>

                            {{#ifEquals financeOfficer.status 'TRANSFER PENDING'}}
                                <button
                                        style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                        type="button" data-status="complete-transfer-button"
                                        data-toggle="modal"
                                        data-officer="finance-officer"
                                        data-target="#landRegistryModal" data-id="{{financeOfficer._id}}"
                                        class="btn btn-secondary width-lg waves-effect waves-light">
                                    COMPLETE TRANSFER
                                </button>
                            {{/ifEquals}}

                            {{#ifCond isEditable '==' true}}
                                <button
                                        style="color: rgb(255, 255, 255); background-color: rgb(243, 161, 8); border-color: #f0b906;"
                                        id="saveButton" type="submit" form="submitForm"
                                        class="btn btn-secondary width-lg waves-effect waves-light">
                                    Save
                                </button>
                                <button
                                        style="color: rgb(255, 255, 255); background-color: rgb(230, 7, 7); border-color: #d10f0f;"
                                        type="button" id="deleteButton" data-status="delete-form"
                                        data-toggle="modal"
                                        data-target="#confirmDeleteModal"
                                        data-application-id="{{financeOfficer._id}}"
                                        class="btn btn-secondary width-lg waves-effect waves-light">
                                    Delete
                                </button>
                                <button
                                        style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                        type="button" data-status="submit-application" data-toggle="modal"
                                        data-target="#submitApplicationModal"
                                        data-id="{{financeOfficer._id}}"
                                        class="btn btn-secondary width-lg waves-effect waves-light">
                                    Submit
                                </button>
                            {{/ifCond}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

{{>modals/download-file-modal}}
{{#ifCond isEditable '==' true}}
    {{>modals/upload-file-modal}}
    {{>modals/submit-finance-modal}}
    {{>modals/confirm-delete-modal}}
{{else}}
    {{>modals/upload-documents-modal}}
    {{>modals/land-registry-officer-modal}}
{{/ifCond}}


<script type="text/javascript">
    let idValue = "{{financeOfficer._id}}";
    const myFormValidate = $("#submitForm")[0];
    $("#saveButton").click(function () {
        $('#submitForm').submit(function (event) {
            const saveBtn = $("#saveButton");
            saveBtn.prop('disabled', true);
            event.preventDefault();

            if (!myFormValidate.checkValidity()) {
                if (myFormValidate.reportValidity) {
                    myFormValidate.reportValidity();
                    return;
                }
            }
            $.ajax({
                url: '/finance-officer/' + idValue + '/update',
                type: 'POST',
                timeout: 3000,
                data: $(this).serialize(),
                success: function () {
                    Swal.fire('Success', 'The form has been saved successfully', 'success').then(() => {
                        location.href = '/finance-officer/dashboard';
                    });
                },
                error: function () {
                    Swal.fire('Error', 'There was an error saving the form', 'error').then(() => {
                        location.href = '/finance-officer/dashboard';
                    });
                },
            });
        });
    });
</script>
