const mongoose = require('mongoose');

const validationSchema = new mongoose.Schema({
    applicantDetails: { type: Boolean, required: false },
    personalDetails: { type: Boolean, required: false },
    additionalApplicants: { type: Boolean, required: false },
    propertyDetails: { type: Boolean, required: false },
    sellersDetails: { type: Boolean, required: false },
    affidavit: { type: Boolean, required: false },
    marriageCertificates: { type: Boolean, required: false },
    birthCertificate: { type: Boolean, required: false },
    parentsDocuments: { type: Boolean, required: false },
    signedAgreement: { type: Boolean, required: false },
    landRegister: { type: Boolean, required: false },
    valuation: { type: Boolean, required: false },
    statusCard: { type: Boolean, required: false },
    botcCertificate: { type: Boolean, required: false },
});

const fileSchema = new mongoose.Schema({
    fileId: { type: String, required: true },
    fieldName: { type: String, required: true },
    originalName: { type: String, required: true },
    encoding: { type: String, required: true },
    mimeType: { type: String, required: true },
    blobName: { type: String, required: true },
    container: { type: String, required: true },
    blob: { type: String, required: true },
    blobType: { type: String, required: true },
    size: { type: String, required: true },
    etag: { type: String, required: true },
    url: { type: String, required: true }
});

const commentsSchema = new mongoose.Schema({
    date: { type: Date, required: true },
    internalComments: { type: String, required: false },
    declineReason: { type: String, required: false },
    user: { type: String, required: true },
    status: { type: String, required: true },
});

const tierSchema = new mongoose.Schema({
    validations: validationSchema,
    approvedAt: { type: Date, required: false },
    completedAt: { type: Date, required: false },
    declinedAt: { type: Date, required: false },
    conflictedAt: { type: Date, required: false },
    comments: [commentsSchema],
    username: { type: String, required: false },
});

const propertyDetailsSchema = new mongoose.Schema({
    parcel: { type: String, required: true },
    district: { type: String, required: true },
    island: { type: String, required: true },
    value: { type: Number, required: true },
    valuationDate: { type: Date, required: true },
    notaryName: { type: String, required: true },
    notaryCountry: { type: String, required: true },
});

const personalDetailsSchema = new mongoose.Schema({
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    firm: { type: String, required: false },
    phone: { type: String, required: true },
    email: { type: String, required: true },
});

const applicantSchema = new mongoose.Schema({
    gender: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    dateOfBirth: { type: Date, required: true },
    address: { type: String, required: true },
    addressOptional: { type: String, required: false },
    island: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    isSpouse: { type: Boolean, required: true },
});

const sellerSchema = new mongoose.Schema({
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    firm: { type: String, required: false },
});

const requestInformationSchema = new mongoose.Schema({
    requests: [String],
    createdBy: { type: String, required: true },
    createdAt: { type: Date, required: true },
    internalComments: { type: String, required: false },
    managementComments: { type: String, required: false },
    userComments: { type: String, required: false },
    submittedAt: { type: Date, required: false },
});

const StampDutyApplicationSchema = new mongoose.Schema(
    {
        referenceNr: { type: String, required: true, unique: true },
        status: { type: String, required: true },
        filingYourself: { type: Boolean, required: true },
        sellers: [sellerSchema],
        applicants: [applicantSchema],
        personalInformation: personalDetailsSchema,
        propertyDetails: propertyDetailsSchema,
        // Applicant
        gender: { type: String, required: true },
        firstName: { type: String, required: true },
        lastName: { type: String, required: true },
        dateOfBirth: { type: Date, required: true },
        address: { type: String, required: true },
        addressOptional: { type: String, required: false },
        island: { type: String, required: true },
        phone: { type: String, required: true },
        email: { type: String, required: true },
        maritalStatus: { type: String, required: true },
        documentType: { type: String, required: true },
        statusCardNumber: { type: String, required: false },
        additionalApplicants: { type: Boolean, required: true },
        confirmation: { type: Boolean, required: true },
        affidavit: [fileSchema],
        marriageCertificates: [fileSchema],
        birthCertificate: [fileSchema],
        parentsDocuments: [fileSchema],
        signedAgreement: [fileSchema],
        landRegister: [fileSchema],
        valuation: [fileSchema],
        statusCard: [fileSchema],
        botcCertificate: [fileSchema],
        // Management information
        remittedAmount: { type: Number, required: false, default: 0 },
        stampDutyOfficerTier: tierSchema,
        psOfficerTier: tierSchema,
        auditReady: { type: Boolean, required: false, default: false },
        informationRequests: [requestInformationSchema],
        remissionOrder: [fileSchema],
        additionalInformation: [fileSchema],
    },
    {
        timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
    }
);



//Export model
module.exports.StampDutyApplicationModel = mongoose.model('stampdutyexemptionapplications', StampDutyApplicationSchema);

