{{!-- CONFIRM MODAL --}}
<div class="modal fade" id="landRegistryModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div  class="modal-body p-3 text-justify">
                <div id="message_modal">

                </div>
                <div id="divInputComment" class="mt-1 text-justify" style="display: none">
                    <label for="inputCommentModal"><small></small></label> <br>
                    <textarea class="form-control" name="inputCommentModal" id="inputCommentModal" placeholder="Add comment..." rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button type="button" class="btn solid royal-blue" id="sendButton" data-status="send-officer"
                        style="display: none">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    const modalInfoValidate = {
        "pending-ps-button": {
            "modalMessage": 'You are about to approve the application. Are you sure?',
            "incompleteMessage": 'Please validate the application entirely.',
            "successMessage": 'The application has been approved successfully.',
            "errorMessage": 'There was an error approving the application'
        },
        "complete-button": {
            "modalMessage": 'You are about to approve for complete the application. Are you sure?',
            "incompleteMessage": 'Please upload the signed document first, before you can proceed.',
            "successMessage": 'The application has been approved successfully.',
            "errorMessage": 'There was an error approving the application'
        },
        "decline-button": {
            "modalMessage": 'You are about to decline the application. Are you sure?',
            "successMessage": 'The application has been declined successfully.',
            "errorMessage": 'There was an error rejecting the application'
        },
        "additional-button": {
            "modalMessage": 'Please describe the information that is missing so the Land Registry Officer knows what he/she needs to add.',
            "successMessage": 'Additional Information has been saved successfully.',
            "errorMessage": 'There was an error saving the additional information'
        },
        "send-stamp-duty-button": {
            "modalMessage": 'Please provide a short comment for the stamp duty officer, stating why the application is incomplete.',
            "successMessage": 'Send back to the collector of Stamp Duty has been saved successfully.',
            "errorMessage": 'There was an error sending back to the collector of Stamp Duty'
        },
        "approve-ps-button": {
            "modalMessage": 'You are about to approve the application. Are you sure?',
            "successMessage": 'The application has been approved successfully.',
            "errorMessage": 'There was an error approving the application.'
        },
        "complete-transfer-button": {
            "modalMessage": 'You are about to complete the transfer information. Are you sure?',
            "successMessage": 'The application has been approved successfully.',
            "errorMessage": 'There was an error approving the application.'
        },
        "conflict-button": {
            "modalMessage": 'You are about to conflict the application. Are you sure?',
            "successMessage": 'The application has been send conflict successfully.',
            "errorMessage": 'There was an error sending conflict the application.'
        },
        "send-back-conflict-button": {
            "modalMessage": 'You are about to send back the application and mark as conflict. Are you sure?',
            "successMessage": 'Send back to conflict has been saved successfully.',
            "errorMessage": 'There was an error sending back to conflict'
        },
    };
    
    let status = '';
    let landOfficerId = '';
    let comment = '';
    let officer = '';

    $('#landRegistryModal').on('show.bs.modal', function (event) {
        $("#divInputComment").hide();
        $("#sendButton").hide();
        let button = $(event.relatedTarget); // Button that triggered the modal
        status = button.data('status');
        landOfficerId = button.data('id');
        officer = button.data('officer');
        $('#message_modal').html(modalInfoValidate[status].modalMessage);
        if (status === "pending-ps-button"){
            $.ajax({
                type: 'POST',
                url: '/stamp-duty/'+ landOfficerId + '/validate',
                data: $("#submissionForm").serialize(),
                success: (response) => {
                    if (response.isCompleteValidated){

                        $('#message_modal').html(modalInfoValidate[status].modalMessage);
                        $("#divInputComment").show();
                        $("#sendButton").show();
                    }
                    else{
                        $('#message_modal').html(modalInfoValidate[status].incompleteMessage);
                    }

                },
                error: (err) => {
                    $('#landRegistryModal').modal('hide');
                    Swal.fire('Error', modalInfoValidate[status].errorMessage, 'error');
                },
            });
        }
        else if (status === "complete-button"){
            $.ajax({
                type: 'POST',
                url: '/ps-submissions/'+ landOfficerId + '/validate-remission',
                success: (response) => {
                    if (response.hasRemissionFiles){

                        $('#message_modal').html(modalInfoValidate[status].modalMessage);
                        $("#divInputComment").show();
                        $("#sendButton").show();
                    }
                    else{
                        $('#message_modal').html(modalInfoValidate[status].incompleteMessage);
                    }

                },
                error: (err) => {
                    $('#landRegistryModal').modal('hide');
                    Swal.fire('Error', modalInfoValidate[status].errorMessage, 'error');
                },
            });
        }
        else{
            $("#sendButton").show();
            $("#divInputComment").show();
        }
    });

    $('#landRegistryModal').on('hide.bs.modal', function (event) {
        $("#sendButton").hide();
        $("#divInputComment").hide();
        $('#message_modal').html('');
    });

    $('#sendButton').on('click', function () {
        comment = $('#inputCommentModal').val();
        $.ajax({
            type: 'POST',
            url: '/stamp-duty/'+ landOfficerId + '/update-status',
            data: { status: status, comment: comment, officer: officer },
            success: () => {
                $('#landRegistryModal').modal('hide');
                Swal.fire('Success', modalInfoValidate[status].successMessage, 'success').then(() => {
                    if (officer === "ps-officer"){
                        location.href = '/ps-submissions/dashboard';
                    }
                    else if (officer === "finance-officer"){
                        location.href = '/finance-officer/dashboard';
                    }
                    else{
                        location.href = '/stamp-duty/dashboard';
                    }

                });
            },
            error: (err) => {
                $('#landRegistryModal').modal('hide');
                Swal.fire('Error', modalInfoValidate[status].errorMessage, 'error');
            },
        });
    });
</script>
