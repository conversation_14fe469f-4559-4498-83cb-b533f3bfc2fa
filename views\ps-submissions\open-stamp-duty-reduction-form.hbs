<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submitForm" method="POST" autocomplete="off">
                            <div class="row mt-2">
                                <div class="col-md-12">
                                    <h2>Stamp Duty Reduction</h2>
                                    <br>
                                </div>
                            </div>
                            <hr>
                            <div class="row mt-2">
                                <div class="col-12">
                                    {{>client-forms/stamp-duty-exemption-log application=application}}
                                </div>
                            </div>
                            <hr>
                            {{>client-forms/stamp-duty-reduction-validate-form
                            application=application
                            islands=islands
                            calendar=calendar
                            validations=validations
                            filesInformation=filesInformation
                            disabledValidations=false}}
                            <hr class="bg-warning text-warning">
                            <h3>
                                Please fill in the remitted amount (USD)
                            </h3>
                            <div>
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <div class="form-group mb-0">
                                            <label for="remittedAmountControl">Remitted amount</label>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="input-group mb-3">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text" id="money-addon">USD</span>
                                            </div>
                                            <input type="number" name="remittedAmount" id="remittedAmountControl"
                                                class="form-control" min="0" value="{{application.remittedAmount}}"
                                                required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="d-flex flex-wrap justify-content-between mt-2">

                            <div class="flex-grow">
                                <button onclick="saveProgress()" type="button"
                                    class="btn btn-primary action-button width-xl">
                                    Save
                                </button>
                            </div>

                            <div class="flex-grow">
                                <button onclick="openReturnApplicationModal()" type="button"
                                    class="btn btn-danger action-button width-xl">
                                    Send back to Stamp duty
                                </button>
                            </div>
                            <div class="flex-grow">
                                <button onclick="openApproveApplicationModal()" type="button"
                                    class="btn btn-success action-button width-xl">
                                    Approve
                                </button>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-2">
                                <a href="javascript:history.back()" class="btn btn-secondary btn-block action-button">
                                    Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>modals/approve-modal}}
{{>modals/return-modal}}


<script type="text/javascript">
    function saveProgress() {
        $(".action-button").each(function () { $(this).prop('disabled', true) });
        const form = $('#submitForm').serializeArray();
        let serializedForm = {};
        for (let i = 0; i < form.length; i++) {
            const nameField = form[i]['name'];
            serializedForm[nameField] = form[i]['value'];
        }
        $.ajax({
            type: "POST",
            url: "./update",
            data: JSON.stringify(serializedForm),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.success) {
                    toastr["success"]('Your application was updated successfully', 'Application updated!');
                    window.location = './update'
                } else {
                    toastr["warning"](data.message, 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    }

    function openApproveApplicationModal() {
        let invalidChecks = false;
        let invalidRemittedAmount = false;
        $("input[type='checkbox']:visible").each(function () {
            const val = $('input:checkbox[name="' + this.name + '"]:checked').val();
            if (val === undefined) {
                $('input:checkbox[name="' + this.name + '"]').toggleClass("is-invalid", true);
                invalidChecks = true;
            } else {
                $('input:checkbox[name="' + this.name + '"]').toggleClass("is-invalid", false);
            }
        });
        if (!$('#remittedAmountControl').val() || $('#remittedAmountControl').val() == 0) {
            invalidRemittedAmount = true;
        }

        if (!invalidChecks && !invalidRemittedAmount) {
            $('#approve-modal').modal();
        } else {
            if (invalidRemittedAmount) {
                toastr["warning"]('Please provide a remitted amount for this application', 'Error!');
            }
            if (invalidChecks) {
                toastr["warning"]('Please validate all sections before approval', 'Error!');
            }

        }

    }

    function openReturnApplicationModal() {
        $('#return-modal').modal();
    }

    $("input[type='checkbox']").on('change', function () {
        const empty = $('input[name="' + this.name + '"]:checked').val() === "";
        $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
    });

    $('#submitReturn').click(function () {
        $.ajax({
            type: "POST",
            url: "./return",
            data: JSON.stringify({ internalComments: $('#returnModalComments').val() }),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    Swal.fire('Success', data.message || "Your application was updated successfully", 'success').then(() => {
                        location.href = '/ps-submissions/dashboard';
                    });
                } else {
                    toastr["warning"](data.message ? data.message : "There was an error updating the application...", 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    });

    $('#submitApprove').click(function () {
        $(".action-button").each(function () { $(this).prop('disabled', true) });
        const form = $('#submitForm').serializeArray();
        let serializedForm = {
            internalComments: $('#approvedModalComments').val()
        };
        for (let i = 0; i < form.length; i++) {
            const nameField = form[i]['name'];
            serializedForm[nameField] = form[i]['value'];
        }
        $.ajax({
            type: "POST",
            url: "./approve",
            data: JSON.stringify(serializedForm),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    Swal.fire('Success', data.message || "Your application was updated successfully", 'success').then(() => {
                        location.href = '/ps-submissions/dashboard';
                    });
                } else {
                    toastr["warning"](data.message ? data.message : "There was an error updating the application...", 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    });

</script>
