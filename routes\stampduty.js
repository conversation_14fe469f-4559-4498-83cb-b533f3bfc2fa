const express = require('express');
const router = express.Router();

const stampDutyController = require('../controllers/stampDutyController');
const uploadController = require('../controllers/uploadController');
const downloadController = require('../controllers/downloadController');

//PAGES
router.get('/dashboard', ensureAuthenticatedStampDuty, stampDutyController.getDashboard);
router.get('/dashboard/search', ensureAuthenticatedStampDuty, stampDutyController.superSearchDashboard);
router.post('/dashboard/search/', ensureAuthenticatedStampDuty, stampDutyController.superSearchDashboard);
router.get('/dashboard/status/:status', ensureAuthenticatedStampDuty, stampDutyController.stampDutyDashboard);
router.post('/dashboard/status/:status', ensureAuthenticatedStampDuty, stampDutyController.stampDutyDashboard);
router.get('/:stampDutyId/open', ensureAuthenticatedStampDuty, stampDutyController.getStampDutyView);
router.put('/:stampDutyId/update-auditor', ensureAuthenticatedStampDuty, stampDutyController.updateStampDutyAuditor);
router.get('/import-duty-waiver/:stampDutyId/open', ensureAuthenticatedStampDuty, stampDutyController.getImportDutyWaiverView);
router.post('/import-duty-waiver/:stampDutyId/update', ensureAuthenticatedStampDuty, stampDutyController.saveImportDutyWaiverApplication);
router.post('/:stampDutyId/update-status', ensureAuthenticatedStampDuty, stampDutyController.updateStatus);
router.post('/:stampDutyId/validate', ensureAuthenticatedStampDuty, stampDutyController.validateInformation);

// Stamp Duty Exemption routes
router.get('/exemption-application/:stampDutyId/update', ensureAuthenticatedStampDuty, stampDutyController.getStampDutyExemptionApplicationView);
router.post('/exemption-application/:stampDutyId/update', ensureAuthenticatedStampDuty, stampDutyController.saveStampDutyExemptionApplication);
router.post('/exemption-application/:stampDutyId/approve', ensureAuthenticatedStampDuty, stampDutyController.approveStampDutyExemptionApplication);
router.post('/exemption-application/:stampDutyId/complete', ensureAuthenticatedStampDuty, stampDutyController.completeStampDutyExemptionApplication);
router.post('/exemption-application/:stampDutyId/decline', ensureAuthenticatedStampDuty, stampDutyController.declineStampDutyExemptionApplication);
router.post('/exemption-application/:stampDutyId/conflict', ensureAuthenticatedStampDuty, stampDutyController.conflictStampDutyExemptionApplication);
router.post('/exemption-application/:stampDutyId/request-information', ensureAuthenticatedStampDuty, stampDutyController.requestInformationStampDutyExemptionApplication);

router.get('/exemption-application/:stampDutyId/uploaded-files', ensureAuthenticatedStampDuty, stampDutyController.getFilesStampDutyExemptionApplication);

router.post('/exemption-application/:stampDutyId/upload-documents/:fileGroup', ensureAuthenticatedStampDuty,
  uploadController.uploadFileStampDutyApplication.fields([{  name: 'fileUploaded',  maxCount: 5}]),
  stampDutyController.storeFilesStampDutyExemptionApplication
);

router.delete(
  '/exemption-application/:stampDutyId/delete-document', ensureAuthenticatedStampDuty,
  stampDutyController.deleteUploadStampDutyExemptionApplication
);

router.get(
  '/exemption-application/:stampDutyId/download-document/:group/:fileId',
  ensureAuthenticatedStampDuty,  downloadController.downloadStampDutyExemptionFile
);


// Stamp Duty Reduction routes
router.get('/reduction-application/:stampDutyId/update', ensureAuthenticatedStampDuty, stampDutyController.getReductionApplicationView);
router.post('/reduction-application/:stampDutyId/update', ensureAuthenticatedStampDuty, stampDutyController.saveReductionApplication);
router.post('/reduction-application/:stampDutyId/approve', ensureAuthenticatedStampDuty, stampDutyController.approveReductionApplication);
router.post('/reduction-application/:stampDutyId/complete', ensureAuthenticatedStampDuty, stampDutyController.completeReductionApplication);
router.post('/reduction-application/:stampDutyId/decline', ensureAuthenticatedStampDuty, stampDutyController.declineReductionApplication);
router.post('/reduction-application/:stampDutyId/conflict', ensureAuthenticatedStampDuty, stampDutyController.conflictReductionApplication);
router.post('/reduction-application/:stampDutyId/request-information', ensureAuthenticatedStampDuty, stampDutyController.requestInformationReductionApplication);

router.get('/reduction-application/:stampDutyId/uploaded-files', ensureAuthenticatedStampDuty, stampDutyController.getFilesReductionApplication);

router.post('/reduction-application/:stampDutyId/upload-documents/:fileGroup', ensureAuthenticatedStampDuty,
  uploadController.uploadFileStampDutyApplication.fields([{  name: 'fileUploaded',  maxCount: 5}]),
  stampDutyController.storeFilesReductionApplication
);

router.delete(
  '/reduction-application/:stampDutyId/delete-document', ensureAuthenticatedStampDuty,
  stampDutyController.deleteUploadReductionApplication
);

router.get(
  '/reduction-application/:stampDutyId/download-document/:group/:fileId',
  ensureAuthenticatedStampDuty,  downloadController.downloadReductionApplicationFile
);




function ensureAuthenticatedStampDuty(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isStampDuty || req.session.authentication.isPsOfficer) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}


module.exports = router;
