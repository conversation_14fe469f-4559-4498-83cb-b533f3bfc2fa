<div id="complete-modal" class="modal fade" tabindex="-1" role="dial" aria-hidden="true" style="display: none;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <div class="col-md-12 p-1">
                    <label for="completedModalComments">You are about to complete the application. Are you sure?</label>
                    <div class="form-group">
                        <textarea id="completedModalComments" class="form-control mt-2"
                        placeholder="Internal comments (will not be sent to the applicant)"
                            rows="5"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light waves-effect" data-dismiss="modal">Cancel
                </button>
                <button type="button" class="btn btn-primary waves-effect waves-light" id="submitComplete">Confirm</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div>

<script type="text/javascript">

    $('#complete-modal').on('shown.bs.modal', function () {
        $('#completedModalComments').trigger('focus');
    });

    $('#complete-modal').on('hidden.bs.modal', function () {
        $('#completedModalComments').val('');

    });

</script>