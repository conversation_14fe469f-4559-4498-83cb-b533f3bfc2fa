const mongoose = require('mongoose');

const cardHoldersSchema = new mongoose.Schema({
    cardnr: { type: String, required: false },
    firstname: { type: String, required: false },
    middlename: { type: String, required: false },
    lastname: { type: String, required: false },
    date_of_birth: { type: Date, required: false },
});

module.exports.cardHoldersModel = mongoose.model('statusCardHolders', cardHoldersSchema);