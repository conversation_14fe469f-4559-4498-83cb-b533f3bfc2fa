const createError = require('http-errors');
const express = require('express');
const path = require('path');
const logger = require('morgan');
const cookieParser = require('cookie-parser');
const expressSession = require('express-session');
const bodyParser = require('body-parser');
const fetch = require("node-fetch");
const redis = require("redis");
const redisStore = require('connect-redis')(expressSession);
const hbs = require('express-handlebars');
const handlebars = require('handlebars');
const { allowInsecurePrototypeAccess } = require('@handlebars/allow-prototype-access')
const moment = require('moment');
const config = require('./config');
const routes = require('./routes/index');
const landOfficerRouter = require('./routes/landofficer');
const stampDutyRouter = require('./routes/stampduty');
const psRouter = require('./routes/pssubmissions');
const financeRouter = require('./routes/financeofficer');
const customsRouter = require('./routes/customsofficer');
const auditorRouter = require('./routes/auditor');
const deputyCommissionerRouter = require('./routes/deputycommissioner');
const exportFilesRouter = require('./routes/exportfiles');
const declineNoticesRouter = require('./routes/decline-notices');
const homeOwnerPolicyRouter = require('./routes/home-owner-policy');
require('express-async-errors');

const mongoose = require('mongoose');
const mongoDB = process.env.MONGODB;
mongoose.connect(mongoDB, { useNewUrlParser: true, useUnifiedTopology: true });
mongoose.set('useFindAndModify', false);
const db = mongoose.connection;
db.on('error', console.error.bind(console, 'MongoDB connection error:'));

const app = express();

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'hbs');
app.engine('hbs', hbs.engine({
  extname: 'hbs',
  handlebars: allowInsecurePrototypeAccess(handlebars),
  defaultView: 'default',
  layoutsDir: __dirname + '/views/layouts/',
  partialsDir: __dirname + '/views/partials/',
  helpers: {
    formatLocalDate: function (date, format) {
      if (date) {
        // SET TCI TIME UTC-4
        return moment.utc(date).subtract(4, 'h').format(format);
      } else {
        return '';
      }
    },
    formatDate: function (date, format) {
      if (date) {
        return moment.utc(date).format(format);
      } else {
        return '';
      }
    },
    formatNumber: function (number) {
      if (number || number === 0) {
        return number.toString().replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",")
      } else {
        return '';
      }
    },
    convertInformationNames: function (informationNames) {
      if (informationNames) {
        const requestNames = {
          affidavit: "Affidavit",
          marriageCertificates: "Marriage Certificates",
          statusCard: "Status Card",
          birthCertificate: "Birth Certificate",
          governmentIssuedIdentification: "Government issued identification",
          parentsDocuments: "Documentation Parents",
          itemsInvoices: "Invoices",
          buildingPictures: "Photographs building or site",
          buildingPermit: "Building permit",
          landRegister: "Land Register extract",
          valuation: "Valuation report",
          botcCertificate: "BOTC Certificate",
          personalDetails: "Personal details",
          applicantDetails: "Applicant details",
          additionalApplicants: "Additional Applicants",
          propertyDetails: "Property details",
          companyDetails: "Company details",
          citizenshipDetails: "Citizenship details",
        }
        informationNames = informationNames.map(iname => requestNames[iname]);

        return informationNames.join(', ');
      } else {
        return '';
      }
    },
    ifEquals: function (str1, str2, options) {
      return str1 === str2 ? options.fn(this) : options.inverse(this);
    },
    ifNotIn: function (element, list) {
      return list.indexOf(element) === -1;
    },
    ifCond: function (v1, operator, v2, options) {
      switch (operator) {
        case '==':
          return (v1 == v2) ? options.fn(this) : options.inverse(this);
        case '===':
          return (v1 === v2) ? options.fn(this) : options.inverse(this);
        case '!=':
          return (v1 != v2) ? options.fn(this) : options.inverse(this);
        case '!==':
          return (v1 !== v2) ? options.fn(this) : options.inverse(this);
        case '<':
          return (v1 < v2) ? options.fn(this) : options.inverse(this);
        case '<=':
          return (v1 <= v2) ? options.fn(this) : options.inverse(this);
        case '>':
          return (v1 > v2) ? options.fn(this) : options.inverse(this);
        case '>=':
          return (v1 >= v2) ? options.fn(this) : options.inverse(this);
        case '&&':
          return (v1 && v2) ? options.fn(this) : options.inverse(this);
        case '||':
          return (v1 || v2) ? options.fn(this) : options.inverse(this);
        default:
          return options.inverse(this);
      }
    },
    concat: function () {
      let concatValue = "";
      for (let i = 0; i < arguments.length - 1; ++i) {
        //Do your thing with each array element.
        if (typeof (arguments[i]) === "string" || typeof (arguments[i]) === "number") {
          concatValue = concatValue + arguments[i].toString();
        }
      }
      //Return your results...
      return concatValue;
    },
    ternary: function (condition, v1, v2) {
      if (condition) {
        return v1;
      } else {
        return v2;
      }
    },
  },
})
);

// uncomment after placing your favicon in /public
//app.use(favicon(path.join(__dirname, 'public', 'favicon.ico')));
app.use(logger('dev'));
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true, parameterLimit: 50000 }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));

const redisClient = redis.createClient(6380, process.env.REDIS_HOST,
  { auth_pass: process.env.REDIS_PASS, tls: { servername: process.env.REDIS_HOST } });

app.use(expressSession({
  secret: 'keyboard cat',
  resave: true,
  saveUninitialized: false,
  store: new redisStore({ client: redisClient }),
  cookie: {
    maxAge: 7200000,  //120 mins
    secure: false,
    httpOnly: true
  },
}));

app.use('/', routes);
app.use('/land-officer', landOfficerRouter);
app.use('/stamp-duty', stampDutyRouter);
app.use('/auditor', auditorRouter);
app.use('/ps-submissions', psRouter);
app.use('/finance-officer', financeRouter);
app.use('/customs-officer', customsRouter);
app.use('/deputy-commissioner', deputyCommissionerRouter);
app.use('/export-files', exportFilesRouter);
app.use('/home-owner-policy', homeOwnerPolicyRouter);
app.use('/decline-notices', declineNoticesRouter);

app.set('trust proxy', 1);
app.disable('x-powered-by');


app.get('/login',
  async function (req, res) {
    const token = req.headers["x-ms-token-aad-access-token"];

    // const token = config.appServiceAuthSesion;
    console.log("login");
    if (process.env.NODE_ENV === "dev") {

      req.session.user = {
        access_token: '',
        username: "<EMAIL>",
        name: "TCI Demo",
      };

      req.session.authentication = {
        isLandOfficer: true,
        isStampDuty: true,
        isPsOfficer: true,
        isFinance: true,
        isCustoms: true,
        isAuditor: true,
        isDeputyCommissioner: true,
        isHomeOwnerDownloadDocuments: true,
      };
      if (req.session.redirectUrl) {
        const url = req.session.redirectUrl;
        req.session.redirectUrl = null;
        res.redirect(url);
      } else {
        res.redirect('/');
      }
    }
    else {
      if (!token) {
        return res.redirect('/not-authorized');
      }
      
      const userResponse = await getUser(token);

      const responseAad = await getSecurityGroups(token);

      if (!responseAad && !req.params.afterRefresh) {
        return res.redirect('/not-authorized');
      }

      const securityGroups = responseAad.value;
      
      if (!securityGroups || !securityGroups.length || !userResponse) {
        return res.redirect('/not-authorized');
      } else {
        req.session.user = {
            username: userResponse.userPrincipalName,
            name: userResponse.displayName,
        };
      }

      if (securityGroups) {          
        req.session.authentication = {
          isLandOfficer: securityGroups.findIndex((group) => group === config.securityGroupTierSecSdeaLandRegistryOfficers) > -1,
          isStampDuty: securityGroups.findIndex((group) => group === config.securityGroupTierSecSdeaStampDutyOfficers) > -1,
          isPsOfficer: securityGroups.findIndex((group) => group === config.securityGroupTierSecSdeaPrimeSecretary) > -1,
          isFinance: securityGroups.findIndex((group) => group === config.securityGroupTierSecSdeaFinance) > -1,
          isCustoms: securityGroups.findIndex((group) => group === config.securityGroupTierSecSdeaCustoms) > -1,
          isAuditor: securityGroups.findIndex((group) => group === config.securityGroupTierSecSdeaAuditor) > -1,
          isDeputyCommissioner: securityGroups.findIndex((group) => group === config.securityGroupTierSecSdeaDeputyCommissioner) > -1,
          isHomeOwnerDownloadDocuments: securityGroups.findIndex((group) => group === config.securityGroupHomeOwnerPolicyDownloadDocuments) > -1,
        };
        
        if (req.session.redirectUrl) {
          const url = req.session.redirectUrl;
          req.session.redirectUrl = null;
          res.redirect(url);
        } else {
          res.redirect('/');
        }
      } else {
        res.redirect('/not-authorized');

      }
    }

  });

app.get('/logout', function (req, res) {
  req.session.destroy(function () {
    res.redirect(process.env.AZURE_APP_URL + "/.auth/logout");
  });
});

// 'logout' route, logout from passport, and destroy the session with AAD.
app.get('/not-authorized', function (req, res) {
  res.render('not-authorized', { message: "You are not authorized" });
});

app.get('/session-expired', function (req, res) {    
    res.render('session-expired', { url: process.env.AZURE_AD_ADMIN_CONSENT_URL });
});

app.get('/refresh-token', async function (req, res) {
    const sessionToken = req.cookies.AppServiceAuthSession;
    if(await refreshToken(sessionToken) == false) {
        res.redirect('/session-expired');
    } else {
        res.redirect('/login?afterRefresh=true');
    }
});

// error handlers

// development error handler
// will print stacktrace
if (app.get('env') === 'development') {
  app.use(function (err, req, res) {
    res.status(err.status || 500);
    res.render('error', {
      message: err.message,
      error: err
    });
  });
}

// catch 404 and forward to error handler
app.use(function (req, res, next) {
    next(createError(404));
});

// error handler
// eslint-disable-next-line no-unused-vars
app.use(function (err, req, res) {
    // set locals, only providing error in development
    if (process.env.DEVELOPMENT_ERRORS === 'true') {
        res.locals.message = err.message;
        res.locals.error = req.app.get('env') === 'development' ? err : {};
    } else {
        res.locals.message = 'An error ocurred!';
        res.locals.error = {};
    }


    // render the error page
    res.status(err.status || 500);
    res.render('error');
});

async function getSecurityGroups(token) {    
    try {

        let todo = {
            securityEnabledOnly: true
        };

        const options = {
            'method': 'POST',
            'body': JSON.stringify(todo),
            'headers': {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            }
        }

        const response = await fetch(process.env.AZURE_GRAPH_API_URL + '/me/getMemberObjects', options);
        if (!response.status || response.status !== 200) {
            return null;
        }
        
        return await response.json();
    } catch (e) {
        console.log(e);
    }
}

async function getUser(token) {
    try {

        const options = {
            'method': 'GET',
            'headers': {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + token
            }
        };
        const response = await fetch(process.env.AZURE_GRAPH_API_URL + '/me', options);

        if (!response.status || response.status !== 200) {
            return null;
        }
        
        return await response.json();
    } catch (e) {
        console.log(e);
    }
}

async function refreshToken(token) {
    try {

        const options = {
            'method': 'GET',
            'headers': {
                'Content-Type': 'application/json',
                'Cookie': 'AppServiceAuthSession=' + token
            }
        };
        const response = await fetch(process.env.AZURE_APP_URL + "/.auth/refresh", options);

        if (!response.status || response.status !== 200) {
            return false;
        }
        return true;
    } catch (e) {
        console.log(e);
    }
}

module.exports = app;

