<div class="row mt-2">
    <div class="col-md-11">
        <div class="form-group mb-3">
            <label class="mb-2" for="documentTypeStatusCard">
                Applicant is a Turks & Caicos Islander Status Holder
            </label>
        </div>
    </div>
    <div class="col-md-1">
        <div class="form-group mb-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="documentTypeStatusCard" name="documentType"
                        required value="status-card" {{#ifEquals application.documentType 'status-card'
                        }}checked{{/ifEquals}} disabled />
                    <label class="custom-control-label" for="documentTypeStatusCard"></label>
                </div>
            </div>
        </div>
    </div>
</div>
{{#ifEquals application.documentType 'status-card' }}
<!-- STATUS CARD NUMBER -->
<div class="row mt-2" id="statusCardSection">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="statusCardNumber">Status Card Number<i
                    class="fa fa-info-circle fa-lg ml-1 {{statusInformationColor}}" data-toggle="tooltip"
                    data-trigger="hover" data-placement="top" data-html="true" data-title="
                    {{#if foundCardHolder}}
                    <u>Match in database</u><br>
                    First name<span class='{{#if foundCardHolder.matchFirstName}}text-green{{else}}text-red{{/if}}'>&nbsp;{{foundCardHolder.firstname}}</span><br>
                    Last name<span class='{{#if foundCardHolder.matchLastName}}text-green{{else}}text-red{{/if}}'>&nbsp;{{foundCardHolder.lastname}}</span><br>
                    Date of birth<span class='{{#if foundCardHolder.matchDateOfBirth}}text-green{{else}}text-red{{/if}}'>&nbsp;{{formatDate foundCardHolder.date_of_birth 'DD/MM/YYYY'}}</span><br>
                    {{else}}
                    <span class='text-red'>Status Card Number not found</span>
                    {{/if}}
                    "></i></label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input name="statusCardNumber" id="statusCardNumber" class="form-control" type="text" maxlength="11"
                value="{{application.statusCardNumber}}" disabled required>
        </div>
    </div>
</div>
{{/ifEquals}}
<div class="row mt-2">
    <div class="col-md-11">
        <div class="form-group mb-3">
            <label class="mb-2" for="documentTypeBotc">
                Applicant is a British Overseas Territories Citizen
            </label>
        </div>
    </div>
    <div class="col-md-1">
        <div class="form-group mb-3">
            <div class="col-12 d-flex justify-content-end">
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="documentTypeBotc" name="documentType" required
                        value="botc" {{#ifEquals application.documentType 'botc' }}checked{{/ifEquals}} disabled />
                    <label class="custom-control-label" for="documentTypeBotc"></label>
                </div>
            </div>
        </div>
    </div>
</div>