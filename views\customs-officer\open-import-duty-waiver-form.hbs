<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submitForm" method="POST" autocomplete="off">
                            <div class="row mt-2">
                                <div class="col-md-12">
                                    <h2>Home Owner Policy</h2>
                                    <br>
                                </div>
                            </div>
                            <hr>
                            <div class="row mt-2">
                                <div class="col-12">
                                    {{>client-forms/import-duty-waiver-log application=application}}
                                </div>
                            </div>
                            <hr>
                            {{>client-forms/import-duty-waiver-validate-form
                            application=application
                            islands=islands
                            calendar=calendar
                            applicants=applicants
                            validations=validations
                            filesInformation=filesInformation
                            disabledValidations=false}}
                            <hr class="bg-warning text-warning">
                            {{>client-forms/applicant-totals applicants=applicants}}
                            <h3>
                                Please fill in the following amounts (USD)
                            </h3>
                            <div>
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <div class="form-group mb-0">
                                            <label for="remittedAmountControl">Revenue forgone</label>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="input-group mb-3">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text" id="money-addon">USD</span>
                                            </div>
                                            <input type="number" name="remittedAmount" id="remittedAmountControl"
                                                class="form-control" min="0" value="{{application.remittedAmount}}"
                                                required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-4">
                                        <div class="form-group mb-0">
                                            <label for="valueOfGoodsControl">Value of goods</label>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="input-group mb-3">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text" id="money-addon">USD</span>
                                            </div>
                                            <input type="number" name="valueOfGoods" id="valueOfGoodsControl"
                                                class="form-control" min="0" value="{{application.valueOfGoods}}"
                                                required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="d-flex flex-wrap justify-content-between mt-2">
                            <div class="flex-grow">
                                <button onclick="openAdditionalInformationModal()" type="button"
                                    class="btn btn-info action-button width-xl">
                                    Request information
                                </button>
                            </div>
                            <div class="flex-grow">
                                <button onclick="saveProgress()" type="button"
                                    class="btn btn-primary action-button width-xl">
                                    Save
                                </button>
                            </div>
                            <div class="flex-grow">
                                <button onclick="openConflictApplicationModal()" type="button"
                                    class="btn btn-warning action-button width-xl">
                                    Conflict
                                </button>
                            </div>
                            <div class="flex-grow">
                                <button onclick="openDeclineApplicationModal()" type="button"
                                    class="btn btn-danger action-button width-xl">
                                    Decline
                                </button>
                            </div>
                            <div class="flex-grow">
                                <button onclick="openApproveApplicationModal()" type="button"
                                    class="btn btn-success action-button width-xl">
                                    Approve
                                </button>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-2">
                                <button onclick="window.location='/customs-officer/dashboard'" type="button"
                                    class="btn btn-secondary btn-block action-button">
                                    Back
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

{{>modals/additional-information application=application type='import-duty-waiver'}}
{{>modals/decline-modal}}
{{>modals/approve-modal}}
{{>modals/conflict-modal}}
<script type="text/javascript">
    function saveProgress() {
        $(".action-button").each(function () { $(this).prop('disabled', true) });
        const form = $('#submitForm').serializeArray();
        let serializedForm = {};
        for (let i = 0; i < form.length; i++) {
            const nameField = form[i]['name'];
            serializedForm[nameField] = form[i]['value'];
        }
        $.ajax({
            type: "POST",
            url: "./update",
            data: JSON.stringify(serializedForm),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.success) {
                    toastr["success"]('Your application was updated successfully', 'Application updated!');
                    window.location = './update'
                } else {
                    toastr["warning"](data.message, 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    }

    function openApproveApplicationModal() {
        let invalidChecks = false;
        let invalidRemittedAmount = false;
        let invalidValueOfGoods = false;
        $("input[type='checkbox']:visible").each(function () {
            const val = $('input:checkbox[name="' + this.name + '"]:checked').val();
            if (val === undefined) {
                $('input:checkbox[name="' + this.name + '"]').toggleClass("is-invalid", true);
                invalidChecks = true;
            } else {
                $('input:checkbox[name="' + this.name + '"]').toggleClass("is-invalid", false);
            }
        });
        if (!$('#remittedAmountControl').val() || $('#remittedAmountControl').val() == 0) {
            invalidRemittedAmount = true;
        }
        if (!$('#valueOfGoodsControl').val() || $('#valueOfGoodsControl').val() == 0) {
            invalidValueOfGoods = true;
        }

        if (!invalidChecks && !invalidRemittedAmount) {
            $('#approve-modal').modal();
        } else {
            if (invalidRemittedAmount) {
                toastr["warning"]('Please provide the revenue forgone for this application', 'Error!');
            }
            if (invalidValueOfGoods) {
                toastr["warning"]('Please provide the value of goods for this application', 'Error!');
            }
            if (invalidChecks) {
                toastr["warning"]('Please validate all sections before approval', 'Error!');
            }

        }

    }

    function openAdditionalInformationModal() {
        $('#additional-information-modal').modal();
    }

    function openDeclineApplicationModal() {
        $('#decline-modal').modal();
    }

    function openConflictApplicationModal() {
        $('#conflict-modal').modal();
    }

    $("input[type='checkbox']").on('change', function () {
        const empty = $('input[name="' + this.name + '"]:checked').val() === "";
        $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
    });

    $('#submitDecline').click(function () {
        if (!($('input[name="declineReason"]:checked').val())) {
            $('#declineReasonError').show();
        } else {
            if (($('input[name="declineReason"]:checked').val() === 'Other' && !$("#decline-reason-other-text").val())) {
                $('#declineReasonError').show();
            }
            else {
                $('#declineReasonError').hide();
                declineApplication();
            }
        }
    });
    
    $('#submitConflict').click(function () {
        $.ajax({
            type: "POST",
            url: "./conflict",
            data: JSON.stringify({internalComments: $('#conflictModalComments').val()}),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.success) {
                    toastr["success"]('The application was marked as conflict successfully', 'Application updated!');
                    window.location = '/customs-officer/dashboard'
                } else {
                    toastr["warning"](data.message, 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            }
        });
    });

    $('#submitApprove').click(function () {
        $(".action-button").each(function () { $(this).prop('disabled', true) });
        const form = $('#submitForm').serializeArray();
        let serializedForm = {
            internalComments: $('#approvedModalComments').val()
        };
        for (let i = 0; i < form.length; i++) {
            const nameField = form[i]['name'];
            serializedForm[nameField] = form[i]['value'];
        }
        $.ajax({
            type: "POST",
            url: "./approve",
            data: JSON.stringify(serializedForm),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.success) {
                    toastr["success"]('The application was approved successfully', 'Application approved!');
                    window.location = '/customs-officer/dashboard'
                } else {
                    toastr["warning"](data.message, 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    });

    function declineApplication() {
        let declineReason = '';

        declineReason = $('input[name="declineReason"]:checked').val();
        if (declineReason === 'Other') {
            declineReason = $('#decline-reason-other-text').val();
        }
        $.ajax({
            type: "POST",
            url: "./decline",
            data: JSON.stringify({
                declineReason,
                internalComments: $('#declinedModalComments').val()
            }),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.success) {
                    toastr["success"]('The application was declined successfully', 'Application declined!');
                    window.location = '/customs-officer/dashboard'
                } else {
                    toastr["warning"](data.message, 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            }
        });
    }
</script>
