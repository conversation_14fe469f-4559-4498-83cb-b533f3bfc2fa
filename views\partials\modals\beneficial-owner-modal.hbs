<div class="modal fade" id="bo-{{beneficialOwner._id}}-modal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-bo-title" class="modal-title">Applicant</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-bo-body" class="modal-body p-3">
                <form action="" class="form" id="beneficialOwnerForm" style="border: none;padding: 5px;">
                    <!-- GENDER -->
                    <div class="row mt-2">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="genderBo">
                                    Gender*
                                </label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <div class="col-12 d-flex justify-content-end">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input type="radio" class="custom-control-input"
                                                    id="genderMaleBo" name="genderBo" required
                                                    value="Male" {{#ifEquals beneficialOwner.gender 'Male' }}checked{{/ifEquals}} disabled />
                                                <label class="custom-control-label"
                                                    for="genderMaleBo">Male</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group mb-3">
                                        <div class="col-12 d-flex justify-content-end">
                                            <div class="custom-control custom-radio custom-control-inline">
                                                <input type="radio" class="custom-control-input"
                                                    id="genderFemaleBo" name="genderBo" required
                                                    value="Female" {{#ifEquals beneficialOwner.gender 'Female'
                                                    }}checked{{/ifEquals}} disabled />
                                                <label class="custom-control-label"
                                                    for="genderFemaleBo">Female</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- FIRST NAME -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="firstNameApplicant">First
                                    Name</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="text" name="firstNameBo" id="firstNameBo"
                                    class="form-control" value="{{beneficialOwner.firstName}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- LAST NAME -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="lastNameBo">Last
                                    Name</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="text" name="lastNameBo" id="lastNameBo" class="form-control"
                                    value="{{beneficialOwner.lastName}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- DATE OF BIRTH -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-0">
                                <label for="dateOfBirthDayControlBo">Date
                                    of
                                    Birth</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="date" name="dateOfBirthBo" id="dateOfBirthBo"
                                    class="form-control" value="{{formatDate beneficialOwner.dateOfBirth "YYYY-MM-DD"}}"
                                    disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- ADDRESS -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="addressBo">Address</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="text" name="addressBo" id="addressBo" class="form-control"
                                    value="{{beneficialOwner.address}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- ADDRESS OPTIONAL -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="addressOptionalBo">Address
                                    (optional)</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input type="text" name="addressOptionalBo" id="addressOptionalBo"
                                    value="{{beneficialOwner.addressOptional}}" disabled class="form-control">
                            </div>
                        </div>
                    </div>
                    <!-- ISLAND -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="islandBo">Island</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <select class="custom-select" id="islandBo" name="islandBo"
                                    aria-required="true" disabled required>
                                    {{#each islands}}
                                    <option {{#ifEquals ../beneficialOwner.island name}} selected{{/ifEquals}}>{{name}}
                                    </option>
                                    {{/each}}
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- PHONE -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="phoneBo">Phone
                                    number</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3 w-100">
                                <input type="text" id="phoneBo" class="form-control" name="phoneBo"
                                    value="{{beneficialOwner.phone}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- EMAIL -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="emailBo">E-mail</label>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <input name="emailBo" id="emailBo" class="form-control" type="email"
                                    value="{{beneficialOwner.email}}" disabled required>
                            </div>
                        </div>
                    </div>
                    <!-- CONSENT -->

                    <div class="row">
                        <div class="col-9">
                            <h4>Consent</h4>
                            <h5 class="mt-1">Files:</h5>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12">
                            <ul style="list-style: disc;" class="ml-3">
                                {{#each beneficialOwner.consent}}
                                <li>
                                    <a href="/home-owner-policy/stamp-duty-reduction/{{../application._id}}/download/consent-{{../beneficialOwner._id}}/{{fileId}}"
                                        target="_blank">{{originalName}}</a>
                                </li>
                                {{/each}}
                            </ul>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    for (i = new Date().getFullYear(); i > 1900; i--) {
        $('#dateOfBirthYearControlBo').append($('<option />').val(i).html(i));
    }

</script>
