const express = require('express');
const router = express.Router();
const financeOfficerController = require('../controllers/financeOfficerController');

//PAGES
router.get('/dashboard', ensureAuthenticated, financeOfficerController.getDashboard);
router.post('/dashboard', ensureAuthenticated, financeOfficerController.getDashboard);
router.get('/exemptions/:id/update', ensureAuthenticated, financeOfficerController.getFinanceOfficerView);
router.post('/exemptions/:id/update', ensureAuthenticated, financeOfficerController.updateFinanceOfficer);


function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isFinance) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;
