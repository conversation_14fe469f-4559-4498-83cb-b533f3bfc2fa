const express = require('express');
const router = express.Router();

const importDutyWaiverController = require('../controllers/home-owner-policy/importDutyWaiverController');
const stampDutyExemptionController = require('../controllers/home-owner-policy/stampDutyExemptionController');
const downloadController = require('../controllers/downloadController');

//PAGES
router.get('/stamp-duty-exemption/:id/files', ensureAuthenticated, stampDutyExemptionController.getApplicationFiles);
router.get('/import-duty-waiver/:id/files', ensureAuthenticated, importDutyWaiverController.getApplicationFiles);
router.get('/stamp-duty-reduction/:id/files', ensureAuthenticated, importDutyWaiverController.getApplicationFiles);

// GET DOWNLOAD STANDARD FILES
router.get("/stamp-duty-exemption/:applicationId/download/:fileGroup/:fileId", ensureAuthenticatedDownloadExemption, downloadController.downloadStampDutyExemptionFile);
router.get("/import-duty-waiver/:applicationId/download/:fileGroup/:fileId", ensureAuthenticatedDownloadWaiver, downloadController.downloadImportDutyWaiverFile);
router.get("/stamp-duty-reduction/:applicationId/download/:fileGroup/:fileId", ensureAuthenticatedDownloadExemption, downloadController.downloadReductionApplicationFile);

function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isHomeOwnerDownloadDocuments) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        req.session.redirectUrl = '/home-owner-policy' + req.url;
        res.redirect('/login');
    }
}

function ensureAuthenticatedDownloadExemption(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isStampDuty ||
            req.session.authentication.isPsOfficer ||
            req.session.authentication.isAuditor ||
            req.session.authentication.isHomeOwnerDownloadDocuments
            ) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        req.session.redirectUrl = '/home-owner-policy' + req.url;
        res.redirect('/login');
    }
}

function ensureAuthenticatedDownloadWaiver(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isFinance ||
            req.session.authentication.isCustoms || 
            req.session.authentication.isAuditor ||
            req.session.authentication.isDeputyCommissioner ||
            req.session.authentication.isHomeOwnerDownloadDocuments) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        req.session.redirectUrl = '/home-owner-policy' + req.url;
        res.redirect('/login');
    }
}


module.exports = router;
