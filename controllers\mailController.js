const sgMail = require('@sendgrid/mail');

function validateEmailConfig() {
  if (!process.env.SENDGRID_API_KEY) {
    throw new Error('SendGrid API key is missing. Please check your environment variables.');
  }

  if (!process.env.EMAIL_SENDER) {
    throw new Error('Email sender address is missing. Please check your environment variables.');
  }

  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
}

function validateEmailParams(to, subject) {
  if (!to || (Array.isArray(to) && to.length === 0)) {
    throw new Error('Recipient email address is required');
  }

  if (!subject) {
    throw new Error('Email subject is required');
  }
}

exports.send = function (to, subject, text, html) {
  try {
    validateEmailConfig();
    validateEmailParams(to, subject);

    const mailOptions = {
      from: process.env.EMAIL_SENDER,
      to: to,
      subject: subject,
      text: text || 'No text content provided',
      html: html || '<p>No HTML content provided</p>'
    };

    // Send mail and handle response
    return sgMail.send(mailOptions)
      .then(response => {
        return response;
      })
      .catch(error => {
        console.error('Error sending email:', error);
        if (error.response) {
          console.error('SendGrid API error:', error.response.body);
        }
        throw error;
      });
  } catch (error) {
    console.error('Error in send function:', error.message);
    throw error;
  }
};

exports.asyncSend = async function (to, subject, text, html, pdf, pdfName) {
  try {
    validateEmailConfig();
    validateEmailParams(to, subject);

    const sendMultiple = Array.isArray(to) && to.length > 1;

    // Create mail options
    const mailOptions = {
      from: process.env.EMAIL_SENDER,
      to: to,
      subject: subject,
      text: text || 'No text content provided',
      html: html || '<p>No HTML content provided</p>'
    };

    // Add PDF attachment if provided
    if (pdf) {
      if (!Buffer.isBuffer(pdf) && typeof pdf !== 'string') {
        throw new Error('PDF content must be a Buffer or a string');
      }

      mailOptions.attachments = [{
        content: pdf,
        filename: pdfName || 'SUBMISSION.pdf',
        type: "application/pdf",
        disposition: "attachment",
        contentId: 'attachment-pdf'
      }];
    }

    // Send mail and handle response
    try {
      const response = await sgMail.send(mailOptions, sendMultiple);

      return response;
    } catch (sendError) {
      // Log detailed error information
      console.error('Error sending email:', sendError.message);

      if (sendError.response) {
        const { statusCode, body } = sendError.response;
        console.error(`SendGrid API error (${statusCode}):`, body);
        if (body.errors && body.errors.length > 0) {
          sendError.message = body.errors[0].message;
        } else {
          sendError.message = `SendGrid API error (${statusCode}): ${JSON.stringify(body)}`;
        }

      }

      throw sendError;
    }
  } catch (error) {
    console.error('Error in asyncSend function:', error.message);
    throw error;
  }
};
