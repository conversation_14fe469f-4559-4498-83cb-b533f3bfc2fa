<h3>Company Details:</h3>
<br>
<!-- COMPANY NAME -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="companyName">Company Name</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="companyFirm" id="companyFirm" class="form-control"
                value="{{application.companyDetails.name}}" disabled>
        </div>
    </div>
</div>
<!-- ADDRESS -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="companyAddress">Address</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="companyAddress" id="companyAddress" class="form-control"
                value="{{application.companyDetails.address}}" disabled required>
        </div>
    </div>
</div>
<!-- ADDRESS OPTIONAL -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="companyAddressOptional">Address
                (optional)</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="companyAddressOptional" id="companyAddressOptional" class="form-control"
                value="{{application.companyDetails.addressOptional}}" disabled>
        </div>
    </div>
</div>
<!-- ISLAND -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="companyIsland">Island</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <select class="custom-select" id="companyIsland" name="companyIsland" disabled required>

                {{#each islands}}
                <option {{#ifEquals ../application.companyDetails.island name}} selected{{/ifEquals}}>
                    {{name}}</option>
                {{/each}}
            </select>
        </div>
    </div>
</div>
<!-- PHONE -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="companyPhone">Phone
                number</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" id="companyPhone" class="form-control" name="companyPhone"
                value="{{application.companyDetails.phone}}" disabled required>
        </div>
    </div>
</div>
<!-- REGISTRATION NUMBER -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="companyRegistrationNumber">Company registration number'</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="companyRegistrationNumber" id="companyRegistrationNumber" class="form-control"
                value="{{application.companyDetails.registrationNumber}}" disabled required>
        </div>
    </div>
</div>
<!-- EMAIL -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="companyEmail">E-mail</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input name="companyEmail" id="companyEmail" class="form-control" type="email"
                value="{{application.companyDetails.email}}" disabled required>
        </div>
    </div>
</div>