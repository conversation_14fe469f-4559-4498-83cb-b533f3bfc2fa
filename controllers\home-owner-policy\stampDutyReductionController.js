const ReductionApplicationModel = require('../../models/stampDutyReductionApplication').ReductionApplicationModel;


exports.getApplicationFiles = async function (req, res) {
    try {
        const reductionApplication = await ReductionApplicationModel.findOne({ _id: req.params.id });
        if (!reductionApplication) {
            return res.status(404).end();
        }
        const filesInformation = {
            affidavit: { name: "Affidavit", files: reductionApplication.affidavit, filesCount: reductionApplication.affidavit.length },
            marriageCertificates: { name: "Marriage Certificates", files: reductionApplication.marriageCertificates, filesCount: reductionApplication.marriageCertificates.length },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: reductionApplication.birthCertificate, filesCount: reductionApplication.birthCertificate.length },
            parentsDocuments: { name: "<PERSON><PERSON>’s documents", files: reductionApplication.parentsDocuments, filesCount: reductionApplication.parentsDocuments.length },
            signedAgreement: { name: "Signed agreement of the purchase of the property", files: reductionApplication.signedAgreement, filesCount: reductionApplication.signedAgreement.length },
            landRegister: { name: "A certified copy of the Land Register Extract in respect of the property", files: reductionApplication.landRegister, filesCount: reductionApplication.landRegister.length },
            valuation: { name: "Valuation", files: reductionApplication.valuation, filesCount: reductionApplication.valuation.length },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: reductionApplication.statusCard, filesCount: reductionApplication.statusCard.length },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: reductionApplication.botcCertificate, filesCount: reductionApplication.botcCertificate.length },
        }
        return res.render('home-owner-policy/file-list', { application: reductionApplication, filesInformation, formType: 'stamp-duty-reduction' });
    } catch (error) {
        console.log("error ", error);
        return res.status(500).end();
    }
};
