<div class="card">
    <div class="card-body">
        <h4 class="justify-content-md-left" style="color:#f2aa0f; font: 200% sans-serif;">
            {{landOfficer.exemptionType}}
        </h4>
        <br>
        <h4><b><i>Comments:</i></b></h4>
        {{#each landOfficer.comments}}
            <div class="row">
                <div class="col-12">
                    <span>({{#formatDate date "DD-MM-YYYY" }}{{/formatDate}})</span>
                    <span class="font-weight-bold">{{  email }} - </span>
                    <span> {{commentStatus}}: </span>
                    <span>{{ comment }}</span>
                </div>
            </div>
        {{/each}}
        <br> <br>

        {{#ifEquals landOfficer.type 'LAND SUBMISSION'}}
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="instrument-number">Instrument Number:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" value="{{landOfficer.instrumentNumber }}" name="instrumentNumber"
                               id="instrument-number" class="number form-control" readonly>
                    </div>
                </div>
            </div>
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transferorName">Transferor:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" value="{{landOfficer.transferorName }}" name="transferorName"
                               id="transferorName"
                               class="form-control" readonly>
                    </div>
                </div>
            </div>
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transfereeName">Transferee:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" value="{{landOfficer.transfereeName }}" name="transfereeName"
                               id="transfereeName"
                               class="form-control" readonly>
                    </div>
                </div>
            </div>

        {{else}}
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transferorName">Applicant:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" value="{{landOfficer.transferorName }}" name="transferorName"
                               id="transferorName"
                               class="form-control" readonly>
                    </div>
                </div>
            </div>
        {{/ifEquals}}


        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="parcel-number">Parcel
                        #:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <input type="text" value="{{landOfficer.parcelNumber }}" name="parcelNumber" id="parcel-number"
                           class="form-control" readonly>
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="district">District:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <input type="text" value="{{landOfficer.district }}" name="district" id="district"
                           class="form-control" readonly>
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="island" id="island-label">Island
                    </label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <select class="custom-select" id="island" name="island" aria-labelledby="island-label" disabled>
                        <option value="">Select</option>
                        <option>Grand Turk</option>
                        <option>Salt Cay</option>
                        <option>Pine Cay</option>
                        <option>North Caicos</option>
                        <option>Middle Caicos</option>
                        <option>South Caicos</option>
                        <option>Ambergris Cay</option>
                        <option>Parrot Cay</option>
                        <option>Providenciales</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="inputValue">Value:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group form-group mb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text" id="basic-addon1">$</span>
                    </div>

                    <input type="text" name="value" id="inputValue" value="{{landOfficer.value }}"
                           pattern="^[0-9.,]*$"
                           data-type="currency" class="form-control" disabled>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-11 text-right">
                <div class="custom-control custom-checkbox align-content-end">
                    <input type="checkbox" class="custom-control-input" name="informationValidated"
                           id="information-validated" {{#if landOfficer.validatedInformation}}
                           checked {{/if}} disabled/>
                    <label class="custom-control-label" for="information-validated">Validate Information</label>
                </div>
            </div>

        </div>
        <br>

        <div class="row mt-1 justify-content-md-center">
            <div class="col-10">
                <label class="pl-1 pb-1">Uploaded Files</label>
            </div>

        </div>

        <div id="officer-assign-table" class="row justify-content-md-center">
            <div class="col-md-10">
                <div class="table-responsive">
                    <table class="table w-100 nowrap table-striped font-size">
                        <thead>
                        <tr>
                            <th scope="col" style="width: auto">Document Name</th>
                            <th scope="col" style="width: 15%" class="text-center">Present</th>
                            <th scope="col" style="width: 15%" class="text-center">Download</th>
                            <th scope="col" style="width: 15%" class="text-center">Validate</th>
                        </tr>
                        </thead>
                        <tbody>
                        {{#each landOfficer.files}}
                            <tr>
                                <td>{{ external }}</td>
                                <td class="text-center">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" disabled class="custom-control-input"
                                               name="files[{{ id }}][present]" id="file-{{ @key }}"
                                               form="submissionForm"
                                            {{#if present}} checked {{/if}} />
                                        <label class="custom-control-label" for="file-{{ @key }}"></label>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <button type="button" class="btn solid royal-blue download-button"
                                            id="btn-{{internal}}" data-land-officer-id="{{../landOfficer._id}}"
                                            data-file-id="{{ id }}" data-toggle="modal" data-target="#downloadFileModal"
                                            {{#unless present}}disabled{{/unless}}>
                                        Download
                                    </button>
                                </td>
                                <td class="text-center">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" name="files[{{ id }}][validated]"
                                               class="custom-control-input validation-checkbox"
                                               id="file-validated-{{ @key }}" form="submissionForm" {{#if validated }}
                                               checked {{/if}} disabled/>
                                        <label class="custom-control-label" for="file-validated-{{ @key }}"></label>
                                    </div>
                                </td>
                            </tr>
                        {{/each}}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        {{#if landOfficer.stampDutyAdditionalFiles}}
            <div class="row mt-1 justify-content-md-center">
                <div class="col-10">
                    <label class="pl-1 pb-1">The Collector of Stamp Duty upload</label>
                </div>

            </div>
            <div id="stamp-duty-upload-files-table" class="row justify-content-md-center">
                <div class="col-md-10">
                    <div class="table-responsive">
                        <table class="table w-100 nowrap table-striped font-size">
                            <thead>
                            <tr>
                                <th scope="col" style="width: auto">Document Name</th>
                                <th scope="col" style="width: 15%" class="text-center">
                                    Download
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr>
                                <td> Additional information</td>
                                <td class="text-center">

                                    <button type="button" class="btn btn-sm solid royal-blue download-button"
                                            id="btn-{{landOfficer.stampDutyAdditionalFiles.internal}}"
                                            data-land-officer-id="{{landOfficer._id}}"
                                            data-file-id="{{ landOfficer.stampDutyAdditionalFiles.id }}"
                                            data-file-group="{{landOfficer.stampDutyAdditionalFiles.fileGroup}}"
                                            data-toggle="modal" data-target="#downloadFileModal">
                                        Download
                                    </button>

                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        {{/if}}

        {{#ifEquals landOfficer.status 'TRANSFER COMPLETED'}}

            <div class="row justify-content-md-center">
                <hr>
                <div class="col-md-10">
                    <h5><b>Transfer Complete File</b></h5>
                </div>
                <div class="col-md-10">

                    <button type="button" class="btn solid royal-blue download-button"
                            id="btn-{{landOfficer.transferCompletedFiles.internal}}"
                            data-land-officer-id="{{landOfficer._id}}"
                            data-file-id="{{ landOfficer.transferCompletedFiles.id }}"
                            data-file-group="{{landOfficer.transferCompletedFiles.fileGroup}}"
                            data-toggle="modal" data-target="#downloadFileModal">
                        Download
                    </button>
                </div>
            </div>
            <br>
        {{/ifEquals}}

        {{#ifEquals landOfficer.exemptionType 'Section 23- Companies & 28- Bodies Corporate'}}

            <div class="row justify-content-md-center">
                <hr>
                <div class="col-md-10">
                    <h5><b>Company information Files</b></h5>
                </div>
                <div class="col-md-10">

                    <button type="button" class="btn solid royal-blue download-button"
                            id="btn-{{landOfficer.companyInformationFiles.internal}}"
                            data-land-officer-id="{{landOfficer._id}}"
                            data-file-id="{{ landOfficer.companyInformationFiles.id }}"
                            data-file-group="{{landOfficer.companyInformationFiles.fileGroup}}"
                            data-toggle="modal" data-target="#downloadFileModal">
                        Download
                    </button>
                </div>
            </div>
            <br>
        {{/ifEquals}}

        {{#ifEquals landOfficer.status 'COMPLETED'}}

            <div class="row justify-content-md-center">
                <hr>
                <div class="col-md-10">
                    <h5><b>Signed Transfer File</b></h5>
                </div>
                <div class="col-md-10">

                    <button type="button" class="btn solid royal-blue download-button"
                            id="btn-{{landOfficer.signedFiles.internal}}" data-land-officer-id="{{landOfficer._id}}"
                            data-file-id="{{ landOfficer.signedFiles.id }}"
                            data-file-group="{{landOfficer.signedFiles.fileGroup}}"
                            data-toggle="modal" data-target="#downloadFileModal">
                        Download
                    </button>
                </div>
            </div>
            <br>
        {{/ifEquals}}

        {{#if canComplete}}
            <hr>
            <div class="row justify-content-md-center">
                <div class="col-md-10">
                    <h5><b>Transfer</b></h5>
                </div>
                <div class="col-md-10">
                    {{#ifEquals landOfficer.type 'FINANCE'}}
                        {{#ifEquals landOfficer.status 'TRANSFER COMPLETED'}}
                            <span>Please upload the remission order</span>
                        {{else}}
                            <span>Please upload the signed transfer</span>
                        {{/ifEquals}}
                    {{else}}
                        <span>Please upload the signed transfer</span>
                    {{/ifEquals}}
                    <br>
                </div>
                <div class="col-md-10 pt-2">
                    <button type="button" form="submitForm" class="btn btn-lg solid royal-blue btn-block upload-button"
                            data-toggle="modal" data-target="#upload-documents-modal" id="btn-signed-evidences"
                            data-submission-id="{{landOfficer._id}}"
                            data-file-type-id="{{landOfficer.signedFiles.id}}"
                            data-field="{{landOfficer.signedFiles.internal}}"
                            data-file-group="{{landOfficer.signedFiles.fileGroup}}">
                        <i class="fa fa-cloud-upload" aria-hidden="true"></i> Upload
                    </button>
                </div>
            </div>
        {{/if}}

    </div>
</div>

<script>

    $('#island option[value="{{landOfficer.island}}"]').attr('selected', 'selected');
    $('#island').val("{{landOfficer.island}}");

    $("input[data-type='currency']").on({
        keyup: function () {
            formatCurrency($(this));
        },
        blur: function () {
            formatCurrency($(this), "blur");
        }
    });
</script>
