<div>
    <div>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="filingYourself">
                        Are you filing this application on behalf of yourself?
                    </label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input"
                                   id="filingYourselfYes" name="filingYourself" value="Yes"
                                   required {{#if application.filingYourself}}checked{{/if}}
                                   disabled/>
                            <label class="custom-control-label"
                                   for="filingYourselfYes">Yes</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input"
                                   id="filingYourselfNo" value="No" name="filingYourself"
                                   {{#unless
                                           application.filingYourself}}checked{{/unless}}
                                   disabled/>
                            <label class="custom-control-label"
                                   for="filingYourselfNo">No</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{#unless application.filingYourself}}
            <div id="personal-details">
                <h3>Personal Details:</h3>
                <br>
                <!-- FIRST NAME -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label class="mb-2" for="personalFirstName">First
                                Name</label>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <input type="text" name="personalFirstName" id="personalFirstName"
                                   class="form-control"
                                   value="{{application.personalInformation.firstName}}"
                                   disabled
                                   required>
                        </div>
                    </div>
                </div>
                <!-- LAST NAME -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label class="mb-2" for="personalLastName">Last
                                Name</label>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <input type="text" name="personalLastName" id="personalLastName"
                                   class="form-control"
                                   value="{{application.personalInformation.lastName}}" disabled
                                   required>
                        </div>
                    </div>
                </div>
                <!-- FIRM -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label class="mb-2" for="personalFirm">Firm</label>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <input type="text" name="personalFirm" id="personalFirm"
                                   class="form-control"
                                   value="{{application.personalInformation.firm}}" disabled>
                        </div>
                    </div>
                </div>

                <!-- PHONE -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label class="mb-2" for="personalPhone">Phone
                                number</label>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <input type="text" id="personalPhone" class="form-control"
                                   name="personalPhone"
                                   value="{{application.personalInformation.phone}}" disabled
                                   required>
                        </div>
                    </div>
                </div>
                <!-- EMAIL -->
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <label class="mb-2" for="personalEmail">E-mail</label>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <input name="personalEmail" id="personalEmail" class="form-control"
                                   type="email"
                                   value="{{application.personalInformation.email}}"
                                   disabled required>
                        </div>
                    </div>
                </div>
                <br>
                <div class="row justify-content-end">
                    <div class="col-3">
                        <div class="custom-control custom-checkbox custom-control-inline">
                            <input type="checkbox" class="custom-control-input"
                                   id="validatedPersonalDetails" name="validatedPersonalDetails"
                                   {{#if
                                           validations.personalDetails}}checked{{/if}}
                                   value="Yes" {{#if disabledValidations}}disabled{{/if}}
                                   required/>
                            <label class="custom-control-label"
                                   for="validatedPersonalDetails">Validate
                                Information</label>
                        </div>
                    </div>
                </div>
            </div>
        {{/unless}}
        <hr>
        <div id="applicant-details">
            {{>client-forms/applicant-details application=application island=island foundCardHolder=foundCardHolder
        statusInformationColor=statusInformationColor}}
            <div class="row justify-content-end">
                <div class="col-3">
                    <div class="custom-control custom-checkbox custom-control-inline">
                        <input type="checkbox" class="custom-control-input"
                               id="validatedApplicantDetails" name="validatedApplicantDetails"
                               {{#if validations.applicantDetails}}checked{{/if}}
                                {{#if disabledValidations}}disabled{{/if}}
                               value="Yes" required/>
                        <label class="custom-control-label"
                               for="validatedApplicantDetails">Validate
                            Information</label>
                    </div>
                </div>
            </div>
        </div>
        <hr>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="additionalApplicants">
                        Are there any additional applicants?
                    </label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <div class="col-12 d-flex justify-content-end">
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input"
                                   id="additionalApplicantsYes" name="additionalApplicants"
                                   value="Yes" {{#if
                                           application.additionalApplicants}}checked{{/if}}
                                   disabled
                                   required/>
                            <label class="custom-control-label"
                                   for="additionalApplicantsYes">Yes</label>
                        </div>
                        <div class="custom-control custom-radio custom-control-inline">
                            <input type="radio" class="custom-control-input"
                                   id="additionalApplicantsNo" value="No"
                                   name="additionalApplicants" {{#unless
                                           application.additionalApplicants }}checked{{/unless}}
                                   disabled/>
                            <label class="custom-control-label"
                                   for="additionalApplicantsNo">No</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{#if application.additionalApplicants}}
            <div id="additional-applicants">
                {{>client-forms/additional-applicants application=application islands=islands}}
                <div class="row justify-content-end">
                    <div class="col-3">
                        <div class="custom-control custom-checkbox custom-control-inline">
                            <input type="checkbox" class="custom-control-input"
                                   id="validatedAdditionalApplicants" {{#if
                                   validations.additionalApplicants}}checked{{/if}}
                                    {{#if disabledValidations}}disabled{{/if}}
                                   name="validatedAdditionalApplicants" value="Yes"
                                   required/>
                            <label class="custom-control-label"
                                   for="validatedAdditionalApplicants">Validate
                                Information</label>
                        </div>
                    </div>
                </div>
            </div>
        {{/if}}
        <hr>
        <div id="property-details">
            {{>client-forms/property-details application=application islands=islands}}
            <div class="row justify-content-end">
                <div class="col-3">
                    <div class="custom-control custom-checkbox custom-control-inline">
                        <input type="checkbox" class="custom-control-input"
                               id="validatedPropertyDetails" name="validatedPropertyDetails"
                               {{#if validations.propertyDetails}}checked{{/if}}
                            {{#if disabledValidations}}disabled{{/if}}
                               value="Yes"
                               required/>
                        <label class="custom-control-label"
                               for="validatedPropertyDetails">Validate
                            Information</label>
                    </div>
                </div>
            </div>
        </div>
        <hr>

        <!-- SELLERS -->
        {{#if application.sellers}}
            <div id="additional-applicants">
                {{>client-forms/sellers application=application islands=islands}}
                <div class="row justify-content-end">
                    <div class="col-3">
                        <div class="custom-control custom-checkbox custom-control-inline">
                            <input type="checkbox" class="custom-control-input"
                                   id="validatedSellersDetails" {{#if
                                           validations.sellersDetails}}checked{{/if}}
                                           {{#if disabledValidations}}disabled{{/if}}
                                   name="validatedSellersDetails" value="Yes" required/>
                            <label class="custom-control-label"
                                   for="validatedSellersDetails">Validate
                                Information</label>
                        </div>
                    </div>
                </div>
            </div>
            <hr>
        {{/if}}

        <div>
            <div class="row mb-1">
                <div class="col-md-12">
                    <h3>Documents:</h3>
                </div>
            </div>
            <div class="row my-2">
                <div class="col-12">
                    {{#each filesInformation}}
                        {{#if filesCount}}
                            <hr>

                            <div class="row">
                                <div class="col-9">
                                    <h4>{{name}}</h4>
                                    <h5 class="mt-1">Files:</h5>
                                </div>
                                <div class="col-3">
                                    <div class="custom-control custom-checkbox custom-control-inline">
                                        <input type="checkbox" class="custom-control-input"
                                               id="validated_{{@key}}" name="validated_{{@key}}"
                                               {{#if validated}}checked{{/if}} value="Yes"
                                               {{#if ../disabledValidations}}disabled{{/if}}
                                               required/>
                                        <label class="custom-control-label"
                                               for="validated_{{@key}}">Validate
                                            Information</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <ul style="list-style: disc;" class="ml-3">
                                        {{#each files}}
                                            <li>
                                                <a href="/home-owner-policy/stamp-duty-exemption/{{../../application._id}}/download/{{@../key}}/{{fileId}}"
                                                   target="_blank">{{originalName}}</a>
                                            </li>
                                        {{/each}}
                                    </ul>
                                </div>
                            </div>
                        {{/if}}
                    {{/each}}
                </div>
            </div>

        </div>
    </div>

</div>
