const ExemptionModel = require('../models/exemptions');
const StampDutyApplicationModel = require('../models/stampDutyExemptionApplication').StampDutyApplicationModel;
const ImportDutyWaiverApplicationModel = require('../models/importDutyWaiverApplication').ImportDutyWaiverApplicationModel;
const ReductionApplicationModel = require('../models/stampDutyReductionApplication').ReductionApplicationModel;
const { islands, calendar } = require('../constants');
const httpConstants = require('http2').constants;
const moment = require('moment');
const statusCardHolderHelper = require('../shared/statusCardHolder.helper');

exports.getDashboard = async function (req, res) {
    try {
        let query = [{auditReady: true}];
        let stampDutyApplicationQuery = [{auditReady: true}];
        let landOfficers = [];
        let stampDutyExemptions = [];
        let importDutyWaiverApplications = [];
        let reductionApplications = [];

        let filters = {
            "searchFilter": req.body.searchFilter,
            "submittedEnd": req.body.submittedEnd,
            "submittedStart": req.body.submittedStart,
            "exemptionType": req.body.exemptionType
        };
        let submissionDate = {};
        if (req.body.submittedStart) {
            submissionDate["createdAt"] = {
                $gte: req.body.submittedStart,
                $lte: req.body.submittedEnd ? moment(req.body.submittedEnd).add(1, 'd').toDate() : new Date(),
            };
            query.push(submissionDate);
            stampDutyApplicationQuery.push(submissionDate);
        } else if (req.body.submittedEnd) {
            submissionDate["createdAt"] = { $lte: moment(req.body.submittedEnd).add(1, 'd').toDate() };
            query.push(submissionDate);
            stampDutyApplicationQuery.push(submissionDate);
        }
        let exceptionType;

        if (req.body.exemptionType === 'love-and-affection') {
            exceptionType = 'Natural Love & Affection';
        } else if (req.body.exemptionType === 'section-exemptions') {
            exceptionType = 'Section 23 & 28 Exemptions';
        } else if (req.body.exemptionType === 'charitable-institution') {
            exceptionType = 'Transfers to Charitable Institutions';
        }
        else if (req.body.exemptionType === 'transmission') {
            exceptionType = 'Transmission';
        }
        else if (req.body.exemptionType === 'refunds') {
            exceptionType = 'Refunds'
        }
        else if (req.body.exemptionType === 'remissions') {
            exceptionType = 'Remissions'
        }
        else if (req.body.exemptionType === 'Import Duty Waiver') {
            exceptionType = 'Import Duty Waiver'
        }
        else if (req.body.exemptionType === 'Stamp Duty Reduction') {
            exceptionType = 'Stamp Duty Reduction'
        }
        else if (req.body.exemptionType === 'exemption-application') {
            exceptionType = 'Stamp Duty Exemption'
        }

        if (exceptionType) {
            query.push({ "exemptionType": exceptionType });
        }

        if (req.body.searchFilter && req.body.searchFilter.length > 2) {
            const searchText = {
                $or: [{ 'transferorName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'transfereeName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'parcelNumber': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'parcelTextNumber': { $regex: req.body.searchFilter, $options: 'i' } }]
            };
            query.push(searchText);
            const searchText2 = {
                $or: [
                { 'firstName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'lastName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'applicantDetails.firstName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'applicantDetails.lastName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'companyDetails.name': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'propertyDetails.parcel': { $regex: req.body.searchFilter, $options: 'i' } }
              ]
            };
            stampDutyApplicationQuery.push(searchText2);
        }

        if (!filters.exemptionType || (filters.exemptionType !== "exemption-application" &&
            req.body.exemptionType !== 'Import Duty Waiver' && filters.exemptionType !== 'Stamp Duty Reduction')
            || filters.exemptionType === 'all') {
            landOfficers = await ExemptionModel.find({ $and: query });
        }

        
        landOfficers.map((updateStatus) => {
            if (updateStatus.status === "SEND TO EXCHANGE OF INFORMATION UNIT") {
                updateStatus.status = "SUBMITTED";
            }
        });
        
        if (!filters.exemptionType || filters.exemptionType === 'exemption-application' || filters.exemptionType === 'all') {
            stampDutyExemptions = await StampDutyApplicationModel.find({ $and: stampDutyApplicationQuery });
        }
        if (!req.body.exemptionType || req.body.exemptionType === 'Import Duty Waiver' || filters.exemptionType === 'all') {
            importDutyWaiverApplications = await ImportDutyWaiverApplicationModel.find({ $and: stampDutyApplicationQuery });
        }
        if (!filters.exemptionType || filters.exemptionType === 'Stamp Duty Reduction' || filters.exemptionType === 'all') {
            reductionApplications = await ReductionApplicationModel.find({ $and: stampDutyApplicationQuery });
        }



        res.render('auditor/dashboard',
            {
                landOfficers,
                filters,
                stampDutyExemptions,
                importDutyWaiverApplications,
                reductionApplications,
            });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/');
    }
};

exports.getSubmissionsView = async function (req, res) {
    try {
        req.session.files = {};
        const landOfficer = await ExemptionModel.findById(req.params.submissionId);

        res.render('auditor/open-stamp-duty-form',
            {
                landOfficer: landOfficer,
            });

    } catch (e) {
        console.log(e);
        return res.status(500).end();
    }
};

exports.getStampDutyExemptionApplicationView = async function (req, res) {
    try {
        req.session.files = {};
        const application = await StampDutyApplicationModel.findById(req.params.submissionId);
        application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
        application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];


        const filesInformation = {
            affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.affidavit : false },
            marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.marriageCertificates : false },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.birthCertificate : false },
            parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.parentsDocuments : false },
            signedAgreement: { name: "Signed agreement of the purchase of the property", files: application.signedAgreement, filesCount: application.signedAgreement.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.signedAgreement : false },
            landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.landRegister : false },
            valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.valuation : false },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.statusCard : false },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.botcCertificate : false },
        }
        let applicants = [{
            firstName: application.firstName,
            lastName: application.lastName,
            dateOfBirth: application.dateOfBirth,
            statusCardNumber: application.statusCardNumber,
        }];

        if (application.applicants && application.applicants.length) {
            applicants.push(...application.applicants.map(ap => {
                return {
                    firstName: ap.firstName,
                    lastName: ap.lastName,
                    dateOfBirth: ap.dateOfBirth,
                }
            }));
        }


        applicants = await Promise.all(applicants.map(async (ap) => {
            // Search in import duty waiver submissions
            const previousImportDutyApplications = await ImportDutyWaiverApplicationModel.find({
                '$or': [
                    {
                        'lastName': ap.lastName,
                        'dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'applicants.lastName': ap.lastName,
                        'applicants.dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
                    }
                ],
            });
            // Search in import duty waiver submissions
            const previousStampDutyExemptionApplications = await StampDutyApplicationModel.find({
                '$or': [
                    {
                        'lastName': ap.lastName,
                        'dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'applicants.lastName': ap.lastName,
                        'applicants.dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
                    }
                ],
            });
            ap.previousApplications = {
                importDuty: previousImportDutyApplications,
                stampDutyExemption: previousStampDutyExemptionApplications,
            };
            ap.totalValue =
                [
                    previousImportDutyApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
                    previousStampDutyExemptionApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
                ].reduce((a, b) => a + b, 0);
            return ap;
        }));

        const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatus(application);


        res.render('auditor/open-stamp-duty-exemption-form',
            {
                application: application,
                islands,
                calendar,
                applicants,
                filesInformation,
                foundCardHolder,
                statusInformationColor,
                remissionCount: application.remissionOrder ? application.remissionOrder.length : 0,
                additionalInformationCount: application.additionalInformation ? application.additionalInformation.length : 0,
            });

    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.getImportDutyWaiverApplicationView = async function (req, res) {
    try {
        req.session.files = {};
        const application = await ImportDutyWaiverApplicationModel.findById(req.params.submissionId);
        application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
        application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];
        const filesInformation = {
            affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.affidavit : false },
            marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.marriageCertificates : false },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.birthCertificate : false },
            parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.parentsDocuments : false },
            itemsInvoices: { name: "Copies of invoices", files: application.itemsInvoices, filesCount: application.itemsInvoices.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.itemsInvoices : false },
            buildingPictures: { name: "Building photographs", files: application.buildingPictures, filesCount: application.buildingPictures.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.buildingPictures : false },
            buildingPermit: { name: "Building permit", files: application.buildingPermit, filesCount: application.buildingPermit.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.buildingPermit : false },
            landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.landRegister : false },
            valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.valuation : false },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.statusCard : false },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.botcCertificate : false },
        }
        let applicants = [{
            firstName: application.firstName,
            lastName: application.lastName,
            dateOfBirth: application.dateOfBirth,
            statusCardNumber: application.statusCardNumber,
        }];

        if (application.applicants && application.applicants.length) {
            applicants.push(...application.applicants.map(ap => {
                return {
                    firstName: ap.firstName,
                    lastName: ap.lastName,
                    dateOfBirth: ap.dateOfBirth,
                }
            }));
        }


        applicants = await Promise.all(applicants.map(async (ap) => {
            // Search in import duty waiver submissions
            const previousImportDutyApplications = await ImportDutyWaiverApplicationModel.find({
                '$or': [
                    {
                        'lastName': ap.lastName,
                        'dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'applicants.lastName': ap.lastName,
                        'applicants.dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
                    }
                ],
            });
            // Search in import duty waiver submissions
            const previousStampDutyExemptionApplications = await StampDutyApplicationModel.find({
                '$or': [
                    {
                        'lastName': ap.lastName,
                        'dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'applicants.lastName': ap.lastName,
                        'applicants.dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
                    }
                ],
            });
            ap.previousApplications = {
                importDuty: previousImportDutyApplications,
                stampDutyExemption: previousStampDutyExemptionApplications,
            }
            ap.totalValue =
                [
                    previousImportDutyApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
                    previousStampDutyExemptionApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
                ].reduce((a, b) => a + b, 0);
            return ap;
        }));


        const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatus(application);


        res.render('auditor/open-import-duty-waiver-form',
            {
                application: application,
                islands,
                calendar,
                applicants,
                filesInformation,
                foundCardHolder,
                statusInformationColor
            });

    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.getReductionApplicationView = async function (req, res) {
    try {
        req.session.files = {};
        const application = await ReductionApplicationModel.findById(req.params.submissionId);
        application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
        application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];


        const filesInformation = {
            affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.affidavit : false },
            marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.marriageCertificates : false },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.birthCertificate : false },
            parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.parentsDocuments : false },
            landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.landRegister : false },
            valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.valuation : false },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.statusCard : false },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.psOfficerTier ? application.psOfficerTier.validations?.botcCertificate : false },
        }

        const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatusReduction(application);

        res.render('auditor/open-stamp-duty-reduction-form',
            {
                application: application,
                validations: application.psOfficerTier ? application.psOfficerTier.validations : null,
                islands,
                calendar,
                filesInformation,
                remissionCount: application.remissionOrder ? application.remissionOrder.length : 0,
                additionalInformationCount: application.additionalInformation ? application.additionalInformation.length : 0,
                foundCardHolder,
                statusInformationColor
            });

    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};


