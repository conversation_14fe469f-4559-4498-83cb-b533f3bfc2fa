const express = require('express');
const router = express.Router();
const deputyCommissionerController = require('../controllers/deputyCommissionerController');

//PAGES
router.get('/dashboard', ensureAuthenticated, deputyCommissionerController.getDashboard);
router.post('/dashboard', ensureAuthenticated, deputyCommissionerController.getDashboard);


router.get('/import-duty-waiver/:id/update', ensureAuthenticated, deputyCommissionerController.getImportDutyWaiverView);
router.post('/import-duty-waiver/:id/update', ensureAuthenticated, deputyCommissionerController.updateImportDutyWaiver);
router.post('/import-duty-waiver/:id/approve', ensureAuthenticated, deputyCommissionerController.approveImportDutyWaiver);
router.post('/import-duty-waiver/:id/decline', ensureAuthenticated, deputyCommissionerController.declineImportDutyWaiver);
router.post('/import-duty-waiver/:id/conflict', ensureAuthenticated, deputyCommissionerController.conflictImportDutyWaiver);
router.post('/import-duty-waiver/:id/request-information', ensureAuthenticated, deputyCommissionerController.requestInformationImportDutyWaiver);


function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isDeputyCommissioner) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;
