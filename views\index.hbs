<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h1>Stamp Duty Exemption Program</h1>
                    <p>Welcome {{user.name}}</p>
                    <h4> Statistics: </h4> <br>
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="myChartStatus"></canvas>
                        </div>
                        <div class="col-md-6">
                            <canvas id="myChartType"></canvas>
                        </div>
                    </div>                    
                    <br><br><br><br>
                    <div class="row">
                        {{#if authentication.isStampDuty}}
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">The Collector of Stamp Duty</h5>
                                    <a href="/stamp-duty/dashboard" class="btn btn-light btn-sm waves-effect">View all submissions</a>
                                </div>
                            </div>
                        </div>
                        {{/if}}
                        {{#if authentication.isPsOfficer}}
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    {{>notification-counter count=psSubmissionsCount}}
                                    <h5 class="card-title text-white">PS Submissions</h5>
                                    <a href="/ps-submissions/dashboard" class="btn btn-light btn-sm waves-effect">View PS Submissions</a>
                                </div>
                            </div>
                        </div>
                        {{/if}}
                        {{#if authentication.isLandOfficer}}
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    {{>notification-counter count=landOfficerCount}}
                                    <h5 class="card-title text-white">Land Officer Submissions</h5>
                                    <a href="/land-officer/dashboard" class="btn btn-light btn-sm waves-effect">View Land Officer Submissions</a>
                                </div>
                            </div>
                        </div>
                        {{/if}}
                        {{#if authentication.isFinance}}
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        {{>notification-counter count=financeOfficerCount}}
                                        <h5 class="card-title text-white">Ministry of Finance</h5>
                                        <a href="/finance-officer/dashboard" class="btn btn-light btn-sm waves-effect">
                                            View Finance Officer submissions</a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                        {{#if authentication.isCustoms}}
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        {{>notification-counter count=customsOfficerCount}}
                                        <h5 class="card-title text-white">Customs Officer</h5>
                                        <a href="/customs-officer/dashboard" class="btn btn-light btn-sm waves-effect">
                                            View Customs Officer submissions</a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                        {{#if authentication.isDeputyCommissioner}}
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        {{>notification-counter count=deputyCommissionerCount}}
                                        <h5 class="card-title text-white">Deputy Commissioner</h5>
                                        <a href="/deputy-commissioner/dashboard" class="btn btn-light btn-sm waves-effect">
                                            View Deputy Commissioner submissions</a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                        {{#if authentication.isAuditor}}
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Auditor</h5>
                                        <a href="/auditor/dashboard" class="btn btn-light btn-sm waves-effect">
                                            View submissions</a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                        {{#if authentication.isStampDuty}}
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title text-white">Decline notices</h5>
                                        <a href="/decline-notices" class="btn btn-light btn-sm waves-effect">
                                            Manage decline notices</a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript">
    let status = document.getElementById('myChartStatus').getContext('2d');
    let typeForm = document.getElementById('myChartType').getContext('2d');
    let labelsForStatus = ['Saved', 'Submitted', 'Requested Information', 'Pending PS', 'Declined', 'Approved PS', 'Transfer Pending', 'Transfer Completed', 'Completed', 'Conflict'];
    let colorsForStatus = ['rgb(243, 161, 8)', 'rgb(48, 17, 223)', 'rgb(255, 81, 0)',
        'rgba(90, 10, 70, 0.836)', 'rgb(230, 7, 7)', 'rgb(6, 110, 151)', 'rgb(77, 71, 21)', 'rgb(151, 214, 4)', 'rgb(18, 163, 4)', 'rgb(86, 7, 12)'];

    let myChart = new Chart(status, {
        type: 'pie',
        data: {
            datasets: [{
                data: [
                {{saved}},
                {{submitted}},
                {{requested}},
                {{pendingPS}},
                {{declined}},
                {{approvedPS}},
                {{transferPending}},
                {{transferCompleted}},
                {{completed}},
                {{conflict}}
                ],
                backgroundColor: colorsForStatus
            }],
            labels: labelsForStatus
        },
        options: {
            responsive: true,
            plugins: {
                datalabels: {
                    color: '#fff'
                }
            },
            legend: {
                position: 'left',
                labels: {
                fontSize: 15
            }
            }
        }
    });
    
    let myChart2 = new Chart(typeForm, {
        type: 'bar',
        data: {
            labels: ['Natural Love','Section Exemptions','Charitable Institutions', 'Home Owner Policy'],
            datasets: [{
                label: 'Amount of submissions per type',
                data: [
                    {{naturalLove}},
                    {{sectionExemption}},
                    {{charitableInstitutions}},
                    {{homeOwnerPolicy}}
                ],
                backgroundColor: [
                    'rgb(48, 17, 223)',
                    'rgb(48, 17, 223)',
                    'rgb(48, 17, 223)',
                    'rgb(48, 17, 223)'
                ]
            }]
        },
        options:{
        scales:{
            //xAxes: [{
            //    display: false //this will remove all the x-axis grid lines
            //}],
            yAxes: [{
            display: true,
            ticks: {
                suggestedMin: 0,    // minimum will be 0, unless there is a lower value.
                // OR //
                beginAtZero: true   // minimum value will be 0.
            }
        }]
        }
    }
    }); 
</script>
