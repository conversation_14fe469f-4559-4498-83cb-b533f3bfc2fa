const excel = require("node-excel-export");
const moment = require("moment");
const ExemptionModel = require('../models/exemptions');

exports.exportSearchXls = async function (req, res) {
    try {
        const documentsData = await searchToExport(req);

        const styles = {
            headerTable: {
                fill: {
                    fgColor: {
                        rgb: "ffffff",
                    },
                },
                border: {
                    top: {style: "thin", color: "000000"},
                    bottom: {style: "thin", color: "000000"},
                    left: {style: "thin", color: "000000"},
                    right: {style: "thin", color: "000000"},
                },
                font: {
                    color: {
                        rgb: "000000",
                    },
                    sz: 12,
                    bold: false,
                    underline: false,
                },
            },
        };

        const specification = {
            transferorName: {
                displayName: "Name Transferor",
                headerStyle: styles.headerTable,
                width: 120,
            },
            transfereeName: {
                displayName: "Name Transferee",
                headerStyle: styles.headerTable,
                width: 120,
            },
            parcelNumber: {
                displayName: "Parcel Number",
                headerStyle: styles.headerTable,
                width: 120,
            },
            district: {
                displayName: "District",
                headerStyle: styles.headerTable,
                width: 160,
            },
            island: {
                displayName: "Island",
                headerStyle: styles.headerTable,
                width: 120,
            },
            value: {
                displayName: "Value",
                headerStyle: styles.headerTable,
                width: 120,
            },
            exemptionType: {
                displayName: "Exemption Type",
                headerStyle: styles.headerTable,
                width: 120,
            },
            status: {
                displayName: "Status",
                headerStyle: styles.headerTable,
                width: 120,
            },
            createdAt: {
                displayName: "Created",
                headerStyle: styles.headerTable,
                width: 120,
            },
            completedAt: {
                displayName: "Completed",
                headerStyle: styles.headerTable,
                width: 120,
            }
        };
        const dataset = [];
        for (let i = 0; i < documentsData.length; i++) {
            dataset.push({
                transferorName: documentsData[i].transferorName,
                transfereeName: documentsData[i].transfereeName,
                parcelNumber: documentsData[i].parcelNumber + "/" + documentsData[i].parcelTextNumber ,
                district: documentsData[i].district,
                island: documentsData[i].island,
                value: documentsData[i].value,
                exemptionType: documentsData[i].exemptionType,
                status: documentsData[i].status,
                createdAt: documentsData[i].createdAt ? moment(documentsData[i].createdAt).format("YYYY-MM-DD") : '',
                completedAt: documentsData[i].completedAt ?  moment(documentsData[i].completedAt).format("YYYY-MM-DD") : '',
            });
        }
        //
        const report = excel.buildExport([
            {
                name: "Submission",
                specification: specification,
                data: dataset,
            },
        ]);

        res.attachment("Report.xlsx");
        return res.send(report);
    } catch (error) {
        console.log(error);
    }
};

async function searchToExport(req) {
    try {

        const statusByOfficer = {
            "landOfficer": ['SAVED', 'REQUESTED', 'APPROVED PS', 'SUBMITTED'],
            "stampDutyOfficer": {
                "declined" : ["DECLINED"],
                "completed" : ["COMPLETED"],
                "submitted" : ["SUBMITTED"],
                "request-information": ["REQUESTED"],
                "pending-ps": ["PENDING PS"],
                "approved-ps":  ['RETURNED BY PS', 'APPROVED PS']
            },
            "psOfficer": ['PENDING PS', 'TRANSFER COMPLETED'],
            "financeOfficer": ['SAVED', 'REQUESTED', 'TRANSFER PENDING']
        };

        const exemptionTypes = {
            "love-and-affection": 'Natural Love & Affection',
            "section-exemptions": 'Section 23 & 28 Exemptions',
            "charitable-institution": 'Transfers to Charitable Institutions',
            "home-owner-policy": 'Home Owner Policy',
            "transmission": 'Transmission',
            "refunds": 'Refunds',
            "remissions": 'Remissions'
        };

        let filters = {
            "searchFilter": req.body.searchFilter,
            "exemptionType": req.body.exemptionType,
            "searchDeclined": !!req.body.searchDeclined,
            "searchCompleted": !!req.body.searchCompleted,
            "searchSaved": !!req.body.searchSaved,
            "submittedEnd": req.body.submittedEnd,
            "submittedStart": req.body.submittedStart,
            "searchRequested": !!req.body.searchRequested,
        };

        let query = [{}];
        let status = [];

        let submissionDate = {};

        if (req.body.submittedStart) {
            submissionDate["createdAt"] = {
                $gte: req.body.submittedStart,
                $lte: req.body.submittedEnd ? moment(req.body.submittedEnd).add(1, 'd').toDate() : new Date(),
            };
            query.push(submissionDate);
        } else if (req.body.submittedEnd) {
            submissionDate["createdAt"] = { $lte: moment(req.body.submittedEnd).add(1, 'd').toDate() };
            query.push(submissionDate);
        }

        if (req.body.officer === 'stampDutyOfficer'){
            if (req.body.status !== ''){
                const statusOfficer=  statusByOfficer[req.body.officer];
                status = statusOfficer[req.body.status]
            }
            else{
                status =  ['SUBMITTED', "TRANSFER COMPLETED"];
            }
        }
        else{
            if (req.body.searchDeclined) {
                status.push('DECLINED')
            }
            if (req.body.searchCompleted) {
                status.push('COMPLETED')
            }
            if (req.body.searchSaved) {
                status.push('SAVED')
            }
            if (req.body.searchRequested) {
                status.push('REQUESTED')
            }
            if (req.body.searchSubmitted) {
                status.push('SUBMITTED')
            }

            if (!filters.searchDeclined && !filters.searchCompleted){
                status = statusByOfficer[req.body.officer];
            }
        }


        query.push({"status": {$in: status}});
        if (req.body.officer === "financeOfficer") {
            query.push({"type": "FINANCE"});
        }
        else if (req.body.officer === "landOfficer"){
            query.push({"type": "LAND SUBMISSION"});
        }

        if (filters.searchFilter && filters.searchFilter.length > 2) {
            query.push({
                $or: [{'transferorName': {$regex: req.body.searchFilter, $options: 'i'}},
                    {'transfereeName': {$regex: req.body.searchFilter, $options: 'i'}},
                    {'parcelNumber': {$regex: req.body.searchFilter, $options: 'i'}},
                    {'parcelTextNumber': {$regex: req.body.searchFilter, $options: 'i'}}]
            })
        }

        if (filters.exemptionType){
            const exemption = exemptionTypes[filters.exemptionType];
            if (exemption){
                query.push({"exemptionType": exemption});
            }
        }

        return await ExemptionModel.find({$and: query})
    } catch (error) {
        console.log(error);
    }
}
