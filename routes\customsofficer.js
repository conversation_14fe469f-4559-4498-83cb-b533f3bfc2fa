const express = require('express');
const router = express.Router();
const customsOfficerController = require('../controllers/customsOfficerController');

//PAGES
router.get('/dashboard', ensureAuthenticated, customsOfficerController.getDashboard);
router.post('/dashboard', ensureAuthenticated, customsOfficerController.getDashboard);


router.get('/import-duty-waiver/:id/update', ensureAuthenticated, customsOfficerController.getImportDutyWaiverView);
router.post('/import-duty-waiver/:id/update', ensureAuthenticated, customsOfficerController.updateImportDutyWaiver);
router.post('/import-duty-waiver/:id/approve', ensureAuthenticated, customsOfficerController.approveImportDutyWaiver);
router.post('/import-duty-waiver/:id/decline', ensureAuthenticated, customsOfficerController.declineImportDutyWaiver);
router.post('/import-duty-waiver/:id/conflict', ensureAuthenticated, customsOfficerController.conflictImportDutyWaiver);
router.post('/import-duty-waiver/:id/request-information', ensureAuthenticated, customsOfficerController.requestInformationImportDutyWaiver);


function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isCustoms) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;
