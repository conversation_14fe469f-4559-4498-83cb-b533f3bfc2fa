<div class="card">
    <div class="card-body">
        <div class="row justify-content-md-left">
            <h4 style="color:#f2aa0f; font: 200% sans-serif;">{{landOfficer.exemptionType}} </h4>
        </div>
        <br>
        <h4><b><i>Comments: </i></b></h4>
        {{#each landOfficer.comments}}
        <div class="row">
            <div class="col-12">
                <span>({{#formatDate date "DD-MM-YYYY" }}{{/formatDate}})</span>
                <span class="font-weight-bold">{{  email }} - </span>
                <span> {{commentStatus}}: </span>
                <span>{{ comment }}</span>
            </div>
        </div>
        {{/each}}
        <br> <br>
        {{#ifEquals landOfficer.type 'LAND SUBMISSION'}}
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="instrument-number">Instrument Number:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" value="{{landOfficer.instrumentNumber }}" name="instrumentNumber"
                               id="instrument-number" class="form-control" {{#ifCond isEditable '==' false}} readonly
                        {{/ifCond}}>
                    </div>
                </div>
            </div>
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transferorName">Transferor:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" value="{{landOfficer.transferorName }}" name="transferorName" id="transferorName"
                               class="form-control" {{#ifCond isEditable '==' false}} readonly {{/ifCond}}>
                    </div>
                </div>
            </div>
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transfereeName">Transferee:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" value="{{landOfficer.transfereeName }}" name="transfereeName" id="transfereeName"
                               class="form-control" {{#ifCond isEditable '==' false}} readonly {{/ifCond}}>
                    </div>
                </div>
            </div>
        {{else}}
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transferorName">Applicant:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" value="{{landOfficer.transferorName }}" name="transferorName" id="transferorName"
                               class="form-control" {{#ifCond isEditable '==' false}} readonly {{/ifCond}}>
                    </div>
                </div>
            </div>
        {{/ifEquals}}

        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="parcel-number">Parcel
                        #:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input value="{{landOfficer.parcelNumber}}" name="parcelNumber" id="parcel-number" type="number"
                        maxlength="5" required class="form-control" {{#ifCond isEditable '==' false}} readonly
                        {{/ifCond}}>
                    <span class="input-group-text">/</span>
                    <input value="{{landOfficer.parcelTextNumber}}" name="parcelTextNumber" id="parcel-text-number"
                        type="text" required class="form-control" {{#ifCond isEditable '==' false}} readonly
                        {{/ifCond}}>
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="district">District:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <input type="text" value="{{landOfficer.district }}" name="district" id="district"
                        class="form-control" {{#ifCond isEditable '==' false}} readonly {{/ifCond}}>
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="island" id="island-label">Island
                    </label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <select class="custom-select" id="island" name="island" aria-labelledby="island-label"
                        {{#ifCond isEditable '==' false}} disabled {{/ifCond}}>
                        <option value="">Select</option>
                        <option>Grand Turk</option>
                        <option>Salt Cay</option>
                        <option>Pine Cay</option>
                        <option>North Caicos</option>
                        <option>Middle Caicos</option>
                        <option>South Caicos</option>
                        <option>Ambergris Cay</option>
                        <option>Parrot Cay</option>
                        <option>Providenciales</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="inputValue">Value:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group form-group mb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text" id="basic-addon1">$</span>
                    </div>

                    <input type="text" name="value" id="inputValue" value="{{landOfficer.value }}"
                           pattern="^[0-9.,]*$"
                            data-type="currency"  class="form-control"  {{#ifCond isEditable '==' false}}
                           readonly {{/ifCond}}>
                </div>
            </div>
        </div>
        <br>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group">
                    <label>Uploaded Files:</label>
                </div>

            </div>
            <div class="col-md-6">
                {{#each files}}
                <div class="row p-1">
                    <div class="col-md-10">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" name="files[{{id}}][present]" class="custom-control-input"
                                id="{{internal}}" form="submitForm" {{#if present}} checked {{/if}}
                                {{#ifCond ../isEditable '==' false}} disabled {{/ifCond}} />
                            <label class="custom-control-label" for="{{internal}}">{{external}}</label>
                        </div>
                    </div>

                    <div class="row justify-content-between">
                        {{#ifCond ../isEditable '==' true}}
                        <button type="button" form="submitForm" class="btn solid royal-blue upload-button"
                            data-toggle="modal" data-target="#upload-modal" id="btn-{{internal}}"
                            data-file-type-id="{{id}}" data-land-officer-id="{{../landOfficer._id}}" data-row="1"
                            data-field="{{internal}}" data-file-group="{{fileGroup}}" {{#if present}}
                            style="background-color:#0AC293;border-color:#0AC292;" {{/if}}>
                            Upload
                        </button>
                        {{else}}
                        <button type="button" class="btn solid royal-blue download-button" id="btn-{{internal}}"
                            data-land-officer-id="{{../landOfficer._id}}" data-file-id="{{ id }}" data-toggle="modal"
                            data-target="#downloadFileModal" {{#unless present}}disabled{{/unless}}>
                            Download
                        </button>
                        {{/ifCond}}
                    </div>
                </div>
                {{/each}}
            </div>
        </div>
        <br>

        {{#ifEquals landOfficer.type 'FINANCE'}}
            {{#ifEquals landOfficer.status 'TRANSFER PENDING'}}
                <div class="row justify-content-md-center">
                    <div class="col-md-10">
                        <h5><b>Complete Transfer</b></h5>
                    </div>
                    <div class="col-md-10">
                        <span>Please upload the completed transfer</span><br>
                    </div>
                    <div class="col-md-10 pt-2">
                        <button type="button" form="submitForm" class="btn btn-lg solid royal-blue btn-block upload-button"
                                data-toggle="modal" data-target="#upload-documents-modal" id="btn-signed-evidences"
                                data-submission-id="{{landOfficer._id}}"
                                data-file-type-id="{{landOfficer.transferCompletedFiles.id}}"
                                data-field="{{landOfficer.transferCompletedFiles.internal}}"
                                data-file-group="{{landOfficer.transferCompletedFiles.fileGroup}}">
                            <i class="fa fa-cloud-upload" aria-hidden="true"></i> Upload
                        </button>
                    </div>
                </div>
            {{/ifEquals}}
        {{/ifEquals}}
    </div>
</div>

<script>

    $('#island option[value="{{landOfficer.island}}"]').attr('selected', 'selected');
    $('#island').val("{{landOfficer.island}}");

    var input = document.getElementById('parcel-number');
    input.addEventListener('input', function () {
        if (this.value.length > 5)
            this.value = this.value.slice(0, 5);
    });

    $("input[data-type='currency']").on({
        change: function() {
            formatCurrency($(this));
        },
        keyup: function() {
            formatCurrency($(this));
        },
        blur: function() {
            formatCurrency($(this), "blur");
        }
    });

</script>
