<!-- DELETE OWNER MODAL -->
<div class="modal fade" id="confirmDeleteModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Saved Application</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">You are about to delete this application, are you sure? This action cannot be undone and all information and uploaded files will be permanently deleted.</div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Cancel
                </button>
                <button id="deleteApplicationButton" type="button" class="btn btn-danger">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let appId = '';
    $('#confirmDeleteModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        appId = button.data('application-id');
        
    });

    $('#deleteApplicationButton').click(function () {
        $.ajax({
            type: 'DELETE',
            url: '/land-officer/' + appId + '/delete',
            data: {},
            success: function () {
                Swal.fire(
                        'Success',
                        'The Application has been deleted successfully',
                        'success'
                ).then(() => {
                    location.href = '/land-officer/dashboard';
                });

            },
            error: function () {
                Swal.fire(
                        'Error',
                        'There was an error while trying to delete the Application',
                        'error'
                );
            },
        });
    });
</script>