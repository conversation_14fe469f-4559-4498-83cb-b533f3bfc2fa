<!doctype html>
<html lang="en-us">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <link rel="icon" href="assets/img/favicon.png" type="image/png">
    <title>STAMP DUTY</title>
    <!-- inject-head:css -->

    <link rel="stylesheet" href="/stylesheets/bootstrap.min.css">
    <link rel="stylesheet" href="/stylesheets/icons.min.css">

    <link href="/javascripts/libs/jquery-nice-select/nice-select.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/switchery/switchery.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/multiselect/multi-select.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/select2/select2.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-select/bootstrap-select.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-touchspin/jquery.bootstrap-touchspin.css" rel="stylesheet"
        type="text/css" />
    <link href="/javascripts/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-colorpicker/bootstrap-colorpicker.min.css" rel="stylesheet"
        type="text/css" />
    <link href="/javascripts/libs/clockpicker/bootstrap-clockpicker.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-datepicker/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/dropzone/dropzone.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/dropify/dropify.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/datatables/dataTables.bootstrap4.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/datatables/responsive.bootstrap4.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/datatables/select.bootstrap4.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/toastr/toastr.min.css" rel="stylesheet" type="text/css" />

    <link rel="stylesheet" href="/stylesheets/app.min.css">
    <link rel="stylesheet" href="/stylesheets/style.css">

    <script src="/javascripts/vendor.js"></script>
    <script src="/javascripts/libs/jquery-nice-select/jquery.nice-select.min.js"></script>
    <script src="/javascripts/libs/switchery/switchery.min.js"></script>
    <script src="/javascripts/libs/multiselect/jquery.multi-select.js"></script>
    <script src="/javascripts/libs/select2/select2.min.js"></script>
    <script src="/javascripts/libs/jquery-mockjax/jquery.mockjax.min.js"></script>
    <script src="/javascripts/libs/autocomplete/jquery.autocomplete.min.js"></script>
    <script src="/javascripts/libs/bootstrap-select/bootstrap-select.min.js"></script>
    <script src="/javascripts/libs/bootstrap-touchspin/jquery.bootstrap-touchspin.min.js"></script>
    <script src="/javascripts/libs/bootstrap-maxlength/bootstrap-maxlength.min.js"></script>
    <script src="/javascripts/libs/flatpickr/flatpickr.min.js"></script>
    <script src="/javascripts/libs/bootstrap-colorpicker/bootstrap-colorpicker.min.js"></script>
    <script src="/javascripts/libs/clockpicker/bootstrap-clockpicker.min.js"></script>
    <script src="/javascripts/libs/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
    <script src="/javascripts/libs/dropzone/dropzone.min.js"></script>
    <script src="/javascripts/libs/moment/moment.min.js"></script>

    <script src="/javascripts/libs/sweetalert2/sweetalert2.min.js"></script>

    <script src="/javascripts/libs/datatables/jquery.dataTables.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.bootstrap4.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.responsive.min.js"></script>
    <script src="/javascripts/libs/datatables/responsive.bootstrap4.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.buttons.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.bootstrap4.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.html5.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.flash.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.print.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.keyTable.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.select.min.js"></script>
    <script src="/javascripts/libs/toastr/toastr.min.js"></script>
    <script src="/javascripts/libs/handlebars/handlebars.runtime.min-v4.5.3.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.8.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@0.7.0"></script>


    <script type="text/javascript">
        $(document).ready(function () {

            var ieMessage = "To protect your security this portal is only accessible with Google Chrome, safari, Firefox and Edge.";

            if (GetIEVersion() > 0) {
                $("body").append("<div class='ie-div' style='position: absolute; top: 0px; left: 0px; line-height:30px; background-color: #0081B4; opacity:0.8; filter:alpha(opacity=80); color: #FFFFFF; font-weight:bold;width: 100%;height:100%;padding: 20px; padding-top:150px; font-size: 24px'><div style='text-align:center'>" + ieMessage + " Click <a href='microsoft-edge:" + document.location + "' style='font-size:24px;color:#FFFFFF;text-decoration:underline'>here</a> to open in Edge</div></div>");
            }

        });

        /**
         * @return {number}
         */
        function GetIEVersion() {

            var sAgent = window.navigator.userAgent;
            var Idx = sAgent.indexOf("MSIE");

            // If IE, return version number.
            if (Idx > 0)
                return parseInt(sAgent.substring(Idx + 5, sAgent.indexOf(".", Idx)));

            // If IE 11 then look for Updated user agent string.
            else if (!!navigator.userAgent.match(/Trident\/7\./))
                return 11;

            else
                return 0; //It is not IE
        }

        function hideIEMessage() {
            heroCheckThisOut$(".ie-div").remove();
        }


        function formatNumber(n) {
            return n.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",")
        }

        function formatCurrency(input, blur) {
            // appends $ to value, validates decimal side
            // and puts cursor back in right position.

            // get input value
            let input_val = input.val();

            // don't validate empty input
            if (input_val === "") { return; }

            // original length
            const original_len = input_val.length;

            // initial caret position
            let caret_pos = input.prop("selectionStart");

            // check for decimal
            if (input_val.indexOf(".") >= 0) {

                // get position of first decimal
                // this prevents multiple decimals from
                // being entered
                const decimal_pos = input_val.indexOf(".");

                // split number by decimal point
                let left_side = input_val.substring(0, decimal_pos);
                let right_side = input_val.substring(decimal_pos);

                // add commas to left side of number
                left_side = formatNumber(left_side);
                // validate right side
                right_side = formatNumber(right_side);
                // On blur make sure 2 numbers after decimal
                if (blur === "blur") {
                    right_side += "00";
                }

                // Limit decimal to only 2 digits
                right_side = right_side.substring(0, 2);

                // join number by .
                input_val = left_side + "." + right_side;

            } else {
                // no decimal entered
                // add commas to number
                // remove all non-digits
                input_val = formatNumber(input_val);

                // final formatting
                if (blur === "blur") {
                    input_val += ".00";
                }
            }

            // send updated string to input
            input.val(input_val);

            // put caret back in the right position
            const updated_len = input_val.length;
            caret_pos = updated_len - original_len + caret_pos;
            input[0].setSelectionRange(caret_pos, caret_pos);
        }


    </script>

    <!-- endinject -->
</head>

<body>
    <header class="main-header">
        <div class="container">
            <div class="main-logo">
                <figure>
                    <a href="/">
                        <img src="/images/TCI-Logo.png" style="width:150px; height:150px" alt="Turks & Caicos Islands Government">
                    </a>

                </figure>
                <!-- /.brand -->                
            </div>
            <!-- /.main-logo -->

           <div class="navigation">
                <nav class="sub-nav">
                    <ul>
                        <li>{{user.name}}</li>
                    </ul>
                    <ul>
                        {{#if user}}<li><a href="/logout" class="btn btn-danger  width-xl">Logout</a></li>{{/if}}
                    </ul>
                </nav>
                <!-- /.sub-nav-items -->

                
                
            </div>
            <!-- /.navigation -->

        </div>
    <!-- /.container -->
    </header>
    <br><br><br>
    {{{body}}}

    <script type='text/javascript' src='/javascripts/form-advanced.init.js'></script>
    <script type='text/javascript' src='/javascripts/form-pickers.init.js'></script>
    <script type='text/javascript' src='/javascripts/jquery.disableAutoFill.min.js'></script>

</body>

</html>
