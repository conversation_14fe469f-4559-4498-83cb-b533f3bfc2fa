<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submissionForm">
                            {{#ifEquals landOfficer.status 'CONFLICT'}}
                                {{>ps-submission/ps-conflict-submission-form landOfficer=landOfficer}}
                            {{else}}
                                {{>ps-submission/ps-submission-form landOfficer=landOfficer}}
                            {{/ifEquals}}
                        </form>
                        <div class="d-flex justify-content-between">

                            <a href="/ps-submissions/dashboard"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>

                            {{#if canComplete}}
                                <button
                                        style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                        type="button" data-status="complete-button" data-toggle="modal"
                                        data-officer="ps-officer"
                                        data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                        class="btn btn-secondary width-lg waves-effect waves-light">
                                    COMPLETE
                                </button>
                            {{else}}

                                {{#ifEquals landOfficer.status 'CONFLICT'}}
                                    <button
                                            style="color: rgb(255, 255, 255); background-color: rgb(243, 161, 8); border-color: #f0b906;"
                                            id="additionalButton" type="button" data-status="additional-button"
                                            data-toggle="modal" data-officer="ps-officer"
                                            data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                            class="btn btn-secondary width-lg waves-effect waves-light">
                                        Additional Information
                                    </button>
                                    <button
                                            style="color: rgb(255, 255, 255); background-color: rgb(230, 7, 7); border-color: #d10f0f;"
                                            type="button" id="declineButton" data-status="decline-button"
                                            data-toggle="modal" data-officer="ps-officer"
                                            data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                            class="btn btn-secondary width-lg waves-effect waves-light">
                                        Decline
                                    </button>
                                    <button
                                            style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                            type="button" data-status="pending-ps-button" data-toggle="modal"
                                            data-officer="ps-officer"
                                            data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                            class="btn btn-secondary width-lg waves-effect waves-light">
                                        Approve
                                    </button>
                                {{else}}

                                    {{#ifCond landOfficer.status '!==' 'COMPLETED' }}
                                        {{#if landOfficer.hasInterestConflicts}}
                                            <button
                                                    style="color: rgb(255, 255, 255); background-color: rgb(243, 161, 8); border-color: #f0b906;"
                                                    id="sendConflictBtn" type="button" data-status="send-back-conflict-button"
                                                    data-toggle="modal"
                                                    data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                                    data-officer="ps-officer"
                                                    class="btn btn-secondary width-lg waves-effect waves-light">
                                                Send back to conflict
                                            </button>
                                        {{else}}
                                            <button
                                                    style="color: rgb(255, 255, 255); background-color: rgb(243, 161, 8); border-color: #f0b906;"
                                                    id="sendStampDutyBtn" type="button" data-status="send-stamp-duty-button"
                                                    data-toggle="modal"
                                                    data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                                    data-officer="ps-officer"
                                                    class="btn btn-secondary width-lg waves-effect waves-light">
                                                Send to Stamp Duty
                                            </button>
                                        {{/if}}

                                        <button
                                                style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                                type="button" data-status="approve-ps-button" data-toggle="modal"
                                                data-officer="ps-officer"
                                                data-target="#landRegistryModal" data-id="{{landOfficer._id}}"
                                                class="btn btn-secondary width-lg waves-effect waves-light">
                                            Approve
                                        </button>

                                    {{/ifCond}}

                                {{/ifEquals}}

                            {{/if}}


                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>modals/upload-documents-modal}}
{{>modals/download-file-modal}}
{{>modals/land-registry-officer-modal}}
<script type="text/javascript">

</script>
