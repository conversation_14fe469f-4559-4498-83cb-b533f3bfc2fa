const ExemptionModel = require('../models/exemptions');
const ImportDutyWaiverApplicationModel = require('../models/importDutyWaiverApplication').ImportDutyWaiverApplicationModel;
const StampDutyApplicationModel = require('../models/stampDutyExemptionApplication').StampDutyApplicationModel;
const ReductionApplicationModel = require('../models/stampDutyReductionApplication').ReductionApplicationModel;
const MailController = require('../controllers/mailController');
const MailFormatter = require('../controllers/mailFormatController');
const { islands, calendar } = require('../constants');
const httpConstants = require('http2').constants;
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');
const statusCardHolderHelper = require('../shared/statusCardHolder.helper');
const DeclineNoticeController = require('../controllers/declineNoticesController');

// Helper function to determine if submission has pending actions
const hasActionsPending = (submission, type) => {
    // For Stamp Duty Officer, actions are available based on status and type
    if (type === 'exemption') {
        // For exemptions (landOfficers), actions are available when status allows operations
        const canComplete = (submission.status === "APPROVED PS" || submission.status === "TRANSFER COMPLETED");
        const cantEdit = (submission.status === "DECLINED" || submission.status === "COMPLETED" ||
            submission.status === "PENDING PS" || submission.status === "REQUESTED" || submission.status === "TRANSFER PENDING" || (submission.status === "RETURNED BY PS" && submission.hasInterestConflicts));
        return canComplete || !cantEdit;
    } else if (type === 'stampDutyExemption' || type === 'reductionApplication') {
        // For stamp duty exemptions and reductions, actions are available when NOT complete
        // isComplete = 'COMPLETED' == status || 'DECLINED' == status
        // So actions are available when status is NOT 'COMPLETED' and NOT 'DECLINED'
        const completedStatuses = ['COMPLETED', 'DECLINED'];
        return !completedStatuses.includes(submission.status);
    } else if (type === 'importDutyWaiver') {
        // For import duty waivers, actions are always disabled
        return false;
    }
    return false;
};

exports.stampDutyDashboard = async function (req, res) {
    try {
        let query = [];
        let stampDutyApplicationQuery = [];

        let landOfficers = [];
        let stampDutyExemptions = [];
        let importDutyWaiverApplications = [];
        let reductionApplications = [];

        if (req.body.auditReady === 'Yes') {
            query.push({ auditReady: true });
            stampDutyApplicationQuery.push({ auditReady: true });
        }
        if (req.body.auditReady === 'No') {
            query.push({ auditReady: { $ne: true } });
            stampDutyApplicationQuery.push({ auditReady: { $ne: true } });
        }

        let status = {
            "declined": "DECLINED",
            "completed": "COMPLETED",
            "submitted": ["SUBMITTED",],
            "request-information": "REQUESTED",
            "pending-ps": { "$in": ['PENDING PS', 'TRANSFER PENDING'] },
            "approved-ps": { "$in": ['RETURNED BY PS', 'APPROVED PS'] }
        };
        let exemptionApplicationStatus = {
            "declined": "DECLINED",
            "completed": "COMPLETED",
            "submitted": { "$in": ['NOT STARTED', 'SAVED STAMP DUTY OFFICER', 'RETURNED BY PS'] },
            "approved-ps": { "$in": ['APPROVED'] }
        };

        let filters = {
            "searchFilter": req.body.searchFilter,
            "submittedEnd": req.body.submittedEnd,
            "submittedStart": req.body.submittedStart,
            "exemptionType": req.body.exemptionType,
            "auditReady": req.body.auditReady,
            "showPendingActions": !!req.body.showPendingActions,
        };
        let submissionDate = {};
        if (req.body.submittedStart) {
            submissionDate["createdAt"] = {
                $gte: req.body.submittedStart,
                $lte: req.body.submittedEnd ? moment(req.body.submittedEnd).add(1, 'd').toDate() : new Date(),
            };
            stampDutyApplicationQuery.push(submissionDate);
            query.push(submissionDate);
        } else if (req.body.submittedEnd) {
            submissionDate["createdAt"] = { $lte: moment(req.body.submittedEnd).add(1, 'd').toDate() };
            stampDutyApplicationQuery.push(submissionDate);
            query.push(submissionDate);
        }
        let exceptionType;

        if (req.params.status) {
            query.push({ "status": status[req.params.status] });
            stampDutyApplicationQuery.push({ "status": exemptionApplicationStatus[req.params.status] });
        }
        else {
            status.push('SUBMITTED', 'TRANSFER COMPLETED', 'REQUESTED');
            query.push({ "status": { $in: status } });
            stampDutyApplicationQuery.push({ "status": { $in: ["NOT STARTED", "SAVED STAMP DUTY OFFICER"] } });
        }

        if (req.body.exemptionType === 'love-and-affection') {
            exceptionType = 'Natural Love & Affection';
        } else if (req.body.exemptionType === 'section-exemptions') {
            exceptionType = 'Section 23 & 28 Exemptions';
        } else if (req.body.exemptionType === 'charitable-institution') {
            exceptionType = 'Transfers to Charitable Institutions';
        }
        else if (req.body.exemptionType === 'transmission') {
            exceptionType = 'Transmission';
        }
        else if (req.body.exemptionType === 'refunds') {
            exceptionType = 'Refunds'
        }
        else if (req.body.exemptionType === 'remissions') {
            exceptionType = 'Remissions'
        }
        else if (req.body.exemptionType === 'Import Duty Waiver') {
            exceptionType = 'Import Duty Waiver'
        }
        else if (req.body.exemptionType === 'Stamp Duty Reduction') {
            exceptionType = 'Stamp Duty Reduction'
        }
        else if (req.body.exemptionType === 'exemption-application') {
            exceptionType = 'Stamp Duty Exemption'
        }

        if (exceptionType) {
            query.push({ "exemptionType": exceptionType });
        }

        if (req.body.searchFilter && req.body.searchFilter.length > 2) {
            const searchText = {
                $or: [{ 'transferorName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'transfereeName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'parcelNumber': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'parcelTextNumber': { $regex: req.body.searchFilter, $options: 'i' } }]
            };
            query.push(searchText);
            const searchText2 = {
                $or: [
                    { 'firstName': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'lastName': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'applicantDetails.firstName': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'applicantDetails.lastName': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'companyDetails.name': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'propertyDetails.parcel': { $regex: req.body.searchFilter, $options: 'i' } }]
            };
            stampDutyApplicationQuery.push(searchText2);

        }

        if (!filters.exemptionType || (filters.exemptionType !== "exemption-application" &&
            req.body.exemptionType !== 'Import Duty Waiver' && filters.exemptionType !== 'Stamp Duty Reduction')
            || filters.exemptionType === 'all') {
            landOfficers = await ExemptionModel.find({ $and: query }).limit(1000);
        }

        if (!filters.exemptionType || filters.exemptionType === 'exemption-application' || filters.exemptionType === 'all') {
            stampDutyExemptions = await StampDutyApplicationModel.find({ $and: stampDutyApplicationQuery }).limit(1000);
        }

        if ((!req.body.exemptionType || req.body.exemptionType === 'Import Duty Waiver' || filters.exemptionType === 'all') &&
            (req.params.status === 'declined' ||
                req.params.status === 'approved-ps')) {
            let importDutyWaiverStatus = {
                "declined": "DECLINED BY CUSTOMS OFFICER",
                "approved-ps": "APPROVED",
            };
            importDutyWaiverApplications = await ImportDutyWaiverApplicationModel.find({ status: importDutyWaiverStatus[req.params.status] });
        }

        if (!filters.exemptionType || filters.exemptionType === 'Stamp Duty Reduction' || filters.exemptionType === 'all') {
            reductionApplications = await ReductionApplicationModel.find({ $and: stampDutyApplicationQuery }).limit(1000);
        }

        // Add hasActionsPending property to each submission
        landOfficers = landOfficers.map((submission) => {
            const s = { ...submission._doc };
            s.hasActionsPending = hasActionsPending(submission, 'exemption');
            return s;
        });

        stampDutyExemptions = stampDutyExemptions.map((submission) => {
            const s = { ...submission._doc };
            s.hasActionsPending = hasActionsPending(submission, 'stampDutyExemption');
            return s;
        });

        importDutyWaiverApplications = importDutyWaiverApplications.map((submission) => {
            const s = { ...submission._doc };
            s.hasActionsPending = hasActionsPending(submission, 'importDutyWaiver');
            return s;
        });

        reductionApplications = reductionApplications.map((submission) => {
            const s = { ...submission._doc };
            s.hasActionsPending = hasActionsPending(submission, 'reductionApplication');
            return s;
        });

        // Combine all submissions for unified sorting
        let allSubmissions = [];

        // Add exemptions
        allSubmissions = allSubmissions.concat(landOfficers.map(item => ({
            ...(item._doc || item),
            type: 'exemption',
            rowClass: 'exemptions-row'
        })));

        // Add stamp duty exemptions
        allSubmissions = allSubmissions.concat(stampDutyExemptions.map(item => ({
            ...(item._doc || item),
            type: 'stampDutyExemption',
            rowClass: 'exemptions-application-row'
        })));

        // Add import duty waivers
        allSubmissions = allSubmissions.concat(importDutyWaiverApplications.map(item => ({
            ...(item._doc || item),
            type: 'importDutyWaiver',
            rowClass: 'duty-waiver-row'
        })));

        // Add reduction applications
        allSubmissions = allSubmissions.concat(reductionApplications.map(item => ({
            ...(item._doc || item),
            type: 'reductionApplication',
            rowClass: 'reduction-application-row'
        })));

        // Sort submissions by hasActionsPending first (true first), then by createdAt descending (newest first)
        allSubmissions.sort((a, b) => {
            // Convert boolean to number for sorting (true = 1, false = 0)
            const aHasActions = a.hasActionsPending ? 1 : 0;
            const bHasActions = b.hasActionsPending ? 1 : 0;

            // First sort by hasActionsPending (1 comes before 0, so pending actions first)
            if (aHasActions !== bHasActions) {
                return bHasActions - aHasActions;
            }

            // Then sort by createdAt descending (newest first)
            const aDate = new Date(a.createdAt);
            const bDate = new Date(b.createdAt);
            return bDate - aDate;
        });

        if (req.body.showPendingActions) {
            allSubmissions = allSubmissions.filter(submission => submission.hasActionsPending);
        }

        landOfficers = allSubmissions.filter(s => s.type === 'exemption');
        stampDutyExemptions = allSubmissions.filter(s => s.type === 'stampDutyExemption');
        importDutyWaiverApplications = allSubmissions.filter(s => s.type === 'importDutyWaiver');
        reductionApplications = allSubmissions.filter(s => s.type === 'reductionApplication');

        res.render('stamp-duty/stamp-duty-dashboard',
            {
                landOfficers: landOfficers,
                stampDutyExemptions: stampDutyExemptions,
                filters: filters,
                status: req.params.status,
                importDutyWaiverApplications,
                reductionApplications,
                allSubmissions: allSubmissions
            });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/stamp-duty/dashboard');
    }
};

exports.superSearchDashboard = async function (req, res) {
    try {
        let query = [{}];
        let stampDutyApplicationQuery = [{}];
        let landOfficers = [];
        let stampDutyExemptions = [];
        let importDutyWaiverApplications = [];
        let reductionApplications = [];

        if (req.body.auditReady === 'Yes') {
            query.push({ auditReady: true });
            stampDutyApplicationQuery.push({ auditReady: true });
        }
        if (req.body.auditReady === 'No') {
            query.push({ auditReady: { $ne: true } });
            stampDutyApplicationQuery.push({ auditReady: { $ne: true } });
        }

        let filters = {
            "searchFilter": req.body.searchFilter,
            "submittedEnd": req.body.submittedEnd,
            "submittedStart": req.body.submittedStart,
            "exemptionType": req.body.exemptionType,
            "auditReady": req.body.auditReady,
            "showPendingActions": !!req.body.showPendingActions,
        };
        let submissionDate = {};
        if (req.body.submittedStart) {
            submissionDate["createdAt"] = {
                $gte: req.body.submittedStart,
                $lte: req.body.submittedEnd ? moment(req.body.submittedEnd).add(1, 'd').toDate() : new Date(),
            };
            query.push(submissionDate);
            stampDutyApplicationQuery.push(submissionDate);
        } else if (req.body.submittedEnd) {
            submissionDate["createdAt"] = { $lte: moment(req.body.submittedEnd).add(1, 'd').toDate() };
            query.push(submissionDate);
            stampDutyApplicationQuery.push(submissionDate);
        }
        let exceptionType;

        if (req.body.exemptionType === 'love-and-affection') {
            exceptionType = 'Natural Love & Affection';
        } else if (req.body.exemptionType === 'section-exemptions') {
            exceptionType = 'Section 23 & 28 Exemptions';
        } else if (req.body.exemptionType === 'charitable-institution') {
            exceptionType = 'Transfers to Charitable Institutions';
        }
        else if (req.body.exemptionType === 'transmission') {
            exceptionType = 'Transmission';
        }
        else if (req.body.exemptionType === 'refunds') {
            exceptionType = 'Refunds'
        }
        else if (req.body.exemptionType === 'remissions') {
            exceptionType = 'Remissions'
        }
        else if (req.body.exemptionType === 'Import Duty Waiver') {
            exceptionType = 'Import Duty Waiver'
        }
        else if (req.body.exemptionType === 'Stamp Duty Reduction') {
            exceptionType = 'Stamp Duty Reduction'
        }
        else if (req.body.exemptionType === 'exemption-application') {
            exceptionType = 'Stamp Duty Exemption'
        }

        if (exceptionType) {
            query.push({ "exemptionType": exceptionType });
        }

        if (req.body.searchFilter && req.body.searchFilter.length > 2) {
            const searchText = {
                $or: [{ 'transferorName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'transfereeName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'parcelNumber': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'parcelTextNumber': { $regex: req.body.searchFilter, $options: 'i' } }]
            };
            query.push(searchText);
            const searchText2 = {
                $or: [
                    { 'firstName': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'lastName': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'applicantDetails.firstName': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'applicantDetails.lastName': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'companyDetails.name': { $regex: req.body.searchFilter, $options: 'i' } },
                    { 'propertyDetails.parcel': { $regex: req.body.searchFilter, $options: 'i' } }
                ]
            };
            stampDutyApplicationQuery.push(searchText2);
        }

        if (!filters.exemptionType || (filters.exemptionType !== "exemption-application" &&
            req.body.exemptionType !== 'Import Duty Waiver' && filters.exemptionType !== 'Stamp Duty Reduction')
            || filters.exemptionType === 'all') {
            landOfficers = await ExemptionModel.find({ $and: query });
        }

        if (!filters.exemptionType || filters.exemptionType === 'exemption-application' || filters.exemptionType === 'all') {
            stampDutyExemptions = await StampDutyApplicationModel.find({ $and: stampDutyApplicationQuery });
        }
        if (!req.body.exemptionType || req.body.exemptionType === 'Import Duty Waiver' || filters.exemptionType === 'all') {
            importDutyWaiverApplications = await ImportDutyWaiverApplicationModel.find({ $and: stampDutyApplicationQuery });
        }
        if (!filters.exemptionType || filters.exemptionType === 'Stamp Duty Reduction' || filters.exemptionType === 'all') {
            reductionApplications = await ReductionApplicationModel.find({ $and: stampDutyApplicationQuery });
        }

        // Add hasActionsPending property to each submission
        landOfficers = landOfficers.map((submission) => {
            const s = { ...submission._doc };
            s.hasActionsPending = hasActionsPending(submission, 'exemption');
            return s;
        });

        stampDutyExemptions = stampDutyExemptions.map((submission) => {
            const s = { ...submission._doc };
            s.hasActionsPending = hasActionsPending(submission, 'stampDutyExemption');
            return s;
        });

        importDutyWaiverApplications = importDutyWaiverApplications.map((submission) => {
            const s = { ...submission._doc };
            s.hasActionsPending = hasActionsPending(submission, 'importDutyWaiver');
            return s;
        });

        reductionApplications = reductionApplications.map((submission) => {
            const s = { ...submission._doc };
            s.hasActionsPending = hasActionsPending(submission, 'reductionApplication');
            return s;
        });

        // Combine all submissions for unified sorting
        let allSubmissions = [];

        // Add exemptions
        allSubmissions = allSubmissions.concat(landOfficers.map(item => ({
            ...(item._doc || item),
            type: 'exemption',
            rowClass: 'exemptions-row'
        })));

        // Add stamp duty exemptions
        allSubmissions = allSubmissions.concat(stampDutyExemptions.map(item => ({
            ...(item._doc || item),
            type: 'stampDutyExemption',
            rowClass: 'exemptions-application-row'
        })));

        // Add import duty waivers
        allSubmissions = allSubmissions.concat(importDutyWaiverApplications.map(item => ({
            ...(item._doc || item),
            type: 'importDutyWaiver',
            rowClass: 'duty-waiver-row'
        })));

        // Add reduction applications
        allSubmissions = allSubmissions.concat(reductionApplications.map(item => ({
            ...(item._doc || item),
            type: 'reductionApplication',
            rowClass: 'reduction-application-row'
        })));

        // Sort submissions by hasActionsPending first (true first), then by createdAt descending (newest first)
        allSubmissions.sort((a, b) => {
            // Convert boolean to number for sorting (true = 1, false = 0)
            const aHasActions = a.hasActionsPending ? 1 : 0;
            const bHasActions = b.hasActionsPending ? 1 : 0;

            // First sort by hasActionsPending (1 comes before 0, so pending actions first)
            if (aHasActions !== bHasActions) {
                return bHasActions - aHasActions;
            }

            // Then sort by createdAt descending (newest first)
            const aDate = new Date(a.createdAt);
            const bDate = new Date(b.createdAt);
            return bDate - aDate;
        });

        // Apply "Show only pending actions" filter after sorting
        if (req.body.showPendingActions) {
            allSubmissions = allSubmissions.filter(submission => submission.hasActionsPending);
        }

        // Clear individual arrays and use the combined sorted array
        landOfficers = allSubmissions.filter(s => s.type === 'exemption');
        stampDutyExemptions = allSubmissions.filter(s => s.type === 'stampDutyExemption');
        importDutyWaiverApplications = allSubmissions.filter(s => s.type === 'importDutyWaiver');
        reductionApplications = allSubmissions.filter(s => s.type === 'reductionApplication');

        res.render('stamp-duty/stamp-duty-dashboard',
            {
                landOfficers,
                filters,
                stampDutyExemptions,
                importDutyWaiverApplications,
                reductionApplications,
                allSubmissions: allSubmissions
            });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/stamp-duty/dashboard');
    }
};


exports.getDashboard = async function (req, res) {
    try {
        // Count submitted submissions that need Stamp Duty Officer action
        let submittedCount = 0;
        try {
            // Count ExemptionModel submissions with SUBMITTED status
            const exemptionSubmittedCount = await ExemptionModel.countDocuments({
                status: 'SUBMITTED'
            });

            // Count StampDutyApplicationModel submissions with SUBMITTED status
            const stampDutySubmittedCount = await StampDutyApplicationModel.countDocuments({
                status: { "$in": ['NOT STARTED', 'SAVED STAMP DUTY OFFICER', 'RETURNED BY PS'] }
            });

            // Count ReductionApplicationModel submissions with SUBMITTED status
            const reductionSubmittedCount = await ReductionApplicationModel.countDocuments({
                status: { "$in": ['NOT STARTED', 'SAVED STAMP DUTY OFFICER', 'RETURNED BY PS'] }
            });


            submittedCount = exemptionSubmittedCount + stampDutySubmittedCount + reductionSubmittedCount;
        } catch (e) {
            console.log("Error counting submitted submissions: ", e);
            submittedCount = 0;
        }

        // Count approved/returned by PS submissions that need Stamp Duty Officer action
        let approvedPsCount = 0;
        try {
            // Count ExemptionModel submissions with APPROVED PS or RETURNED BY PS status
            const exemptionApprovedPsCount = await ExemptionModel.countDocuments({
                status: { "$in": ['RETURNED BY PS', 'APPROVED PS'] }
            });

            // Count StampDutyApplicationModel submissions with APPROVED PS or RETURNED BY PS status
            const stampDutyApprovedPsCount = await StampDutyApplicationModel.countDocuments({
                status: "APPROVED"
            });

            // Count ReductionApplicationModel submissions with APPROVED PS or RETURNED BY PS status
            const reductionApprovedPsCount = await ReductionApplicationModel.countDocuments({
                status: "APPROVED"
            });

            console.log("exemptionApprovedPsCount", exemptionApprovedPsCount);
            console.log("stampDutyApprovedPsCount", stampDutyApprovedPsCount);
            console.log("reductionApprovedPsCount", reductionApprovedPsCount);

            approvedPsCount = exemptionApprovedPsCount + stampDutyApprovedPsCount + reductionApprovedPsCount;
        } catch (e) {
            console.log("Error counting approved/returned by PS submissions: ", e);
            approvedPsCount = 0;
        }

        res.render('stamp-duty/status-dashboard', {
            user: req.session.user,
            submittedCount: submittedCount,
            approvedPsCount: approvedPsCount
        });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/stamp-duty/dashboard');
    }
};

exports.getStampDutyView = async function (req, res) {
    try {
        req.session.files = {};
        const landOfficer = await ExemptionModel.findById(req.params.stampDutyId);
        const canComplete = landOfficer && (landOfficer.status === "APPROVED PS" || landOfficer.status === "TRANSFER COMPLETED");
        const cantEdit = landOfficer && (landOfficer.status === "DECLINED" || landOfficer.status === "COMPLETED" ||
            landOfficer.status === "PENDING PS" || landOfficer.status === "REQUESTED" || landOfficer.status === "TRANSFER PENDING" || (landOfficer.status === "RETURNED BY PS" && landOfficer.hasInterestConflicts));

        const isRemissionApproved = landOfficer && landOfficer.exemptionType === 'Remissions' &&
            (landOfficer.status === "APPROVED PS" || landOfficer.status === "COMPLETED");
        const cantEditRemission = landOfficer && landOfficer.status !== "APPROVED PS";
        res.render('stamp-duty/open-stamp-duty-form',
            {
                landOfficer: landOfficer,
                canComplete: canComplete,
                cantEdit: cantEdit,
                isRemissionApproved,
                cantEditRemission
            });

    } catch (e) {
        console.log(e);
        return res.status(500).end();
    }
};

exports.getImportDutyWaiverView = async function (req, res) {
    try {
        req.session.files = {};
        const application = await ImportDutyWaiverApplicationModel.findById(req.params.stampDutyId);
        application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
        application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];
        const filesInformation = {
            affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.affidavit : false },
            marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.marriageCertificates : false },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.birthCertificate : false },
            parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.parentsDocuments : false },
            itemsInvoices: { name: "Copies of invoices", files: application.itemsInvoices, filesCount: application.itemsInvoices.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.itemsInvoices : false },
            buildingPictures: { name: "Building photographs", files: application.buildingPictures, filesCount: application.buildingPictures.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.buildingPictures : false },
            buildingPermit: { name: "Building permit", files: application.buildingPermit, filesCount: application.buildingPermit.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.buildingPermit : false },
            landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.landRegister : false },
            valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.valuation : false },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.statusCard : false },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations?.botcCertificate : false },
        }
        let applicants = [{
            firstName: application.firstName,
            lastName: application.lastName,
            dateOfBirth: application.dateOfBirth,
            statusCardNumber: application.statusCardNumber,
        }];

        if (application.applicants && application.applicants.length) {
            applicants.push(...application.applicants.map(ap => {
                return {
                    firstName: ap.firstName,
                    lastName: ap.lastName,
                    dateOfBirth: ap.dateOfBirth,
                }
            }));
        }


        applicants = await Promise.all(applicants.map(async (ap) => {
            // Search in import duty waiver submissions
            const previousImportDutyApplications = await ImportDutyWaiverApplicationModel.find({
                '$or': [
                    {
                        'lastName': ap.lastName,
                        'dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'applicants.lastName': ap.lastName,
                        'applicants.dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
                    }
                ],
            });
            // Search in import duty waiver submissions
            const previousStampDutyExemptionApplications = await StampDutyApplicationModel.find({
                '$or': [
                    {
                        'lastName': ap.lastName,
                        'dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'applicants.lastName': ap.lastName,
                        'applicants.dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
                    }
                ],
            });
            ap.previousApplications = {
                importDuty: previousImportDutyApplications,
                stampDutyExemption: previousStampDutyExemptionApplications,
            }
            ap.totalValue =
                [
                    previousImportDutyApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
                    previousStampDutyExemptionApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
                ].reduce((a, b) => a + b, 0);
            return ap;
        }));


        res.render('stamp-duty/open-import-duty-waiver-form',
            {
                application: application,
                validations: application.deputyCommissionerTier ? application.deputyCommissionerTier.validations : null,
                islands,
                calendar,
                applicants,
                filesInformation
            });

    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.updateStampDutyAuditor = async function (req, res) {
    try {
        const application = await ExemptionModel.findById(req.params.stampDutyId);
        if (!application) {
            throw new Error('Not found');
        }
        application.auditReady = req.body.auditReady === 'Yes';
        await application.save();
        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, status: httpConstants.HTTP_STATUS_OK });
    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.saveImportDutyWaiverApplication = async function (req, res) {
    try {
        const application = await ImportDutyWaiverApplicationModel.findById(req.params.stampDutyId);
        if (!application) {
            throw new Error('Not found');
        }
        application.auditReady = req.body.auditReady === 'Yes';
        await application.save();
        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, status: httpConstants.HTTP_STATUS_OK });
    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.getStampDutyExemptionApplicationView = async function (req, res) {
    try {
        req.session.files = {};
        const application = await StampDutyApplicationModel.findById(req.params.stampDutyId);
        application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
        application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];


        const filesInformation = {
            affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.affidavit : false },
            marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.marriageCertificates : false },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.birthCertificate : false },
            parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.parentsDocuments : false },
            signedAgreement: { name: "Signed agreement of the purchase of the property", files: application.signedAgreement, filesCount: application.signedAgreement.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.signedAgreement : false },
            landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.landRegister : false },
            valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.valuation : false },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.statusCard : false },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.botcCertificate : false },
        }
        let applicants = [{
            firstName: application.firstName,
            lastName: application.lastName,
            dateOfBirth: application.dateOfBirth,
            statusCardNumber: application.statusCardNumber,
        }];

        if (application.applicants && application.applicants.length) {
            applicants.push(...application.applicants.map(ap => {
                return {
                    firstName: ap.firstName,
                    lastName: ap.lastName,
                    dateOfBirth: ap.dateOfBirth,
                }
            }));
        }


        applicants = await Promise.all(applicants.map(async (ap) => {
            // Search in import duty waiver submissions
            const previousImportDutyApplications = await ImportDutyWaiverApplicationModel.find({
                '$or': [
                    {
                        'lastName': ap.lastName,
                        'dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'applicants.lastName': ap.lastName,
                        'applicants.dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
                    }
                ],
            });
            // Search in import duty waiver submissions
            const previousStampDutyExemptionApplications = await StampDutyApplicationModel.find({
                '$or': [
                    {
                        'lastName': ap.lastName,
                        'dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'applicants.lastName': ap.lastName,
                        'applicants.dateOfBirth': ap.dateOfBirth,
                    },
                    {
                        'statusCardNumber': { '$exists': true, '$nin': ["", null], '$eq': ap.statusCardNumber },
                    }
                ],
            });
            ap.previousApplications = {
                importDuty: previousImportDutyApplications,
                stampDutyExemption: previousStampDutyExemptionApplications,
            };
            ap.totalValue =
                [
                    previousImportDutyApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
                    previousStampDutyExemptionApplications.reduce((a, b) => a + b.remittedAmount || 0, 0),
                ].reduce((a, b) => a + b, 0);
            return ap;
        }));

        const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatus(application);

        res.render('stamp-duty/open-stamp-duty-exemption-form',
            {
                application: application,
                validations: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations : null,
                islands,
                calendar,
                applicants,
                filesInformation,
                foundCardHolder,
                statusInformationColor,
                isNewApplication: ['NOT STARTED', 'SAVED STAMP DUTY OFFICER', 'RETURNED BY PS'].includes(application.status),
                isComplete: 'COMPLETED' == application.status || 'DECLINED' == application.status,
                completedLastRequest: application.informationRequests && application.informationRequests.length ?
                    !!application.informationRequests[application.informationRequests.length - 1].submittedAt : true,
                remissionCount: application.remissionOrder ? application.remissionOrder.length : 0,
                additionalInformationCount: application.additionalInformation ? application.additionalInformation.length : 0,
            });

    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.getFilesStampDutyExemptionApplication = async function (req, res) {
    try {
        let files = [];
        const application = await StampDutyApplicationModel.findById(req.params.stampDutyId);
        if (req.query.fileGroup === 'remission-order') {
            files = application.remissionOrder ? application.remissionOrder : [];
        }
        if (req.query.fileGroup === 'additional-information') {
            files = application.additionalInformation ? application.additionalInformation : [];
        }

        res.json({ status: httpConstants.HTTP_STATUS_OK, files });
    } catch (e) {
        console.log("error: ", e);
        res.redirect('/');
    }
};

exports.storeFilesStampDutyExemptionApplication = async function (req, res) {
    try {
        const application = await StampDutyApplicationModel.findById(req.params.stampDutyId);

        let uploadedFiles = req.files['fileUploaded'];

        const fileName = req.body.fileName.replace(/[^a-zA-Z0-9]/g, "");
        if (uploadedFiles && uploadedFiles.length > 0) {
            if (!application.remissionOrder) {
                application.remissionOrder = [];
            }
            if (!application.additionalInformation) {
                application.additionalInformation = [];
            }

            uploadedFiles = uploadedFiles.map((itemToUpload) => {
                return {
                    fileId: uuidv4(),
                    fieldName: itemToUpload.fieldname.replace(/fileUploaded/i, fileName),
                    blob: itemToUpload.blob.replace(/fileUploaded/i, fileName),
                    blobName: itemToUpload.blobName,
                    url: itemToUpload.url,
                    originalName: itemToUpload.originalname,
                    encoding: itemToUpload.encoding,
                    mimeType: itemToUpload.mimetype,
                    container: itemToUpload.container,
                    blobType: itemToUpload.blobType,
                    size: itemToUpload.size,
                    etag: itemToUpload.etag
                };
            });

            if (req.body.fileGroup === "additional-information") {
                application.additionalInformation = application.additionalInformation.concat(uploadedFiles);
            } else if (req.body.fileGroup === "remission-order") {
                application.remissionOrder = application.remissionOrder.concat(uploadedFiles);
            }

            await application.save();

            res.sendStatus(httpConstants.HTTP_STATUS_OK)
        }
    } catch (e) {
        return res.status(500).end();
    }

};

exports.deleteUploadStampDutyExemptionApplication = async function (req, res) {
    const application = await StampDutyApplicationModel.findById(req.params.stampDutyId);

    if (req.body.group === "remission-order") {
        let files = application.remissionOrder.filter((f) => f.fileId != req.body.fileId);
        application.remissionOrder = files;
    }
    if (req.body.group === "additional-information") {
        let files = application.additionalInformation.filter((f) => f.fileId != req.body.fileId);
        application.additionalInformation = files;
    }
    await application.save();
    res.json({ status: httpConstants.HTTP_STATUS_OK, message: 'success' });
};

exports.updateStatus = async function (req, res) {
    try {
        let email;
        const statusCodes = {
            "pending-ps-button": 'PENDING PS',
            "decline-button": 'DECLINED',
            "additional-button": 'REQUESTED',
            "send-stamp-duty-button": 'RETURNED BY PS',
            "approve-ps-button": 'APPROVED PS',
            "complete-button": 'COMPLETED',
            "complete-transfer-button": "TRANSFER COMPLETED",
            "conflict-button": "CONFLICT",
            "send-back-conflict-button": 'CONFLICT',
        };
        let landOfficer = await ExemptionModel.findById(req.params.stampDutyId);
        if (landOfficer) {
            landOfficer.status = statusCodes[req.body.status];
            if (landOfficer.status === "APPROVED PS" && landOfficer.type === "FINANCE") {
                landOfficer.status = "TRANSFER PENDING";
            }

            if (landOfficer.status === "CONFLICT") {
                landOfficer.hasInterestConflicts = true
            }

            const officer = {
                username: req.session.user.name,
                email: req.session.user.username,
                submittedDate: new Date(),
                comment: req.body.comment,
            };

            if (req.body.officer && req.body.officer === "stamp-duty-officer") {
                landOfficer.stampDutyOfficer = officer;
            } else if (req.body.officer && req.body.officer === "ps-officer") {
                landOfficer.psOfficer = officer;
            }

            landOfficer.comments.push({
                email: req.session.user.username,
                date: new Date(),
                commentStatus: landOfficer.status,
                comment: req.body.comment
            });

            if (landOfficer.status === "COMPLETED") {
                landOfficer.completedAt = new Date();
            }

            await landOfficer.save();
            let recipient = [];
            switch (landOfficer.status) {
                case 'REQUESTED':
                    email = MailFormatter.generateEmailInformationRequested(landOfficer);
                    if (landOfficer.type === "LAND SUBMISSION") {
                        recipient.push(process.env.EMAIL_LAND_OFFICER_RECIPIENT);
                    } else {
                        recipient.push(process.env.EMAIL_FINANCE_OFFICER_RECIPIENT);
                    }

                    break;
                case 'DECLINED':
                    email = MailFormatter.generateEmailDeclined(landOfficer, req.body.comment);
                    if (landOfficer.type === "LAND SUBMISSION") {
                        recipient.push(process.env.EMAIL_LAND_OFFICER_RECIPIENT);
                    } else {
                        recipient.push(process.env.EMAIL_FINANCE_OFFICER_RECIPIENT);
                    }
                    break;
                case 'APPROVED PS':
                    email = MailFormatter.generateEmailApprovedPS(landOfficer);
                    recipient.push(process.env.EMAIL_STAMP_DUTY_OFFICER_RECIPIENT);
                    break;
                case 'TRANSFER PENDING':
                    email = MailFormatter.generateEmailApprovedPS(landOfficer);
                    recipient.push(process.env.EMAIL_FINANCE_OFFICER_RECIPIENT);
                    break;
                case 'TRANSFER COMPLETED':
                    email = MailFormatter.generateEmailTransferCompleted(landOfficer);
                    recipient.push(process.env.EMAIL_PS_OFFICER_RECIPIENT);
                    break;
                case 'CONFLICT':
                    email = MailFormatter.generateEmailConflict(landOfficer);
                    recipient.push(process.env.EMAIL_PS_OFFICER_RECIPIENT);
                    break;
                case 'COMPLETED':
                    email = MailFormatter.generateEmailCompleted(landOfficer);
                    recipient.push(process.env.EMAIL_STAMP_DUTY_OFFICER_RECIPIENT);
                    if (landOfficer.type === "LAND SUBMISSION") {
                        recipient.push(process.env.EMAIL_LAND_OFFICER_RECIPIENT);
                    }
                    else {
                        recipient.push(process.env.EMAIL_FINANCE_OFFICER_RECIPIENT);
                    }
                    break;
                default:
                    break;
            }

            if (email) {
                await MailController.asyncSend(recipient,
                    'Stamp Duty Exemption Program',
                    email.textString,
                    email.htmlString
                );
            }
        }
        return res.status(200).json({ "success": true })

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/');
    }
};

exports.validateInformation = async function (req, res) {
    try {
        const landOfficer = await ExemptionModel.findById(req.params.stampDutyId);

        if (landOfficer) {
            let isCompleteValidated = true;

            landOfficer.validatedInformation = !!req.body.informationValidated;
            if (!req.body.informationValidated) {
                isCompleteValidated = false;
            }

            landOfficer.files.forEach((file) => {

                file.validated = !!(req.body.files[file.id] && req.body.files[file.id]["validated"]);

                if (!file.validated) {
                    isCompleteValidated = false;
                }
            });
            await landOfficer.save();
            return res.status(200).json({ "success": true, "isCompleteValidated": isCompleteValidated })
        } else {
            return res.status(404).json({ "success": false })
        }
    } catch (e) {
        console.log("error: ", e);
        res.redirect('/stamp-duty/dashboard');
    }
};


exports.saveStampDutyExemptionApplication = async function (req, res) {
    try {
        const submission = await StampDutyApplicationModel.findById(req.params.stampDutyId);

        const validation = {
            applicantDetails: req.body.validatedApplicantDetails === 'Yes',
            personalDetails: req.body.validatedPersonalDetails === 'Yes',
            additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
            propertyDetails: req.body.validatedPropertyDetails === 'Yes',
            sellersDetails: req.body.validatedSellersDetails === 'Yes',
            affidavit: req.body.validated_affidavit === 'Yes',
            marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
            birthCertificate: req.body.validated_birthCertificate === 'Yes',
            parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
            signedAgreement: req.body.validated_signedAgreement === 'Yes',
            landRegister: req.body.validated_landRegister === 'Yes',
            valuation: req.body.validated_valuation === 'Yes',
            statusCard: req.body.validated_statusCard === 'Yes',
            botcCertificate: req.body.validated_botcCertificate === 'Yes',
        };

        await StampDutyApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.validations': validation,
            'stampDutyOfficerTier.username': req.session.user.username,
            status: submission.status === 'APPROVED' || submission.status === 'RETURNED BY PS' ? submission.status : 'SAVED STAMP DUTY OFFICER',
            auditReady: req.body.auditReady === 'Yes',
            remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
        });



        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, status: httpConstants.HTTP_STATUS_OK });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/stamp-duty/dashboard');
    }
};

exports.approveStampDutyExemptionApplication = async function (req, res) {
    try {
        const validation = {
            applicantDetails: req.body.validatedApplicantDetails === 'Yes',
            personalDetails: req.body.validatedPersonalDetails === 'Yes',
            additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
            propertyDetails: req.body.validatedPropertyDetails === 'Yes',
            sellersDetails: req.body.validatedSellersDetails === 'Yes',
            affidavit: req.body.validated_affidavit === 'Yes',
            marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
            birthCertificate: req.body.validated_birthCertificate === 'Yes',
            parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
            signedAgreement: req.body.validated_signedAgreement === 'Yes',
            landRegister: req.body.validated_landRegister === 'Yes',
            valuation: req.body.validated_valuation === 'Yes',
            statusCard: req.body.validated_statusCard === 'Yes',
            botcCertificate: req.body.validated_botcCertificate === 'Yes',
        };

        const submission = await StampDutyApplicationModel.findById(req.params.stampDutyId);
        const comment = {
            date: new Date(),
            internalComments: req.body.internalComments,
            user: req.session.user.username,
            status: 'APPROVED',
        };
        if (submission.stampDutyOfficerTier && submission.stampDutyOfficerTier.comments && submission.stampDutyOfficerTier.comments.length) {
            submission.stampDutyOfficerTier.comments.push(comment);
        } else {
            submission.stampDutyOfficerTier = {
                comments: [comment],
            }
        }
        await StampDutyApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.validations': validation,
            'stampDutyOfficerTier.approvedAt': new Date(),
            'stampDutyOfficerTier.username': req.session.user.username,
            'stampDutyOfficerTier.comments': submission.stampDutyOfficerTier.comments,
            status: 'PENDING PS',
            auditReady: req.body.auditReady === 'Yes',
            remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
        });


        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_OK,
            "message": "Your application has been updated successfully"
        });

    } catch (e) {
        console.log("error: ", e);
        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "message": "There was an error updating the status of the application, try again later..."
        });
    }
};

exports.completeStampDutyExemptionApplication = async function (req, res) {
    try {
        const submission = await StampDutyApplicationModel.findById(req.params.stampDutyId);
        if (submission) {
            const validation = {
                applicantDetails: req.body.validatedApplicantDetails === 'Yes',
                personalDetails: req.body.validatedPersonalDetails === 'Yes',
                additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
                propertyDetails: req.body.validatedPropertyDetails === 'Yes',
                sellersDetails: req.body.validatedSellersDetails === 'Yes',
                affidavit: req.body.validated_affidavit === 'Yes',
                marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
                birthCertificate: req.body.validated_birthCertificate === 'Yes',
                parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
                signedAgreement: req.body.validated_signedAgreement === 'Yes',
                landRegister: req.body.validated_landRegister === 'Yes',
                valuation: req.body.validated_valuation === 'Yes',
                statusCard: req.body.validated_statusCard === 'Yes',
                botcCertificate: req.body.validated_botcCertificate === 'Yes',
            };

            const comment = {
                date: new Date(),
                internalComments: req.body.internalComments,
                user: req.session.user.username,
                status: 'COMPLETED',
            };
            if (submission.stampDutyOfficerTier && submission.stampDutyOfficerTier.comments && submission.stampDutyOfficerTier.comments.length) {
                submission.stampDutyOfficerTier.comments.push(comment);
            } else {
                submission.stampDutyOfficerTier = {
                    comments: [comment],
                }
            }
            await StampDutyApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
                'stampDutyOfficerTier.validations': validation,
                'stampDutyOfficerTier.completedAt': new Date(),
                'stampDutyOfficerTier.username': req.session.user.username,
                'stampDutyOfficerTier.comments': submission.stampDutyOfficerTier.comments,
                status: 'COMPLETED',
                auditReady: req.body.auditReady === 'Yes',
                remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
            });


            return res.status(httpConstants.HTTP_STATUS_OK).json({
                "status": httpConstants.HTTP_STATUS_OK,
                "message": "Your application has been updated successfully"
            });
        } else {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                "status": httpConstants.HTTP_STATUS_NOT_FOUND,
                "message": "Submission not found"
            });
        }
    } catch (e) {
        console.log("error: ", e);
        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "message": "There was an error updating the status of the application, try again later..."
        });
    }
};

exports.declineStampDutyExemptionApplication = async function (req, res) {
    try {
        const submission = await StampDutyApplicationModel.findById(req.params.stampDutyId);
        const comment = {
            date: new Date(),
            internalComments: req.body.internalComments,
            declineReason: req.body.declineReason,
            user: req.session.user.username,
            status: 'DECLINED',
        };
        if (submission.stampDutyOfficerTier && submission.stampDutyOfficerTier.comments && submission.stampDutyOfficerTier.comments.length) {
            submission.stampDutyOfficerTier.comments.push(comment);
        } else {
            submission.stampDutyOfficerTier = {
                comments: [comment],
            }
        }

        await StampDutyApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.declinedAt': new Date(),
            'stampDutyOfficerTier.username': req.session.user.username,
            'stampDutyOfficerTier.comments': submission.stampDutyOfficerTier.comments,
            status: 'DECLINED',
        });

        const email = submission.filingYourself ? submission.email : submission.personalInformation?.email;

        const declineNotice = await DeclineNoticeController.createDeclineNotice({
            applicationType: 'STAMP_DUTY_EXEMPTION_APPLICATION',
            applicationId: req.params.stampDutyId,
            declineReason: req.body.declineReason,
            email: email,
            user: req.session.user.username,
            applicationDetails: {
                applicantName: submission.firstName + ' ' + submission.lastName,
                transferorName: submission.firstName + ' ' + submission.lastName,
                transfereeName: "",
                referenceNumber: submission.referenceNr,
                parcelNumber: submission.propertyDetails?.parcel,
                district: submission.propertyDetails?.district,
                island: submission.propertyDetails?.island,
            }
        });

        await DeclineNoticeController.sendDeclineNotice(declineNotice._id, req.session.user.username);

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_OK,
            "message": "Your application has been updated successfully"
        });

    } catch (e) {
        console.log("error: ", e);
        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "message": "There was an error updating the status of the application, try again later..."
        });
    }
};

exports.conflictStampDutyExemptionApplication = async function (req, res) {
    try {
        const submission = await StampDutyApplicationModel.findById(req.params.stampDutyId);
        const comment = {
            date: new Date(),
            internalComments: req.body.internalComments,
            user: req.session.user.username,
            status: 'CONFLICTED',
        };
        if (submission.stampDutyOfficerTier && submission.stampDutyOfficerTier.comments && submission.stampDutyOfficerTier.comments.length) {
            submission.stampDutyOfficerTier.comments.push(comment);
        } else {
            submission.stampDutyOfficerTier = {
                comments: [comment],
            }
        }
        await StampDutyApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.conflictedAt': new Date(),
            'stampDutyOfficerTier.username': req.session.user.username,
            'stampDutyOfficerTier.comments': submission.stampDutyOfficerTier.comments,
            status: 'CONFLICTED BY STAMP DUTY OFFICER',
        });


        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, status: httpConstants.HTTP_STATUS_OK });

    } catch (e) {
        console.log("error: ", e);
        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "message": "There was an error updating the status of the application, try again later..."
        });
    }
};

exports.requestInformationStampDutyExemptionApplication = async function (req, res) {
    try {
        const submission = await StampDutyApplicationModel.findOne({ _id: req.params.stampDutyId });

        const validation = {
            applicantDetails: req.body.validatedApplicantDetails === 'Yes',
            personalDetails: req.body.validatedPersonalDetails === 'Yes',
            additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
            propertyDetails: req.body.validatedPropertyDetails === 'Yes',
            sellersDetails: req.body.validatedSellersDetails === 'Yes',
            affidavit: req.body.validated_affidavit === 'Yes',
            marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
            birthCertificate: req.body.validated_birthCertificate === 'Yes',
            parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
            signedAgreement: req.body.validated_signedAgreement === 'Yes',
            landRegister: req.body.validated_landRegister === 'Yes',
            valuation: req.body.validated_valuation === 'Yes',
            statusCard: req.body.validated_statusCard === 'Yes',
            botcCertificate: req.body.validated_botcCertificate === 'Yes',
        };

        await StampDutyApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.validations': validation,
            'stampDutyOfficerTier.username': req.session.user.username,
            status: submission.status === 'APPROVED' || submission.status === 'RETURNED BY PS' ? submission.status : 'SAVED STAMP DUTY OFFICER',
            auditReady: req.body.auditReady === 'Yes',
            remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
        });

        const newRequest = {
            requests: req.body.requestedInformation,
            createdBy: req.session.user.username,
            createdAt: new Date(),
            internalComments: req.body.internalComments,
            managementComments: req.body.managementComments,
        };

        if (submission.informationRequests && submission.informationRequests.length) {
            submission.informationRequests.push(newRequest);
        } else {
            submission.informationRequests = [newRequest];
        }
        await StampDutyApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            informationRequests: submission.informationRequests,
        });

        const email = submission.filingYourself ? submission.email : submission.personalInformation?.email;
        let emailContent = MailFormatter.generateEmailStampDutyExemptionApplicationInformationRequest(submission);
        await MailController.asyncSend([email],
            'Stamp Duty Exemption Information Requested',
            emailContent.textString,
            emailContent.htmlString
        );

        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, status: httpConstants.HTTP_STATUS_OK });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/stamp-duty/dashboard');
    }
};


exports.getReductionApplicationView = async function (req, res) {
    try {
        req.session.files = {};
        const application = await ReductionApplicationModel.findById(req.params.stampDutyId);
        application.propertyDetails.parcelFirstPart = application.propertyDetails.parcel.split('/')[0];
        application.propertyDetails.parcelSecondPart = application.propertyDetails.parcel.split('/')[1];

        const filesInformation = {
            affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.affidavit : false },
            marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.marriageCertificates : false },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.birthCertificate : false },
            parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.parentsDocuments : false },
            landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.landRegister : false },
            valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.valuation : false },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.statusCard : false },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length, validated: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations?.botcCertificate : false },
        }

        const { foundCardHolder, statusInformationColor } = await statusCardHolderHelper.getCardHolderStatusReduction(application);

        res.render('stamp-duty/open-stamp-duty-reduction-form',
            {
                application: application,
                validations: application.stampDutyOfficerTier ? application.stampDutyOfficerTier.validations : null,
                islands,
                calendar,
                filesInformation,
                foundCardHolder,
                statusInformationColor,
                isNewApplication: ['NOT STARTED', 'SAVED STAMP DUTY OFFICER', 'RETURNED BY PS'].includes(application.status),
                isComplete: 'COMPLETED' == application.status || 'DECLINED' == application.status,
                completedLastRequest: application.informationRequests && application.informationRequests.length ?
                    !!application.informationRequests[application.informationRequests.length - 1].submittedAt : true,
                remissionCount: application.remissionOrder ? application.remissionOrder.length : 0,
                additionalInformationCount: application.additionalInformation ? application.additionalInformation.length : 0,
            });

    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};


exports.saveReductionApplication = async function (req, res) {
    try {
        const submission = await ReductionApplicationModel.findById(req.params.stampDutyId);

        const validation = {
            applicantDetails: req.body.validatedApplicantDetails === 'Yes',
            additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
            companyDetails: req.body.validatedCompanyDetails === 'Yes',
            propertyDetails: req.body.validatedPropertyDetails === 'Yes',
            affidavit: req.body.validated_affidavit === 'Yes',
            marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
            birthCertificate: req.body.validated_birthCertificate === 'Yes',
            parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
            signedAgreement: req.body.validated_signedAgreement === 'Yes',
            landRegister: req.body.validated_landRegister === 'Yes',
            valuation: req.body.validated_valuation === 'Yes',
            statusCard: req.body.validated_statusCard === 'Yes',
            botcCertificate: req.body.validated_botcCertificate === 'Yes',
        };

        await ReductionApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.validations': validation,
            'stampDutyOfficerTier.username': req.session.user.username,
            status: submission.status === 'APPROVED' || submission.status === 'RETURNED BY PS' ? submission.status : 'SAVED STAMP DUTY OFFICER',
            auditReady: req.body.auditReady === 'Yes',
            remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
        });



        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, status: httpConstants.HTTP_STATUS_OK });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/stamp-duty/dashboard');
    }
};

exports.approveReductionApplication = async function (req, res) {
    try {
        const validation = {
            applicantDetails: req.body.validatedApplicantDetails === 'Yes',
            additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
            companyDetails: req.body.validatedCompanyDetails === 'Yes',
            propertyDetails: req.body.validatedPropertyDetails === 'Yes',
            affidavit: req.body.validated_affidavit === 'Yes',
            marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
            birthCertificate: req.body.validated_birthCertificate === 'Yes',
            parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
            signedAgreement: req.body.validated_signedAgreement === 'Yes',
            landRegister: req.body.validated_landRegister === 'Yes',
            valuation: req.body.validated_valuation === 'Yes',
            statusCard: req.body.validated_statusCard === 'Yes',
            botcCertificate: req.body.validated_botcCertificate === 'Yes',
        };

        const reductionApplication = await ReductionApplicationModel.findById(req.params.stampDutyId);
        const comment = {
            date: new Date(),
            internalComments: req.body.internalComments,
            user: req.session.user.username,
            status: 'APPROVED',
        };
        if (reductionApplication.stampDutyOfficerTier && reductionApplication.stampDutyOfficerTier.comments && reductionApplication.stampDutyOfficerTier.comments.length) {
            reductionApplication.stampDutyOfficerTier.comments.push(comment);
        } else {
            reductionApplication.stampDutyOfficerTier = {
                comments: [comment],
            }
        }
        await ReductionApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.validations': validation,
            'stampDutyOfficerTier.approvedAt': new Date(),
            'stampDutyOfficerTier.username': req.session.user.username,
            'stampDutyOfficerTier.comments': reductionApplication.stampDutyOfficerTier.comments,
            status: 'PENDING PS',
            auditReady: req.body.auditReady === 'Yes',
            remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
        });


        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_OK,
            "message": "Your application has been updated successfully"
        });

    } catch (e) {
        console.log("error: ", e);
        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "message": "There was an error updating the status of the application, try again later..."
        });
    }
};

exports.completeReductionApplication = async function (req, res) {
    try {
        const reductionApplication = await ReductionApplicationModel.findById(req.params.stampDutyId);
        if (reductionApplication.remissionOrder && reductionApplication.remissionOrder.length >= 1) {
            const validation = {
                applicantDetails: req.body.validatedApplicantDetails === 'Yes',
                additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
                companyDetails: req.body.validatedCompanyDetails === 'Yes',
                propertyDetails: req.body.validatedPropertyDetails === 'Yes',
                affidavit: req.body.validated_affidavit === 'Yes',
                marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
                birthCertificate: req.body.validated_birthCertificate === 'Yes',
                parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
                signedAgreement: req.body.validated_signedAgreement === 'Yes',
                landRegister: req.body.validated_landRegister === 'Yes',
                valuation: req.body.validated_valuation === 'Yes',
                statusCard: req.body.validated_statusCard === 'Yes',
                botcCertificate: req.body.validated_botcCertificate === 'Yes',
            };

            const comment = {
                date: new Date(),
                internalComments: req.body.internalComments,
                user: req.session.user.username,
                status: 'COMPLETED',
            };
            if (reductionApplication.stampDutyOfficerTier && reductionApplication.stampDutyOfficerTier.comments && reductionApplication.stampDutyOfficerTier.comments.length) {
                reductionApplication.stampDutyOfficerTier.comments.push(comment);
            } else {
                reductionApplication.stampDutyOfficerTier = {
                    comments: [comment],
                }
            }
            await ReductionApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
                'stampDutyOfficerTier.validations': validation,
                'stampDutyOfficerTier.completedAt': new Date(),
                'stampDutyOfficerTier.username': req.session.user.username,
                'stampDutyOfficerTier.comments': reductionApplication.stampDutyOfficerTier.comments,
                status: 'COMPLETED',
                auditReady: req.body.auditReady === 'Yes',
                remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
            });


            return res.status(httpConstants.HTTP_STATUS_OK).json({
                "status": httpConstants.HTTP_STATUS_OK,
                "message": "Your application has been updated successfully"
            });
        } else {
            return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
                "status": httpConstants.HTTP_STATUS_BAD_REQUEST,
                "message": "Please upload the remission order for this application."
            });
        }
    } catch (e) {
        console.log("error: ", e);
        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "message": "There was an error updating the status of the application, try again later..."
        });
    }
};

exports.declineReductionApplication = async function (req, res) {
    try {
        const reductionApplication = await ReductionApplicationModel.findById(req.params.stampDutyId);
        const comment = {
            date: new Date(),
            internalComments: req.body.internalComments,
            declineReason: req.body.declineReason,
            user: req.session.user.username,
            status: 'DECLINED',
        };
        if (reductionApplication.stampDutyOfficerTier && reductionApplication.stampDutyOfficerTier.comments &&
            reductionApplication.stampDutyOfficerTier.comments.length) {
            reductionApplication.stampDutyOfficerTier.comments.push(comment);
        } else {
            reductionApplication.stampDutyOfficerTier = {
                comments: [comment],
            }
        }

        await ReductionApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.declinedAt': new Date(),
            'stampDutyOfficerTier.username': req.session.user.username,
            'stampDutyOfficerTier.comments': reductionApplication.stampDutyOfficerTier.comments,
            status: 'DECLINED',
        });

        const email = reductionApplication.filingBehalf === 'Natural person' ?
            reductionApplication.applicantDetails.email :
            reductionApplication.companyDetails.email;
        const name = reductionApplication.filingBehalf === 'Natural person' ?
            reductionApplication.applicantDetails.firstName + ' ' + reductionApplication.applicantDetails.lastName :
            reductionApplication.companyDetails.name;
        const declineNotice = await DeclineNoticeController.createDeclineNotice({
            applicationType: 'REDUCTION_APPLICATION',
            applicationId: req.params.stampDutyId,
            declineReason: req.body.declineReason,
            email: email,
            user: req.session.user.username,
            applicationDetails: {
                applicantName: name,
                transferorName: name,
                transfereeName: "",
                referenceNumber: reductionApplication.referenceNr,
                parcelNumber: reductionApplication.propertyDetails?.parcel,
                district: reductionApplication.propertyDetails?.district,
                island: reductionApplication.propertyDetails?.island,
            }
        });

        await DeclineNoticeController.sendDeclineNotice(declineNotice._id, req.session.user.username);




        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_OK,
            "message": "Your application has been updated successfully"
        });

    } catch (e) {
        console.log("error: ", e);
        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "message": "There was an error updating the status of the application, try again later..."
        });
    }
};

exports.conflictReductionApplication = async function (req, res) {
    try {
        const reductionApplication = await ReductionApplicationModel.findById(req.params.stampDutyId);
        const comment = {
            date: new Date(),
            internalComments: req.body.internalComments,
            user: req.session.user.username,
            status: 'CONFLICTED',
        };
        if (reductionApplication.stampDutyOfficerTier && reductionApplication.stampDutyOfficerTier.comments &&
            reductionApplication.stampDutyOfficerTier.comments.length) {
            reductionApplication.stampDutyOfficerTier.comments.push(comment);
        } else {
            reductionApplication.stampDutyOfficerTier = {
                comments: [comment],
            }
        }
        await ReductionApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.conflictedAt': new Date(),
            'stampDutyOfficerTier.username': req.session.user.username,
            'stampDutyOfficerTier.comments': reductionApplication.stampDutyOfficerTier.comments,
            status: 'CONFLICTED BY STAMP DUTY OFFICER',
        });


        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, status: httpConstants.HTTP_STATUS_OK });

    } catch (e) {
        console.log("error: ", e);
        return res.status(httpConstants.HTTP_STATUS_OK).json({
            "status": httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            "message": "There was an error updating the status of the application, try again later..."
        });
    }
};

exports.requestInformationReductionApplication = async function (req, res) {
    try {
        const reductionApplication = await ReductionApplicationModel.findOne({ _id: req.params.stampDutyId });

        const validation = {
            applicantDetails: req.body.validatedApplicantDetails === 'Yes',
            additionalApplicants: req.body.validatedAdditionalApplicants === 'Yes',
            companyDetails: req.body.validatedCompanyDetails === 'Yes',
            propertyDetails: req.body.validatedPropertyDetails === 'Yes',
            affidavit: req.body.validated_affidavit === 'Yes',
            marriageCertificates: req.body.validated_marriageCertificates === 'Yes',
            birthCertificate: req.body.validated_birthCertificate === 'Yes',
            parentsDocuments: req.body.validated_parentsDocuments === 'Yes',
            signedAgreement: req.body.validated_signedAgreement === 'Yes',
            landRegister: req.body.validated_landRegister === 'Yes',
            valuation: req.body.validated_valuation === 'Yes',
            statusCard: req.body.validated_statusCard === 'Yes',
            botcCertificate: req.body.validated_botcCertificate === 'Yes',
        };

        await ReductionApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            'stampDutyOfficerTier.validations': validation,
            'stampDutyOfficerTier.username': req.session.user.username,
            status: reductionApplication.status === 'APPROVED' || reductionApplication.status === 'RETURNED BY PS' ? reductionApplication.status : 'SAVED STAMP DUTY OFFICER',
            auditReady: req.body.auditReady === 'Yes',
            remittedAmount: req.body.remittedAmount ? req.body.remittedAmount : 0
        });

        const newRequest = {
            requests: req.body.requestedInformation,
            createdBy: req.session.user.username,
            createdAt: new Date(),
            internalComments: req.body.internalComments,
            managementComments: req.body.managementComments,
        };

        if (reductionApplication.informationRequests && reductionApplication.informationRequests.length) {
            reductionApplication.informationRequests.push(newRequest);
        } else {
            reductionApplication.informationRequests = [newRequest];
        }
        await ReductionApplicationModel.updateOne({ _id: req.params.stampDutyId }, {
            informationRequests: reductionApplication.informationRequests,
        });

        const email = reductionApplication.filingBehalf === 'Natural person' ?
            reductionApplication.applicantDetails.email :
            reductionApplication.companyDetails.email;
        let emailContent = MailFormatter.generateEmailReductionApplicationInformationRequest(reductionApplication);
        await MailController.asyncSend([email],
            'Stamp Duty Reduction Information Requested',
            emailContent.textString,
            emailContent.htmlString
        );

        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, status: httpConstants.HTTP_STATUS_OK });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/stamp-duty/dashboard');
    }
};

exports.getFilesReductionApplication = async function (req, res) {
    try {
        let files = [];
        const application = await ReductionApplicationModel.findById(req.params.stampDutyId);
        if (req.query.fileGroup === 'remission-order') {
            files = application.remissionOrder ? application.remissionOrder : [];
        }
        if (req.query.fileGroup === 'additional-information') {
            files = application.additionalInformation ? application.additionalInformation : [];
        }

        res.json({ status: httpConstants.HTTP_STATUS_OK, files });
    } catch (e) {
        console.log("error: ", e);
        res.redirect('/');
    }
};

exports.storeFilesReductionApplication = async function (req, res) {
    try {
        const application = await ReductionApplicationModel.findById(req.params.stampDutyId);

        let uploadedFiles = req.files['fileUploaded'];

        const fileName = req.body.fileName.replace(/[^a-zA-Z0-9]/g, "");
        if (uploadedFiles && uploadedFiles.length > 0) {
            if (!application.remissionOrder) {
                application.remissionOrder = [];
            }
            if (!application.additionalInformation) {
                application.additionalInformation = [];
            }

            uploadedFiles = uploadedFiles.map((itemToUpload) => {
                return {
                    fileId: uuidv4(),
                    fieldName: itemToUpload.fieldname.replace(/fileUploaded/i, fileName),
                    blob: itemToUpload.blob.replace(/fileUploaded/i, fileName),
                    blobName: itemToUpload.blobName,
                    url: itemToUpload.url,
                    originalName: itemToUpload.originalname,
                    encoding: itemToUpload.encoding,
                    mimeType: itemToUpload.mimetype,
                    container: itemToUpload.container,
                    blobType: itemToUpload.blobType,
                    size: itemToUpload.size,
                    etag: itemToUpload.etag
                };
            });

            if (req.body.fileGroup === "additional-information") {
                application.additionalInformation = application.additionalInformation.concat(uploadedFiles);
            } else if (req.body.fileGroup === "remission-order") {
                application.remissionOrder = application.remissionOrder.concat(uploadedFiles);
            }

            await application.save();

            res.sendStatus(httpConstants.HTTP_STATUS_OK)
        }
    } catch (e) {
        return res.status(500).end();
    }

};

exports.deleteUploadReductionApplication = async function (req, res) {
    const application = await ReductionApplicationModel.findById(req.params.stampDutyId);

    if (req.body.group === "remission-order") {
        let files = application.remissionOrder.filter((f) => f.fileId != req.body.fileId);
        application.remissionOrder = files;
    }
    if (req.body.group === "additional-information") {
        let files = application.additionalInformation.filter((f) => f.fileId != req.body.fileId);
        application.additionalInformation = files;
    }
    await application.save();
    res.json({ status: httpConstants.HTTP_STATUS_OK, message: 'success' });
};
