<div id="additional-information-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="uploadModal"
    style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-lg container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="additional-information-title">
                    Notify applicant
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="col-12">
                                <div class="row">
                                    Please provide the following documents:
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input" id="request-affidavit"
                                                name="requestedInformation" value="affidavit" required /><label
                                                class="custom-control-label" for="request-affidavit">Affidavit</label>
                                        </div>
                                    </div>

                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-marriageCertificates" name="requestedInformation"
                                                value="marriageCertificates" required /><label
                                                class="custom-control-label" for="request-marriageCertificates">Marriage
                                                Certificates (if included/required in this application)</label>
                                        </div>

                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input" id="request-statusCard"
                                                name="requestedInformation" value="statusCard" required /><label
                                                class="custom-control-label" for="request-statusCard">Status
                                                Card</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-birthCertificate" name="requestedInformation"
                                                value="birthCertificate" required /><label class="custom-control-label"
                                                for="request-birthCertificate">Birth Certificate</label>
                                        </div>
                                    </div>

                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-governmentIssuedIdentification" name="requestedInformation"
                                                value="governmentIssuedIdentification" required /><label
                                                class="custom-control-label"
                                                for="request-governmentIssuedIdentification">Government issued
                                                identification</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-parentsDocuments" name="requestedInformation"
                                                value="parentsDocuments" required /><label class="custom-control-label"
                                                for="request-parentsDocuments">Documentation Parents</label>
                                        </div>
                                    </div>

                                </div>
                                {{#ifEquals type 'stamp-duty-exemption'}}
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <div class="custom-control custom-checkbox custom-control-inline"><input
                                                    type="checkbox" class="custom-control-input" id="request-signedAgreement"
                                                    name="requestedInformation" value="signedAgreement" required /><label
                                                    class="custom-control-label"
                                                    for="request-signedAgreement">Signed agreement of the purchase of the property</label>
                                            </div>
                                        </div>

                                    </div>
                                {{/ifEquals}}
                                {{#ifEquals type 'import-duty-waiver'}}
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <div class="custom-control custom-checkbox custom-control-inline"><input
                                                    type="checkbox" class="custom-control-input" id="request-itemsInvoices"
                                                    name="requestedInformation" value="itemsInvoices" required /><label
                                                    class="custom-control-label"
                                                    for="request-itemsInvoices">Invoices</label>
                                            </div>
                                        </div>

                                    </div>

                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <div class="custom-control custom-checkbox custom-control-inline"><input
                                                    type="checkbox" class="custom-control-input"
                                                    id="request-buildingPictures" name="requestedInformation"
                                                    value="buildingPictures" required /><label class="custom-control-label"
                                                                                               for="request-buildingPictures">Photographs building or site</label>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <div class="custom-control custom-checkbox custom-control-inline"><input
                                                    type="checkbox" class="custom-control-input" id="request-buildingPermit"
                                                    name="requestedInformation" value="buildingPermit" required /><label
                                                    class="custom-control-label" for="request-buildingPermit">Building
                                                permit</label>
                                            </div>
                                        </div>

                                    </div>

                                {{/ifEquals}}


                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input" id="request-landRegister"
                                                name="requestedInformation" value="landRegister" required /><label
                                                class="custom-control-label" for="request-landRegister">Land Register
                                                extract</label>
                                        </div>
                                    </div>

                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input" id="request-valuation"
                                                name="requestedInformation" value="valuation" required /><label
                                                class="custom-control-label" for="request-valuation">Valuation
                                                report</label>
                                        </div>
                                    </div>

                                </div>
                                {{#ifEquals application.documentType 'botc'}}
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-botcCertificate" name="requestedInformation"
                                                value="botcCertificate" required /><label class="custom-control-label"
                                                for="request-botcCertificate">BOTC Certificate</label>
                                        </div>
                                    </div>
                                </div>
                                {{/ifEquals}}
                                <div class="row mt-2">
                                    Please provide the following information:
                                </div>
                                {{#ifEquals type 'stamp-duty-reduction'}}
                                {{#ifEquals application.documentType 'status-card'}}
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-citizenshipDetails" name="requestedInformation"
                                                value="citizenshipDetails" required /><label class="custom-control-label"
                                                for="request-citizenshipDetails">Citizenship details</label>
                                        </div>
                                    </div>
                                </div>
                                {{/ifEquals}}
                                {{#ifEquals application.filingBehalf 'Company'}}
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-companyDetails" name="requestedInformation"
                                                value="companyDetails" required /><label class="custom-control-label"
                                                for="request-companyDetails">Company details</label>
                                        </div>
                                    </div>
                                </div>
                                {{/ifEquals}}
                                {{else}}
                                {{#unless application.filingYourself}}
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-personalDetails" name="requestedInformation"
                                                value="personalDetails" required /><label class="custom-control-label"
                                                for="request-personalDetails">Personal details</label>
                                        </div>
                                    </div>
                                </div>
                                {{/unless}}
                                {{/ifEquals}}
                                {{#ifCond application.filingBehalf '!=' 'Company'}}
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-applicantDetails" name="requestedInformation"
                                                value="applicantDetails" required /><label class="custom-control-label"
                                                for="request-applicantDetails">Applicant details</label>
                                        </div>
                                    </div>
                                </div>
                                {{/ifCond}}
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-additionalApplicants" name="requestedInformation"
                                                value="additionalApplicants" required /><label
                                                class="custom-control-label"
                                                for="request-additionalApplicants">Additional Applicants</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <div class="custom-control custom-checkbox custom-control-inline"><input
                                                type="checkbox" class="custom-control-input"
                                                id="request-propertyDetails" name="requestedInformation"
                                                value="propertyDetails" required /><label class="custom-control-label"
                                                for="request-propertyDetails">Property details</label>
                                        </div>
                                    </div>
                                </div>

                                {{#ifEquals type 'stamp-duty-exemption'}}
                                    <div class="row mt-2">
                                        <div class="col-12">
                                            <div class="custom-control custom-checkbox custom-control-inline"><input
                                                    type="checkbox" class="custom-control-input" id="request-sellers"
                                                    name="requestedInformation" value="sellerDetails" required /><label
                                                    class="custom-control-label"
                                                    for="request-sellers">Sellers</label>
                                            </div>
                                        </div>

                                    </div>

                                {{/ifEquals}}

                                <div class="row mt-2">
                                    <p class="invalid-feedback" style="display: none;" id="onHoldError">Please select at
                                        least 1 option</p>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <label for="request-management-comments">Comments for applicant</label>
                                        <textarea type="text" name="request-management-comments" class="form-control"
                                            id="request-management-comments" placeholder="" required></textarea>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <label for="request-internal-comments">Internal Comments</label>
                                        <textarea type="text" name="request-internal-comments" class="form-control"
                                            id="request-internal-comments" placeholder="" required></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- end card-body-->
                        <div class="modal-footer">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <div class="col-md-2">
                                        <button type="button" class="btn solid royal-blue btn-block"
                                            data-dismiss="modal">
                                            Close
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-success btn-block" id="submitRequestBtn"
                                            onclick="submitDocumentRequest()">
                                            <i class="mdi mdi-send mr-1"></i>Send
                                        </button>
                                    </div>


                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- end card-->
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->
        </div>
    </div>
</div>

<script type="text/javascript">

    $('#additional-information-modal').on('hidden.bs.modal', function () {
        $('input[name="requestedInformation"]').each(function () {
            $(this).is(':checked')
        });
    })

    $('input[name="requestedInformation"]').on('change', function () {
        let showError = true;
        $('input[name="requestedInformation"]').each(function () {
            if ($(this).is(':checked')) {
                showError = false;
            }
        });
        if (showError) {
            $('#onHoldError').show();
        } else {
            $('#onHoldError').hide();
        }
    });

    function submitDocumentRequest() {
        $('#submitRequestBtn').prop('disabled', true);
        const requestedInformation = [];
        $('input[name="requestedInformation"]').each(function () {
            if ($(this).is(':checked')) {
                requestedInformation.push($(this).val());
            }
        });
        if (!requestedInformation.length) {
            $('#onHoldError').show();
            $('#submitRequestBtn').prop('disabled', false);
        } else {
            $('#onHoldError').hide();
            const form = $('#submitForm').serializeArray();
            let serializedForm = {};
            for (let i = 0; i < form.length; i++) {
                const nameField = form[i]['name'];
                serializedForm[nameField] = form[i]['value'];
            }
            $.ajax({
                type: "POST",
                url: "./request-information",
                data: JSON.stringify({
                    requestedInformation,
                    managementComments: $('#request-management-comments').val(),
                    internalComments: $('#request-internal-comments').val(),
                    ...serializedForm
                }),
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.success) {
                        toastr["success"]('Your request was submitted successfully', 'Request submitted!');
                        window.location = './update'
                    } else {
                        $('#submitRequestBtn').prop('disabled', false);
                        $(".action-button").each(function () { $(this).prop('disabled', false) });
                        toastr["warning"](data.message, 'Error!');
                    }
                },
                error: function (err) {
                    toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                },
            });
        }
    }

</script>
