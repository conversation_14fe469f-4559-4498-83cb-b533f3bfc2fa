const express = require('express');
const router = express.Router();


const exportSearchController = require('../controllers/exportSearchController');
//PAGES
router.post("/search-xls", ensureAuthenticated, exportSearchController.exportSearchXls);


function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isStampDuty) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
};

module.exports = router;
