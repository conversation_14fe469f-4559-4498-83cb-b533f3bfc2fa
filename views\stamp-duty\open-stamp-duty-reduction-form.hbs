<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submitForm" method="POST" autocomplete="off">
                            <div class="row mt-2">
                                <div class="col-md-12">
                                    <h2>Stamp Duty Reduction</h2>
                                    <br>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-12">
                                    <h3>Audit</h3>
                                </div>
                            </div>
                            <div class="row mt-2 justify-content-between">
                                <div class="col-6">
                                    <div class="custom-control custom-checkbox custom-control-inline mt-2">
                                        <input type="checkbox" class="custom-control-input"
                                            id="auditReady" name="auditReady" {{#if
                                            application.auditReady}}checked{{/if}} value="Yes" />
                                        <label class="custom-control-label" for="auditReady">Application
                                            is audit ready?</label>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="row justify-content-end">
                                        <button onclick="saveProgress()" type="button"
                                            class="btn btn-primary action-button width-xl">
                                            Save
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="row mt-2">
                                <div class="col-12">
                                    {{>client-forms/stamp-duty-exemption-log application=application}}
                                </div>
                            </div>
                            <hr>
                            {{>client-forms/stamp-duty-reduction-validate-form
                            application=application
                            islands=islands
                            calendar=calendar
                            validations=validations
                            filesInformation=filesInformation
                            disabledValidations=isComplete}}
                            <hr class="bg-warning text-warning">

                            <h3>
                                Please fill in the remitted amount (USD)
                            </h3>
                            <div>
                                <div class="row mt-3">
                                    <div class="col-md-4">
                                        <div class="form-group mb-0">
                                            <label for="remittedAmountControl">Remitted amount</label>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <div class="input-group mb-3">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text" id="money-addon">USD</span>
                                            </div>
                                            <input type="number" name="remittedAmount" id="remittedAmountControl"
                                                class="form-control" min="0" value="{{application.remittedAmount}}"
                                                required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {{#unless isNewApplication}}
                            {{#ifCond application.status '!=' 'DECLINED'}}
                            <h3>
                                Document uploads
                            </h3>
                            <div>
                                <div class="row mb-1">
                                    <div class="col-md-9" style="display: table-cell; vertical-align: middle;">
                                        <ul class="ml-3" style="list-style: disc;">
                                            <li>Remission Order</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-3 text-right">
                                        <button id="uploadRemissionOrderBtn" type="button"
                                            class="btn btn-block btn-warning mb-1" data-toggle="modal"
                                            data-target="#upload-files-modal" data-field="Remission Order"
                                            {{#ifCond remissionCount '>' 0}} style="background-color: #159a80; border-color: #148f77"{{else}}{{#if isComplete}}
                                            disabled{{/if}}{{/ifCond}}
                                            data-maxfiles="15" data-minfiles="1" data-group="remission-order">
                                            {{#ifCond remissionCount '>' 0}}Uploaded documents{{else}}Upload{{/ifCond}}
                                        </button>
                                    </div>
                                </div>
                                <div class="row mb-1">
                                    <div class="col-md-9" style="display: table-cell; vertical-align: middle;">
                                        <ul class="ml-3" style="list-style: disc;">
                                            <li>Additional information</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-3 text-right">
                                        <button id="uploadAdditionalInformationBtn" type="button"
                                            class="btn btn-block btn-warning mb-1" data-toggle="modal"
                                            data-target="#upload-files-modal" data-field="Additional information"
                                            {{#ifCond additionalInformationCount '>' 0}} style="background-color: #159a80;border-color: #148f77"{{else}}{{#if isComplete}}
                                            disabled{{/if}}{{/ifCond}}
                                            data-maxfiles="15" data-minfiles="1" data-group="additional-information">
                                            {{#ifCond additionalInformationCount '>' 0}}Uploaded documents{{else}}Upload{{/ifCond}}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {{/ifCond}}
                            {{/unless}}
                        </form>
                        {{#unless isComplete}}
                        <div class="d-flex flex-wrap justify-content-between mt-2">
                            <div class="flex-grow">
                                <button onclick="openAdditionalInformationModal()" type="button"
                                    class="btn btn-info action-button width-xl">
                                    Request information
                                </button>
                            </div>
                            <div class="flex-grow">
                                <button onclick="saveProgress()" type="button"
                                    class="btn btn-primary action-button width-xl">
                                    Save
                                </button>
                            </div>
                            {{#if isNewApplication}}
                            <div class="flex-grow">
                                <button onclick="openConflictApplicationModal()" type="button"
                                    class="btn btn-warning action-button width-xl">
                                    Conflict
                                </button>
                            </div>
                            <div class="flex-grow">
                                <button onclick="openDeclineApplicationModal()" type="button"
                                    class="btn btn-danger action-button width-xl">
                                    Decline
                                </button>
                            </div>
                            <div class="flex-grow tooltip-wrapper" {{#unless completedLastRequest}} data-toggle="tooltip"
                                data-trigger="hover" data-placement="top" data-title="Application has information request in progress"{{/unless}}>
                                <button onclick="openApproveApplicationModal()" type="button"
                                {{#unless completedLastRequest}}disabled{{/unless}}
                                    class="btn btn-success action-button width-xl">
                                    Approve
                                </button>
                            </div>
                            {{else}}
                            <div class="flex-grow">
                                <button onclick="openCompleteApplicationModal()" type="button"
                                    class="btn btn-success action-button width-xl">
                                    Complete
                                </button>
                            </div>
                            {{/if}}
                        </div>
                        {{/unless}}
                        <hr>
                        <div class="row">
                            <div class="col-md-2">
                                <a href="javascript:history.back()" class="btn btn-secondary btn-block action-button">
                                    Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>modals/additional-information application=application type="stamp-duty-reduction"}}
{{>modals/upload-application-files-modal application=application stampDutyType='reduction' isComplete=isComplete}}
{{>modals/decline-modal}}
{{>modals/approve-modal}}
{{>modals/complete-modal}}
{{>modals/conflict-modal}}


<script type="text/javascript">
    let remissionCount = {{remissionCount}};
    function saveProgress() {
        $(".action-button").each(function () { $(this).prop('disabled', true) });
        const form = $('#submitForm').serializeArray();
        let serializedForm = {};
        for (let i = 0; i < form.length; i++) {
            const nameField = form[i]['name'];
            serializedForm[nameField] = form[i]['value'];
        }
        $.ajax({
            type: "POST",
            url: "./update",
            data: JSON.stringify(serializedForm),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.success) {
                    toastr["success"]('Your application was updated successfully', 'Application updated!');
                    window.location = './update'
                } else {
                    toastr["warning"](data.message, 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    }

    function openApproveApplicationModal() {
        if (checkValidations()) {
            $('#approve-modal').modal();
        }
    }

    function openCompleteApplicationModal() {
        if (checkValidations()) {
            if (remissionCount > 0) {
                $('#complete-modal').modal();
            } else {
                toastr["warning"]('Please upload the remission order for this application.', 'Error!');
            }
        }
    }

    function checkValidations() {
        let invalidChecks = false;
        let invalidRemittedAmount = false;
        $("input[type='checkbox'][required]:visible").each(function () {
            const val = $('input:checkbox[name="' + this.name + '"]:checked').val();
            if (val === undefined) {
                $('input:checkbox[name="' + this.name + '"]').toggleClass("is-invalid", true);
                invalidChecks = true;
            } else {
                $('input:checkbox[name="' + this.name + '"]').toggleClass("is-invalid", false);
            }
        });
        if (!$('#remittedAmountControl').val() || $('#remittedAmountControl').val() == 0) {
            invalidRemittedAmount = true;
        }
        if (invalidChecks || invalidRemittedAmount) {
            if (invalidRemittedAmount) {
                toastr["warning"]('Please provide a remitted amount for this application', 'Error!');
            }
            if (invalidChecks) {
                toastr["warning"]('Please validate all sections before approval', 'Error!');
            }
            return false;
        } else {
            return true;
        }
    }

    function openAdditionalInformationModal() {
        $('#additional-information-modal').modal();
    }

    function openDeclineApplicationModal() {
        $('#decline-modal').modal();
    }

    function openConflictApplicationModal() {
        $('#conflict-modal').modal();
    }

    $("input[type='checkbox']").on('change', function () {
        const empty = $('input[name="' + this.name + '"]:checked').val() === "";
        $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
    });

    $('#submitDecline').click(function () {
        if (!($('input[name="declineReason"]:checked').val())) {
            $('#declineReasonError').show();
        } else {
            if (($('input[name="declineReason"]:checked').val() === 'Other' && !$("#decline-reason-other-text").val())) {
                $('#declineReasonError').show();
            }
            else {
                $('#declineReasonError').hide();
                declineApplication();
            }
        }
    });

    $('#submitConflict').click(function () {
        $.ajax({
            type: "POST",
            url: "./conflict",
            data: JSON.stringify({ internalComments: $('#conflictModalComments').val() }),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    Swal.fire('Success', data.message || "Your application was updated successfully", 'success').then(() => {
                        location.href = '/stamp-duty/dashboard';
                    });
                } else {
                    toastr["warning"](data.message ? data.message : "There was an error updating the application...", 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    });

    $('#submitApprove').click(function () {
        $(".action-button").each(function () { $(this).prop('disabled', true) });
        const form = $('#submitForm').serializeArray();
        let serializedForm = {
            internalComments: $('#approvedModalComments').val()
        };
        for (let i = 0; i < form.length; i++) {
            const nameField = form[i]['name'];
            serializedForm[nameField] = form[i]['value'];
        }
        $.ajax({
            type: "POST",
            url: "./approve",
            data: JSON.stringify(serializedForm),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    Swal.fire('Success', data.message || "Your application was updated successfully", 'success').then(() => {
                        location.href = '/stamp-duty/dashboard';
                    });
                } else {
                    toastr["warning"](data.message ? data.message : "There was an error updating the application...", 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    });

    $('#submitComplete').click(function () {
        $(".action-button").each(function () { $(this).prop('disabled', true) });
        const form = $('#submitForm').serializeArray();
        let serializedForm = {
            internalComments: $('#completedModalComments').val()
        };
        for (let i = 0; i < form.length; i++) {
            const nameField = form[i]['name'];
            serializedForm[nameField] = form[i]['value'];
        }
        $.ajax({
            type: "POST",
            url: "./complete",
            data: JSON.stringify(serializedForm),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    Swal.fire('Success', data.message || "Your application was updated successfully", 'success').then(() => {
                        location.href = '/stamp-duty/dashboard';
                    });
                } else {
                    toastr["warning"](data.message ? data.message : "There was an error updating the application...", 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    });

    function declineApplication() {
        let declineReason = '';

        declineReason = $('input[name="declineReason"]:checked').val();
        if (declineReason === 'Other') {
            declineReason = $('#decline-reason-other-text').val();
        }
        $.ajax({
            type: "POST",
            url: "./decline",
            data: JSON.stringify({
                declineReason,
                internalComments: $('#declinedModalComments').val()
            }),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    Swal.fire('Success', data.message || "Your application was updated successfully", 'success').then(() => {
                        location.href = '/stamp-duty/dashboard';
                    });
                } else {
                    toastr["warning"](data.message ? data.message : "There was an error updating the application...", 'Error!');
                    $(".action-button").each(function () { $(this).prop('disabled', false) });
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be updated, please try again later.', 'Error!');
                $(".action-button").each(function () { $(this).prop('disabled', false) });
            },
        });
    }

</script>
