const azure = require('azure-storage');
const landOfficerController = require('../controllers/landOfficerController');
const ExemptionModel = require('../models/exemptions');
const StampDutyExemptionApplicationModel = require('../models/stampDutyExemptionApplication').StampDutyApplicationModel;
const ImportDutyWaiverApplicationModel = require('../models/importDutyWaiverApplication').ImportDutyWaiverApplicationModel;
const ReductionApplicationModel = require('../models/stampDutyReductionApplication').ReductionApplicationModel;
const httpConstants = require('http2').constants;

exports.downloadFile = async function (req, res, next) {
    try {
        let fileToDownload;
        if (req.params.landOfficerId) {
            const landOfficer = await ExemptionModel.findById(req.params.landOfficerId);

            if (landOfficer) {
                let files = landOfficer.files.find((file) => file.id === req.params.fileTypeId);
                if (!files && landOfficer.signedFiles && landOfficer.signedFiles.id === req.params.fileTypeId) {
                    files = landOfficer.signedFiles;
                }

                if (!files && landOfficer.transferCompletedFiles && landOfficer.transferCompletedFiles.id === req.params.fileTypeId) {
                    files = landOfficer.transferCompletedFiles;
                }

                if (!files && landOfficer.companyInformationFiles && landOfficer.companyInformationFiles.id === req.params.fileTypeId) {
                    files = landOfficer.companyInformationFiles;
                }
                if (!files && landOfficer.stampDutyAdditionalFiles && landOfficer.stampDutyAdditionalFiles.id === req.params.fileTypeId) {
                    files = landOfficer.stampDutyAdditionalFiles;
                }

                fileToDownload = files.uploadFiles.find((file) => file.fileId === req.params.fileId)


            }
        }

        if (!fileToDownload) {
            const tempFiles = landOfficerController.getTempUploadFiles(req.session, req.params.fileTypeId);
            if (tempFiles) {
                const file = tempFiles.find((f) => f.fileId === req.params.fileId);
                if (file) {
                    fileToDownload = file;
                }
            }
        }
        const container = process.env.AZURE_STORAGE_CONTAINER_STAMP_DUTY;
        if (fileToDownload) {
            downloadApplicationFile(req, res, next, fileToDownload, false, process.env.AZURE_STORAGE_ACCOUNT, process.env.AZURE_STORAGE_ACCESS_KEY, container);
        }
    } catch (e) {
        console.log(e);
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};

exports.downloadStampDutyExemptionFile = async function (req, res, next) {
    try {
        let fileToDownload;
        if (req.params.applicationId && req.params.fileGroup) {
            const application = await StampDutyExemptionApplicationModel.findById(req.params.applicationId);
            if (application) {
                fileToDownload = application[req.params.fileGroup].find((file) => file.fileId === req.params.fileId);
            }
        } else if (req.params.stampDutyId && req.params.group && req.params.fileId) {
            const application = await StampDutyExemptionApplicationModel.findById(req.params.stampDutyId);
            console.log(application);
            if (application) {
                let files;
                if (req.params.group === 'remission-order') {
                    files = application.remissionOrder ? application.remissionOrder : [];
                }
                if (req.params.group === 'additional-information') {
                    files = application.additionalInformation ? application.additionalInformation : [];
                }
                fileToDownload = files.find((file) => file.fileId == req.params.fileId);
            }
        } else {
            throw new Error();
        }
        if (fileToDownload) {
            const container = process.env.AZURE_STORAGE_CONTAINER_HOME_OWNER_POLICY
            downloadApplicationFile(req, res, next, fileToDownload,
                false, process.env.AZURE_STORAGE_ACCOUNT, process.env.AZURE_STORAGE_ACCESS_KEY, container);
        } else {
            throw new Error();
        }
    } catch (e) {
        console.log(e);
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};

exports.downloadImportDutyWaiverFile = async function (req, res, next) {
    try {
        let fileToDownload;
        if (req.params.applicationId && req.params.fileGroup) {
            const application = await ImportDutyWaiverApplicationModel.findById(req.params.applicationId);
            console.log(application)
            if (application) {
                fileToDownload = application[req.params.fileGroup].find((file) => file.fileId === req.params.fileId);
            }
        } else {
            throw new Error();
        }
        if (fileToDownload) {
            const container = process.env.AZURE_STORAGE_CONTAINER_HOME_OWNER_POLICY
            downloadApplicationFile(req, res, next, fileToDownload,
                false, process.env.AZURE_STORAGE_ACCOUNT, process.env.AZURE_STORAGE_ACCESS_KEY, container);
        } else {
            throw new Error();
        }
    } catch (e) {
        console.log(e);
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};

exports.downloadReductionApplicationFile = async function (req, res, next) {
    try {
        let fileToDownload;
        if (req.params.applicationId && req.params.fileGroup) {
            const application = await ReductionApplicationModel.findById(req.params.applicationId);
            if (application) {
                if (req.params.fileGroup.includes('consent-')) {
                    const beneficialOwner = application.companyDetails.beneficialOwners.find((bo) => {
                        return bo._id.toString() === req.params.fileGroup.split('-')[1];
                    });
                    fileToDownload = beneficialOwner.consent.find((file) => file.fileId === req.params.fileId);
                } else {
                    fileToDownload = application[req.params.fileGroup].find((file) => file.fileId === req.params.fileId);
                }
            }
        } else if (req.params.stampDutyId && req.params.group && req.params.fileId) {
            const application = await ReductionApplicationModel.findById(req.params.stampDutyId);
            if (application) {
                let files;
                if (req.params.group === 'remission-order') {
                    files = application.remissionOrder ? application.remissionOrder : [];
                }
                if (req.params.group === 'additional-information') {
                    files = application.additionalInformation ? application.additionalInformation : [];
                }
                fileToDownload = files.find((file) => file.fileId === req.params.fileId);
            }
        } else {
            throw new Error();
        }
        if (fileToDownload) {
            const container = process.env.AZURE_STORAGE_CONTAINER_HOME_OWNER_POLICY
            downloadApplicationFile(req, res, next, fileToDownload,
              false, process.env.AZURE_STORAGE_ACCOUNT, process.env.AZURE_STORAGE_ACCESS_KEY, container);
        } else {
            throw new Error();
        }
    } catch (e) {
        console.log(e);
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};


const downloadApplicationFile = (req, res, next, files, multipleFiles, account, accessKey, container) => {
    const blobService = azure.createBlobService(account, accessKey);
    let blobUrl = "";

    if (multipleFiles) {
        if (files && files.length > 0) {
            for (let idx = 0; idx < files.length; idx++) {

                if (files[idx].files) {
                    const exists = files[idx].files.find((upload) => upload.id.toString() === req.params.fileId);
                    if (exists) {
                        blobUrl = exists.url;
                        break
                    }
                }
            }

        }
    }
    else {
        blobUrl = files.url;
    }

    if (blobUrl.length > 0) {
        const pathFile = blobUrl.split(container + "/");
        let stream = blobService.createReadStream(container, pathFile[1]);
        res.setHeader("content-type", files.mimeType);
        stream.on('error', function () {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
        });
        stream.pipe(res);
    } else {
        const err = new Error('File not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }
};
