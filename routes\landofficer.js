const express = require('express');
const router = express.Router();

const landOfficerController = require('../controllers/landOfficerController');
const uploadController = require('../controllers/uploadController');
const downloadController = require('../controllers/downloadController');

//PAGES
router.get('/dashboard', ensureAuthenticatedLandOfficer, landOfficerController.landOfficerDashboard);
router.post('/dashboard', ensureAuthenticatedLandOfficer, landOfficerController.landOfficerDashboard);
router.get('/create', ensureAuthenticatedLandOfficer, landOfficerController.openNewLandOfficerView);
router.post('/create', ensureAuthenticatedLandOfficer, landOfficerController.createLandOfficer);
router.get('/:id/open', ensureAuthenticatedLandOfficer, landOfficerController.getLandOfficerView);
router.post('/:id/update', ensureAuthenticatedLandOfficer, landOfficerController.updateLandOfficer);
router.post('/:id/submit-review', ensureAuthenticatedLandOfficer, landOfficerController.submitReviewId);
router.delete('/:id/delete', ensureAuthenticatedLandOfficer, landOfficerController.deleteSavedApplication);
router.post('/upload-document', ensureAuthenticated,
    uploadController.uploadFile.fields([{ name: 'fileUploaded', maxCount: 5 }]),
    landOfficerController.storeFiles
);
//GET UPLOADED FILES
router.get('/files', ensureAuthenticated, landOfficerController.getUploadedFiles);
//DELETE UPLOADED FILE
router.delete('/files', ensureAuthenticated, uploadController.deleteFile);
// GET DOWNLOAD STANDARD FILES
router.get("/files/:fileTypeId/:fileId/download", ensureAuthenticated,
    downloadController.downloadFile);

// GET DOWNLOAD STANDARD FILES
router.get("/:landOfficerId/files/:fileTypeId/:fileId/download", ensureAuthenticated,
    downloadController.downloadFile);

function ensureAuthenticatedLandOfficer(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isLandOfficer) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isStampDuty || req.session.authentication.isLandOfficer ||
            req.session.authentication.isPsOfficer || req.session.authentication.isFinance || req.session.authentication.isAuditor) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;
