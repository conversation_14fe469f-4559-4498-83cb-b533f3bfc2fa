{{!-- CONFIRM MODAL --}}
<div class="modal fade" id="submitReviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div id="message_modal">
                    Are you sure you want to submit? The comment that you place here will be visible for the Stamp Duty officer.
                </div>
                <div class="mt-1 text-justify">
                    <label for="modalInputComments"><small></small></label> <br>
                    <textarea class="form-control" name="modalInputComments" id="modalInputComments"
                        placeholder="Add comment..." rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Cancel
                </button>
                <button type="button" class="btn solid royal-blue" id="sendButtone">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    const myFormValidation = $("#submitForm")[0];
    let statusName = '';
    let comments = '';
    let reviewId;
    //let typeSelected = $('#relationType option:selected').val();
    $('#submitReviewModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        statusName = button.data('status');
        reviewId = button.data('id');
    });
    $('#sendButtone').on('click', function () {
        comments = $('#modalInputComments').val();
        if (!myFormValidation.checkValidity()) {
            if (myFormValidation.reportValidity) {
                myFormValidation.reportValidity();
                return;
            }
        }
        $.ajax({
            url: '/land-officer/' + reviewId + '/submit-review',
            type: 'POST',
            data: $("#submitForm").serialize() + '&comment=' + comments + '&reviewId=' + reviewId,
            timeout: 5000,
            success: function () {
                Swal.fire('Success', 'The form has been submitted successfully', 'success').then(() => {
                    location.href = '/land-officer/dashboard';
                });
            },
            error: function () {
                Swal.fire('Error', 'There was an error submitting the form', 'error').then(() => {
                    location.href = '/land-officer/dashboard';
                });
            },
        });
    });

</script>
