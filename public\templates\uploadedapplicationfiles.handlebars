{{#if files}}
    <div class="table-responsive">
        <table class="table table-striped mb-0">
            <thead>
            <tr>
                <th style="width: 60%">Uploaded Files</th>
                {{#unless hideDelete}}
                <th style="width: 20%">Delete</th>
                {{/unless}}
                <th style="width: 20%">Download</th>
            </tr>
            </thead>
            <tbody>
            {{#each files}}
                <tr>
                    <td>{{originalName}}</td>
                    {{#unless ../hideDelete}}
                    <td>
                        <button class="demo-delete-row btn btn-danger btn-xs btn-icon"
                                onclick="deleteFile('{{fileId}}' ,'{{../group}}', '{{ originalName }}');return false">
                            <i class="fa fa-times"></i>
                        </button>
                    </td>
                    {{/unless}}
                    <td>
                        <button class="demo-download-row btn btn-success btn-xs btn-icon"
                                onclick="downloadFile('{{../group}}', '{{fileId}}')">
                            <i class="fa fa-download"></i>
                        </button>
                    </td>
                </tr>
            {{/each}}
            </tbody>
        </table>
    </div> <!-- end .padding -->
{{/if}}
