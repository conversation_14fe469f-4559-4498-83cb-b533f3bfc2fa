const mongoose = require('mongoose');

const sendAttemptsSchema = new mongoose.Schema(
  {
    date: { type: Date, required: true },
    status: {
      type: String,
      enum: ["FAILED", "SENT"],
      required: true
    },
    email: { type: String, required: true },
    user: { type: String, required: true },
    errorMessage: { type: String, required: false }
  },
  {
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
  }
);

const DeclineNoticeSchema = new mongoose.Schema(
  {
    declineDate: { type: Date, required: true },
    applicationType: {
      type: String,
      enum: [
        "STAMP_DUTY_EXEMPTION_APPLICATION",
        "IMPORT_DUTY_WAIVER",
        "REDUCTION_APPLICATION",
        "EXEMPTIONS"
      ],
      required: true
    },
    // This could be required to save "Natural Love & Affection" or "First Time Homeowner", etc.
    applicationSubType: {
      type: String,
      required: false
    },
    applicationId: { type: String, required: true },
    declineReason: { type: String, required: true },
    user: { type: String, required: true },
    status: {
      type: String,
      enum: ["FAILED", "SENT"],
      required: true
    },
    email: { type: String, required: true },
    sendAttempts: [sendAttemptsSchema],
    applicationDetails: {
      applicantName: { type: String, required: false },
      transferorName: { type: String, required: false },
      transfereeName: { type: String, required: false },
      referenceNumber: { type: String, required: false },
      parcelNumber: { type: String, required: false },
      district: { type: String, required: false },
      island: { type: String, required: false },
    }
  },
  {
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
  }
);

DeclineNoticeSchema.index({ applicationId: 1 });
DeclineNoticeSchema.index({ email: 1 });
DeclineNoticeSchema.index({ declineDate: 1 });
DeclineNoticeSchema.index({ applicationType: 1 });
DeclineNoticeSchema.index({ status: 1 });
DeclineNoticeSchema.index({ 'applicationDetails.referenceNumber': 1 });

//Export model
module.exports.DeclineNoticeModel = mongoose.model('declinenotice', DeclineNoticeSchema);

