const PdfPrinter = require('pdfmake');
const moment = require('moment');
const { Base64Encode } = require('base64-stream');
const { pdfLogo } = require('../constants');
const fs = require('fs');

async function getPDFBase64Text(pdfDoc) {
    return new Promise(resolve => {
        let base64String = '';
        const stream = pdfDoc.pipe(new Base64Encode());
        pdfDoc.end();

        stream.on('data', chunk => { base64String += chunk });

        stream.on('end', () => resolve(base64String));
    })
}




exports.generateImportWaiverApplicationPdf = async function (data) {

    let printer = new PdfPrinter({
        Helvetica: {
            normal: 'Helvetica',
            bold: 'Helvetica-Bold',
            italics: 'Helvetica-Oblique',
            bolditalics: 'Helvetica-BoldOblique'
        }
    });



    let docDefinition = {
        defaultStyle: {
            font: 'Helvetica'
        },
        content: [
            {
                // under NodeJS (or in case you use virtual file system provided by pdfmake)
                // you can also pass file names here
                image: pdfLogo.img,
                width: pdfLogo.width,
            },
            { text: "\n" },
            { text: 'Import Duty Waiver - Details information', alignment: 'center', fontSize: 16, bold: true },
            { text: "\n" },
            {
                style: 'table',
                table: {
                    widths: [300, '*'],
                    body: createWaiverDetailsTable(data)
                },
            },
            {
                style: 'table',
                table: {
                    widths: [300, '*'],
                    body: createPersonalDetailsTable(data)
                },
            },
            { text: "APPLICANT DETAILS", alignment: 'left', fontSize: 13, bold: true },
            {
                style: 'table',
                table: {
                    widths: [300, '*'],
                    body: createWaiverApplicantDetailsTable(data)
                },
            },
            { text: "\n" },
            ...createAdditionalApplicantsTable(data),
            { text: "PROPERTY DETAILS", alignment: 'left', fontSize: 13, bold: true },
            {
                style: 'table',
                table: {
                    widths: [300, '*'],
                    body: createPropertyDetailsTable(data)
                },
            },
            {
                style: 'table',
                table: {
                    widths: [300, '*'],
                    body: createWaiverConfirmationTable(data)
                },
            },
            { text: "UPLOADED DOCUMENTS", margin: [0, 10, 0, 5], alignment: 'left', fontSize: 13, bold: true },
            {
                ul: createUploadedDocumentList(data, "import-waiver")
            }
        ],
        styles: {
            header: {
                fontSize: 18,
                bold: true,
                margin: [0, 0, 0, 10]
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [0, 10, 0, 5]
            },
            table: {
                margin: [0, 5, 0, 15]
            },
            tableHeader: {
                bold: true,
                color: 'black'
            }
        },
    };

    let pdfDoc = printer.createPdfKitDocument(docDefinition);
    return await getPDFBase64Text(pdfDoc);

};


exports.generateImportDutyWaiverApprovalPdf = async function (submission, title, ministry, procedureCode) {

    let printer = new PdfPrinter({
        Helvetica: {
            normal: 'Helvetica',
            bold: 'Helvetica-Bold',
            italics: 'Helvetica-Oblique',
            bolditalics: 'Helvetica-BoldOblique'
        }
    });
    const signature = fs.readFileSync('signatures/IMPORT DUTY WAIVER - SIGNATURE.jpg', { encoding: 'base64' });

    const name = submission.filingYourself ? `${submission.firstName} ${submission.lastName}` : `${submission.personalInformation.firstName} ${submission.personalInformation.lastName}`;
    const date = moment().subtract(4, 'h').format('DD/MM/YYYY');
    const valueOfGoods = submission.valueOfGoods.toString().replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",");
    const remittedAmount = submission.remittedAmount.toString().replace(/\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g, ",");
    const applicantNames = [{ firstName: submission.firstName, lastName: submission.lastName }, ...submission.applicants].map((ap) => {
        return ap.firstName + ' ' + ap.lastName;
    });

    let docDefinition = {
        defaultStyle: {
            font: 'Helvetica',
            lineHeight: 1.2
        },
        content: [
            {
                // under NodeJS (or in case you use virtual file system provided by pdfmake)
                // you can also pass file names here
                image: pdfLogo.img,
                width: pdfLogo.width,
                alignment: 'center'
            },
            { text: "\n" },
            { text: ministry, color: "#f2aa0f", alignment: 'center', fontSize: 16, bold: true },
            { text: "\n" },
            { text: "\n" },
            { text: `DEPARTMENT OF CUSTOMS\nTELEPHONE: ************ or 338-5495\nP.O. BOX 206\nPROVIDENCIALES\nTURKS & CAICOS ISLANDS\nBRITISH WEST INDIES`, alignment: 'left', fontSize: 12, bold: true },
            { text: "\n" },
            {
                canvas: [
                    {
                        type: 'polyline',
                        lineWidth: 2,
                        color: "#000000",
                        closePath: true,
                        points: [{ x: 0, y: 0 }, { x: 515, y: 0 }]
                    },
                ],
            },
            { text: "\n" },
            { text: `${date}\n${name}\n${submission.address}\n${submission.addressOptional ? submission.addressOptional + '\n' : ''}${submission.island}\n\nDear ${applicantNames.join(', ')}, \n`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: title, alignment: 'left', fontSize: 11, decoration: 'underline' },

            { text: "\n" },
            { text: `I hereby write to inform you that the above concession was approved by our concessions desk.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Kindly be advised that the above named goods must be used exclusively for the purpose stated in your letter of request, otherwise the full duties will become payable.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `The normal Customs Procedures to be completed, with the Customs Procedure Code ${procedureCode}. There will be a 0% duty rate and a 5% Customs Processing Fee charge on the value of the goods $${valueOfGoods}. Revenue Foregone $${remittedAmount}.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `The Customs Department’s Audit Division will conduct periodic Audits to ensure end use compliance, with the stipulations of this concession.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Sincerely`, alignment: 'left', fontSize: 11, },
            {
                image: 'data:image/png;base64,' + signature,
                width: 80,
                alignment: 'left'
            },
            { text: `Linda Malcolm`, alignment: 'left', fontSize: 11, },
            { text: `Deputy Collector of Customs`, alignment: 'left', fontSize: 11, },

        ],
        styles: {
            header: {
                fontSize: 18,
                bold: true,
                margin: [0, 0, 0, 10]
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [0, 10, 0, 5]
            },
        },
    };

    let pdfDoc = printer.createPdfKitDocument(docDefinition);
    return await getPDFBase64Text(pdfDoc);

};

exports.generateStampDutyExemptionApprovalPdf = async function (submission) {

    let printer = new PdfPrinter({
        Helvetica: {
            normal: 'Helvetica',
            bold: 'Helvetica-Bold',
            italics: 'Helvetica-Oblique',
            bolditalics: 'Helvetica-BoldOblique'
        }
    });

    const signature = fs.readFileSync('signatures/STAMP DUTY EXEMPTION - SIGNATURE.jpg', { encoding: 'base64' });

    const name = submission.filingYourself ? `${submission.firstName} ${submission.lastName}` : `${submission.personalInformation.firstName} ${submission.personalInformation.lastName}`;
    const date = moment().subtract(4, 'h').format('DD/MM/YYYY');
    const applicantNames = [{ firstName: submission.firstName, lastName: submission.lastName }, ...submission.applicants].map((ap) => {
        return ap.firstName + ' ' + ap.lastName;
    });


    let docDefinition = {
        defaultStyle: {
            font: 'Helvetica',
            lineHeight: 1.2
        },
        content: [
            {
                // under NodeJS (or in case you use virtual file system provided by pdfmake)
                // you can also pass file names here
                image: pdfLogo.img,
                width: pdfLogo.width,
                alignment: 'center'
            },
            { text: "\n" },
            {
                canvas: [
                    {
                        type: 'polyline',
                        lineWidth: 2,
                        color: "#000000",
                        closePath: true,
                        points: [{ x: 0, y: 0 }, { x: 515, y: 0 }]
                    },
                ],
            },
            { text: "\n" },
            { text: `${date}\n\n${name}\n${submission.address}\n${submission.addressOptional ? submission.addressOptional + '\n' : ''}${submission.island}\nTurks and Caicos Islands\n\nDear ${applicantNames.join(', ')}, \n`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: "\n" },
            { text: `Kindly be advised that your application for ${applicantNames.join(', ')} for the Stamp Duty Waiver under the Turks & Caicos Islands Citizen Homeowner Policy in regards to Parcel ${submission.propertyDetails.parcel}, ${submission.propertyDetails.island}, has been reviewed and approved by the Ministry of Finance. The next steps are as follows:`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Submit the executed Transfers to the Land Registry, who will then forward them onto the Ministry of Finance;`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `The processing times as per page 11 of the Policy is four (4) weeks after approval for completion of the waiver, which is the Remission that is prepared by the Attorney General’s Chambers;`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Once the Instrument is prepared it must be then signed by the Minister of Finance in order to give it legal effect; and`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Once the signed Remission is received by the Ministry of Finance, that Remission along with the Transfer is then sent back to the Land Registry who will then register the Transfer once they are satisfied that all of their internal processes and procedures have been met.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Kindly note, as per the signed Affidavit, that the property that is the subject of this approval is only to be used as the applicants’ principal place of residence and not as an income property. Should the property not be used in accordance with the Policy or sold within a period of five (5) years the remitted amount will become due and payable. A restriction to this effect will also be placed on the Land Register.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Should you have any further queries, kindly contact the undersigned.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Regards`, alignment: 'left', fontSize: 11, },
            {
                image: 'data:image/png;base64,' + signature,
                width: 230,
                alignment: 'left'
            },
            { text: "Cagina Foster-Lubrin", alignment: 'left', fontSize: 11, },
            { text: "Senior Crown Counsel, Ministry of Finance", alignment: 'left', fontSize: 11, },
            { text: "Stamp Duty Collector", alignment: 'left', fontSize: 11, },

        ],
        styles: {
            header: {
                fontSize: 18,
                bold: true,
                margin: [0, 0, 0, 10]
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [0, 10, 0, 5]
            },
        },
    };

    let pdfDoc = printer.createPdfKitDocument(docDefinition);
    return await getPDFBase64Text(pdfDoc);

};

exports.generateStampDutyReductionApprovalPdf = async function (submission) {

    let printer = new PdfPrinter({
        Helvetica: {
            normal: 'Helvetica',
            bold: 'Helvetica-Bold',
            italics: 'Helvetica-Oblique',
            bolditalics: 'Helvetica-BoldOblique'
        }
    });

    const signature = fs.readFileSync('signatures/STAMP DUTY EXEMPTION - SIGNATURE.jpg', { encoding: 'base64' });

    const name = submission.filingBehalf === 'Company' ? submission.companyDetails.name : `${submission.applicantDetails.firstName} ${submission.applicantDetails.lastName}`;
    const date = moment().subtract(4, 'h').format('DD/MM/YYYY');
    let percentage = '';

    if (submission.propertyDetails.value <= 100000) {
        percentage = '0%';
    } else if (['Grand Turk', 'Salt Cay', 'South Caicos', 'North Caicos', 'Middle Caicos'].includes(submission.propertyDetails.island)) {
        percentage = '4%';
    } else if (['Parrot Cay', 'Pine Cay', 'Ambergris Cay', 'Providenciales', 'West Caicos'].includes(submission.propertyDetails.island)) {
        percentage = '6%';
    }
    const type = submission.filingBehalf === 'Company' ? 'companyDetails' : 'applicantDetails';

    let displayName = submission.filingBehalf === 'Company' ? submission.companyDetails.name : `${submission.applicantDetails.firstName} ${submission.applicantDetails.lastName}`;

    let docDefinition = {
        defaultStyle: {
            font: 'Helvetica',
            lineHeight: 1.2
        },
        content: [
            {
                // under NodeJS (or in case you use virtual file system provided by pdfmake)
                // you can also pass file names here
                image: pdfLogo.img,
                width: pdfLogo.width,
                alignment: 'center'
            },
            { text: "\n" },
            {
                canvas: [
                    {
                        type: 'polyline',
                        lineWidth: 2,
                        color: "#000000",
                        closePath: true,
                        points: [{ x: 0, y: 0 }, { x: 515, y: 0 }]
                    },
                ],
            },
            { text: "\n" },
            { text: `${date}\n\n${name}\n${submission[type].address}\n${submission[type].addressOptional ? submission[type].addressOptional + '\n' : ''}${submission[type].island}\nTurks and Caicos Islands\n\nDear ${displayName}, \n`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: "\n" },
            { text: `Kindly be advised that your application for the Stamp Duty Rate Reduction under the Turks and Caicos Islands Stamp Duty Reduction Policy for Turks & Caicos Islands Status Holder and British Overseas Territory Citizen (BOTC) in regards to Parcel  ${submission.propertyDetails.parcel}, has been reviewed and approved by the Ministry of Finance and the applicable rate to be paid is ${percentage}. The next steps are as follows:`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Submit the executed Transfers to the Land Registry, who will then forward them onto the Ministry of Finance;`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `The processing times as per page 11 of the Policy is four (4) weeks after approval for completion of the waiver, which is the Remission that is prepared by the Attorney General’s Chambers;`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Once the Instrument is prepared it must be then signed by the Minister of Finance in order to give it legal effect; and`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Once the signed Remission is received by the Ministry of Finance, that Remission along with the Transfer is then sent back to the Land Registry who will then register the Transfer once they are satisfied that all of their internal processes and procedures have been met.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Kindly note, as per the signed Affidavit, that the property that is the subject of this approval is only to be used as the applicants’ principal place of residence and not as an income property. Should the property not be used in accordance with the Policy or sold within a period of five (5) years of the date of acquisition for residential property and a period of ten (10) years from the date of acquisition for commercial property to a non-Turks & Caicos Islander Status Card Holder or non- British Oversees Territories Citizen, that the remitted amount will become due and payable. A restriction to this effect will also be placed on the Land Register.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Should you have any further queries, kindly contact the undersigned.`, alignment: 'left', fontSize: 11, },
            { text: "\n" },
            { text: `Regards`, alignment: 'left', fontSize: 11, },
            {
                image: 'data:image/png;base64,' + signature,
                width: 230,
                alignment: 'left'
            },
            { text: "Cagina Foster-Lubrin", alignment: 'left', fontSize: 11, },
            { text: "Senior Crown Counsel, Ministry of Finance", alignment: 'left', fontSize: 11, },
            { text: "Stamp Duty Collector", alignment: 'left', fontSize: 11, },

        ],
        styles: {
            header: {
                fontSize: 18,
                bold: true,
                margin: [0, 0, 0, 10]
            },
            subheader: {
                fontSize: 16,
                bold: true,
                margin: [0, 10, 0, 5]
            },
        },
    };

    let pdfDoc = printer.createPdfKitDocument(docDefinition);
    return await getPDFBase64Text(pdfDoc);

};


function createWaiverDetailsTable(application) {
    let tableBody = [];

    tableBody.push([{ text: 'Import Duty Waiver for Existing Homeowners?', style: 'tableHeader' },
    { text: formatBoolean(application.applicationType === "existing") }]);

    tableBody.push([{ text: 'Import Duty Waiver for Existing Homeowners in the event of a man made or natural disaster?', style: 'tableHeader' },
    { text: formatBoolean(application.applicationType === "disaster") }]);

    tableBody.push([{ text: 'Import Duty Waiver for First-Time Homeowners?', style: 'tableHeader' },
    { text: formatBoolean(application.applicationType === "first-time") }]);
    return tableBody;
}


function createPersonalDetailsTable(application) {
    let tableBody = [];

    tableBody.push([{ text: 'Reference Number', style: 'tableHeader' },
    { text: application.referenceNr }]);
    tableBody.push([{ text: 'Date application has been submitted', style: 'tableHeader' },
    { text: moment.utc(application.createdAt).subtract(4, 'h').format('DD/MM/YYYY hh:mm a') }]);

    tableBody.push([{ text: 'Are you filing this application on behalf of yourself?', style: 'tableHeader' },
    { text: formatBoolean(application.filingYourself) }]);

    if (application.filingYourself === false) {
        tableBody.push([{ text: 'First Name', style: 'tableHeader' }, { text: application.personalInformation.firstName }]);
        tableBody.push([{ text: 'Last Name', style: 'tableHeader' }, { text: application.personalInformation.lastName }]);
        tableBody.push([{ text: 'Firm', style: 'tableHeader' }, { text: application.personalInformation.firm }]);

        if (application.personalInformation.address) {
            tableBody.push([{ text: 'Address', style: 'tableHeader' }, { text: application.personalInformation.address }]);
        }
        if (application.personalInformation.addressOptional) {
            tableBody.push([{ text: 'Address optional', style: 'tableHeader' }, { text: application.personalInformation.addressOptional }]);
        }
        if (application.personalInformation.island) {
            tableBody.push([{ text: 'Island', style: 'tableHeader' }, { text: application.personalInformation.island }]);
        }

        tableBody.push([{ text: 'Phone Number', style: 'tableHeader' }, { text: application.personalInformation.phone }]);
        tableBody.push([{ text: 'E-mail', style: 'tableHeader' }, { text: application.personalInformation.email }]);
    }

    return tableBody;
}

function createWaiverApplicantDetailsTable(application) {
    let tableBody = [];
    tableBody.push([{ text: 'Gender', style: 'tableHeader' }, { text: application.gender }]);
    tableBody.push([{ text: 'First Name', style: 'tableHeader' }, { text: application.firstName }]);
    tableBody.push([{ text: 'Last Name', style: 'tableHeader' }, { text: application.lastName }]);
    tableBody.push([{ text: 'Date of Birth', style: 'tableHeader' }, { text: formatDate(application.dateOfBirth, 'YYYY-MM-DD') }]);
    tableBody.push([{ text: 'Address', style: 'tableHeader' }, { text: application.address }]);
    tableBody.push([{ text: 'Address (Optional)', style: 'tableHeader' }, { text: application.addressOptional }]);
    tableBody.push([{ text: 'Island', style: 'tableHeader' }, { text: application.island }]);
    tableBody.push([{ text: 'Phone Number', style: 'tableHeader' }, { text: application.phone }]);
    tableBody.push([{ text: 'E-mail', style: 'tableHeader' }, { text: application.email }]);
    tableBody.push([{ text: 'Marital Status', style: 'tableHeader' }, { text: application.maritalStatus }]);
    tableBody.push([{ text: 'Applicant is a Turks & Caicos Islander Status Holder?', style: 'tableHeader' },
    { text: formatBoolean(application.documentType === 'status-card') }]);
    if (application.documentType === 'status-card') {
        tableBody.push([{ text: 'Status card number', style: 'tableHeader' },
        { text: application.statusCardNumber }]);
    }
    tableBody.push([{ text: 'Applicant is a British Overseas Territories Citizen?', style: 'tableHeader' },
    { text: formatBoolean(application.documentType === 'botc') }]);
    tableBody.push([{ text: 'Are there any additional applicants?', style: 'tableHeader' }, { text: formatBoolean(application.additionalApplicants) }]);
    return tableBody;
}

function createAdditionalApplicantsTable(application) {

    if (application.additionalApplicants === false) {
        return [];
    } else {
        console.log(application)
        let tables = [];
        let applicantNumber = 1;
        for (const applicant of application.applicants) {
            tables.push({ text: `Applicant  (${applicantNumber})`, fontSize: 13, alignment: 'left', });
            tables.push(...generateApplicantTable(applicant));
            applicantNumber++;
        }
        return tables;

    }

}

function createPropertyDetailsTable(application) {
    let tableBody = [];

    tableBody.push([{ text: 'Parcel Number', style: 'tableHeader' }, { text: application.propertyDetails?.parcel || '' }]);
    tableBody.push([{ text: 'District', style: 'tableHeader' }, { text: application.propertyDetails?.district }]);
    tableBody.push([{ text: 'Island', style: 'tableHeader' }, { text: application.propertyDetails?.island }]);
    tableBody.push([{ text: 'Market value of the property', style: 'tableHeader' }, { text: application.propertyDetails?.value }]);
    tableBody.push([{ text: 'Valuation Date', style: 'tableHeader' }, { text: formatDate(application.propertyDetails?.valuationDate, 'YYYY-MM-DD') }]);
    tableBody.push([{ text: 'Name of Notary', style: 'tableHeader' }, { text: application.propertyDetails?.notaryName }]);
    tableBody.push([{ text: 'Country of Notary', style: 'tableHeader' }, { text: application.propertyDetails?.notaryCountry }]);
    if (application.sellers !== undefined) {
        tableBody.push([{ text: 'Are there any sellers?', style: 'tableHeader' }, { text: formatBoolean(application.sellers?.length > 0) }]);
    }

    return tableBody;
}


function generateApplicantTable(applicant) {
    const tables = [];
    const tableElement = {
        style: 'table',
        table: {
            widths: [300, '*'],
            body: []
        },
    };

    tableElement.table.body.push([{ text: 'Gender', style: 'tableHeader' }, { text: applicant.gender }]);
    tableElement.table.body.push([{ text: 'First Name', style: 'tableHeader' }, { text: applicant.firstName }]);
    tableElement.table.body.push([{ text: 'Last Name', style: 'tableHeader' }, { text: applicant.lastName }]);
    tableElement.table.body.push([{ text: 'Date of Birth', style: 'tableHeader' }, { text: formatDate(applicant.dateOfBirth, "YYYY-MM-DD") }]);
    tableElement.table.body.push([{ text: 'Address', style: 'tableHeader' }, { text: applicant.address }]);
    tableElement.table.body.push([{ text: 'Address (Optional)', style: 'tableHeader' }, { text: applicant.addressOptional }]);
    tableElement.table.body.push([{ text: 'Island', style: 'tableHeader' }, { text: applicant.island }]);
    tableElement.table.body.push([{ text: 'Phone Number', style: 'tableHeader' }, { text: applicant.phone }]);
    tableElement.table.body.push([{ text: 'E-mail', style: 'tableHeader' }, { text: applicant.email }]);
    tableElement.table.body.push([{ text: 'Is this applicant your spouse?*', style: 'tableHeader' }, { text: formatBoolean(applicant.isSpouse) }]);
    tables.push(tableElement);
    return tables;
}


function createUploadedDocumentList(application, type) {
    let files = [];
    let uploadedFiles = [];

    if (type === "exemption") {
        uploadedFiles = [...application.affidavit, ...application.marriageCertificates, ...application.birthCertificate,
        ...application.parentsDocuments, ...application.signedAgreement, ...application.landRegister, ...application.valuation,
        ...application.statusCard, ...application.botcCertificate
        ];
    }
    else if (type === "import-waiver") {
        uploadedFiles = [...application.affidavit, ...application.marriageCertificates, ...application.birthCertificate,
        ...application.parentsDocuments, ...application.itemsInvoices, ...application.buildingPictures, ...application.buildingPermit,
        ...application.landRegister, ...application.valuation, ...application.statusCard, ...application.botcCertificate
        ];
    }

    if (uploadedFiles.length > 0) {
        files = uploadedFiles.map((f) => f.originalName);
    }
    return files
}

function createWaiverConfirmationTable(application) {
    let tableBody = [];

    tableBody.push([{ text: 'Expected date to finish construction/refurbishment/renovation', style: 'tableHeader' }, { text: formatDate(application.finishConstruction, 'YYYY-MM-DD') }]);
    tableBody.push([{
        text: 'I, the applicant(s) understand and accept that, in order to ensure the intended benefit of ' +
            'the scheme, if the property is sold within 5 years of the date of refurbishment, that I am/we are liable to pay ' +
            'the import duty that would have been payable but for the concession; and that an appropriate restriction will ' +
            'be entered regarding the 5-year time period on the Turks & Caicos Land Register.', style: 'tableHeader'
    },
    { text: formatBoolean(application.confirmation1) }]);
    if (application.applicationType === "existing" || application.applicationType === "disaster") {
        tableBody.push([{
            text: 'I, the applicant(s) hereby declare that the property is residential and owned for ten years or more.',
            style: 'tableHeader'
        }, { text: formatBoolean(application.confirmation2) }]);
        tableBody.push([{
            text: 'I, the applicant(s) hereby declare that I actually occupy the home, and the home is ' +
                'considered my legal residence for all purposes.', style: 'tableHeader'
        },
        { text: formatBoolean(application.confirmation3) }]);

        if (application.applicationType === "existing") {
            tableBody.push([{ text: 'I, the applicant(s) hereby declare that the items imported are included in the list as outlined in the Policy.', style: 'tableHeader' },
            { text: formatBoolean(application.confirmation4) }]);
        }
    }
    else {

        tableBody.push([{
            text: 'I, the applicant(s) hereby declare that construction is geared towards New Homes or property ' +
                'only for use as principal private residence. That is, Primary residence only, no investment properties.',
            style: 'tableHeader'
        }, { text: formatBoolean(application.confirmation5) }]);
        tableBody.push([{
            text: 'I, the applicant(s) hereby declare that construction is the first residential property the ' +
                'applicant(s) has(/have) constructed within the Turks and Caicos Islands.', style: 'tableHeader'
        },
        { text: formatBoolean(application.confirmation6) }]);
        tableBody.push([{
            text: 'I, the applicant(s) hereby declare that if the application is approved, I will not dispose ' +
                'of any of the goods imported under the import duty relief (through sale) within a five (5) year period.',
            style: 'tableHeader'
        }, { text: formatBoolean(application.confirmation7) }]);
        tableBody.push([{ text: 'All applicants are first-time buyers of a property within the Turks and Caicos Islands.', style: 'tableHeader' },
        { text: formatBoolean(application.confirmation8) }]);
    }
    tableBody.push([{
        text: 'The facts which I depose to and the information contained in the application therein are true to ' +
            'the best of my knowledge and belief in support of an application for Import Duty Concession under the First Time Homeowners Policy', style: 'tableHeader'
    },
    { text: formatBoolean(application.confirmation9) }]);
    return tableBody;
}


function formatBoolean(boolValue) {
    if (boolValue === null || boolValue === undefined) {
        return "";
    }
    return (boolValue ? "Yes" : "No")
}

function formatDate(date, format) {
    if (date) {
        if (typeof (date) === "string") {
            return moment(date).format(format);
        }
        else {
            date = new Date(date.getTime() + (date.getTimezoneOffset() * 60000));
            return moment(date).format(format);
        }
    } else {
        return '';
    }
}
