const { DeclineNoticeModel } = require('../models/declineNotice');
//const ExemptionModel = require('../models/exemptions');
const { ImportDutyWaiverApplicationModel } = require('../models/importDutyWaiverApplication');
const { StampDutyApplicationModel } = require('../models/stampDutyExemptionApplication');
const { ReductionApplicationModel } = require('../models/stampDutyReductionApplication');
const MailController = require('../controllers/mailController');
const MailFormatter = require('../controllers/mailFormatController');

exports.createDeclineNotice = async function (body) {
  const declineNotice = new DeclineNoticeModel({
    declineDate: new Date(),
    status: 'FAILED', // Default status until email is sent
    ...body
  });
  await declineNotice.save();

  return declineNotice;
};

const sendDeclineNotice = async function (declineNoticeId, username) {
  const declineNotice = await DeclineNoticeModel.findById(declineNoticeId);
  if (!declineNotice) {
    return { success: false, message: 'Decline notice not found' };
  }

  const models = {
    STAMP_DUTY_EXEMPTION_APPLICATION: StampDutyApplicationModel,
    IMPORT_DUTY_WAIVER: ImportDutyWaiverApplicationModel,
    REDUCTION_APPLICATION: ReductionApplicationModel,
    // EXEMPTIONS: ExemptionModel // TODO: Implement decline notice for client
  };

  const emailGenerator = {
    STAMP_DUTY_EXEMPTION_APPLICATION: MailFormatter.generateEmailStampDutyExemptionApplicationDeclined,
    IMPORT_DUTY_WAIVER: MailFormatter.generateEmailImportDutyWaiverDecline,
    REDUCTION_APPLICATION: MailFormatter.generateEmailReductionApplicationDeclined,
    // EXEMPTIONS: "NOT_IMPLEMENTED" // TODO: Implement decline notice for client
  };

  const emailSubject = {
    STAMP_DUTY_EXEMPTION_APPLICATION: 'Stamp Duty Exemption Application denied',
    IMPORT_DUTY_WAIVER: 'Import Duty Waiver Application denied',
    REDUCTION_APPLICATION: 'Stamp Duty Reduction Application denied',
    // EXEMPTIONS: "NOT_IMPLEMENTED" // TODO: Implement decline notice for client
  };

  if (!models[declineNotice.applicationType] || !emailGenerator[declineNotice.applicationType]) {
    return { success: false, message: 'Application type not found' };
  }

  const application = await models[declineNotice.applicationType].findById(declineNotice.applicationId);
  if (!application) {
    return { success: false, message: 'Application not found' };
  }

  try {
    const emailContent = emailGenerator[declineNotice.applicationType](application, declineNotice.declineReason);
    await MailController.asyncSend([declineNotice.email],
      emailSubject[declineNotice.applicationType],
      emailContent.textString,
      emailContent.htmlString
    );
    declineNotice.sendAttempts.push({
      date: new Date(),
      status: 'SENT',
      email: declineNotice.email,
      user: username
    });
    declineNotice.status = 'SENT';
    await declineNotice.save();
    return { success: true, emailSent: true, message: 'Decline notice sent' };
  } catch (e) {
    console.log("error: ", e);
    declineNotice.sendAttempts.push({
      date: new Date(),
      status: 'FAILED',
      email: declineNotice.email,
      errorMessage: e.message,
      user: username
    });
    declineNotice.status = declineNotice.status === 'FAILED' ? 'FAILED' : 'SENT';
    await declineNotice.save();
    return { success: true, emailSent: false, message: 'Decline notice email failed' };
  }
}

exports.sendDeclineNotice = sendDeclineNotice;

exports.getDashboard = async function (req, res) {
  try {
    // Extract filter parameters from query
    const {
      name,
      referenceNumber,
      applicationType,
      declinedAfter,
      declinedBefore,
      status
    } = req.query;

    // Build filter query
    const query = {};

    // Name filter (search across multiple fields)
    if (name && name.trim() !== '') {
      const nameRegex = new RegExp(name.trim(), 'i');
      query.$or = [
        { 'applicationDetails.applicantName': nameRegex },
        { 'applicationDetails.transferorName': nameRegex },
        { 'applicationDetails.transfereeName': nameRegex }
      ];
    }

    // Reference number filter
    if (referenceNumber && referenceNumber.trim() !== '') {
      query['applicationDetails.referenceNumber'] = new RegExp(referenceNumber.trim(), 'i');
    }

    // Application type filter
    if (applicationType && applicationType.trim() !== '') {
      query.applicationType = applicationType;
    }

    // Date range filters
    if (declinedAfter || declinedBefore) {
      query.declineDate = {};

      if (declinedAfter) {
        query.declineDate.$gte = new Date(declinedAfter);
      }

      if (declinedBefore) {
        // Set time to end of day for the "before" date
        const beforeDate = new Date(declinedBefore);
        beforeDate.setHours(23, 59, 59, 999);
        query.declineDate.$lte = beforeDate;
      }
    }

    // Status filter
    if (status && status.trim() !== '') {
      query.status = status;
    }

    // Execute query with filters
    const declineNotices = await DeclineNoticeModel.find(query).sort({ declineDate: -1 });

    // Pass filters and results to the view
    return res.render('decline-notices/dashboard', {
      declineNotices,
      filters: {
        name,
        referenceNumber,
        applicationType,
        declinedAfter,
        declinedBefore,
        status
      }
    });
  } catch (e) {
    console.log("error: ", e);
    res.redirect('/');
  }
};

exports.getDeclineNotice = async function (req, res) {
  try {
    const declineNotice = await DeclineNoticeModel.findById(req.params.id);
    return res.send(declineNotice);
  } catch (e) {
    console.log("error: ", e);
    res.redirect('/');
  }
};

exports.resendDeclineNotice = async function (req, res) {
  try {
    if (req.body.email) {
      const declineNotice = await DeclineNoticeModel.findById(req.params.id);
      if (!declineNotice) {
        return res.status(404).json({ success: false, message: 'Decline notice not found' });
      }
      declineNotice.email = req.body.email;
      await declineNotice.save();
    }

    const result = await sendDeclineNotice(req.params.id, req.session.user.username);
    return res.status(200).json(result);
  } catch (e) {
    console.log("error: ", e);
    return res.status(500).json({ success: false, message: 'An error occurred while resending the notice' });
  }
};


