const ExemptionModel = require('../models/exemptions');
const uploadController = require('../controllers/uploadController');
const landOfficerController = require('../controllers/landOfficerController');
const MailController = require('../controllers/mailController');
const MailFormatter = require('../controllers/mailFormatController');
const httpConstants = require('http2').constants;


function setFilesInformation(files, id, req) {
    files.forEach((file) => {
        if (req.body.files) {
            file.present = !!(req.body.files[file.id] && req.body.files[file.id]["present"]);
        }
        const uploadFiles = landOfficerController.getTempUploadFiles(req.session, file.id);

        if (uploadFiles) {
            uploadFiles.forEach((f) => {
                const name = file.internal.replace(/[\s’'/()]/g, '');
                uploadController.moveUpload(f, name, id);
                f.url = f.url.replace(name, id + "/" + name)
            });
            file.uploadFiles = uploadFiles;
        }
    });

    return files;
}

exports.getDashboard = async function (req, res) {
    try {
        let query = [];
        let status = [];
        let filters = {
            "searchFilter": req.body.searchFilter,
            "exemptionType": req.body.exemptionType,
            "searchDeclined": !!req.body.searchDeclined,
            "searchCompleted": !!req.body.searchCompleted,
            "searchSaved": !!req.body.searchSaved,
            "searchRequested": !!req.body.searchRequested
        };
        status.push('SUBMITTED');
        if (!req.body.searchDeclined && !req.body.searchCompleted
            && !req.body.searchSaved && !req.body.searchRequested) {
            status.push('SAVED', 'REQUESTED', 'TRANSFER PENDING')
        }

        if (req.body.searchDeclined) { status.push('DECLINED') }
        if (req.body.searchCompleted) { status.push('COMPLETED') }
        if (req.body.searchSaved) { status.push('SAVED') }
        if (req.body.searchRequested) { status.push('REQUESTED') }

        query.push({ "status": { $in: status } }, { "type": 'FINANCE' });
        if (filters.exemptionType) {
            query.push({ 'exemptionType': filters.exemptionType });
        }

        if (req.body.searchFilter && req.body.searchFilter.length > 2) {
            query.push({
                $or: [{ 'transferorName': { $regex: req.body.searchFilter, $options: 'i' } },
                { 'parcelNumber': { $regex: req.body.searchFilter, $options: 'i' } },
                ],
            })
        }
        let financeOfficers = await ExemptionModel.find({ $and: query });

        res.render('finance-officer/dashboard',
            {
                financeOfficers,
                filters: filters
            });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/land-officer/dashboard');
    }
};

exports.getFinanceOfficerView = async function (req, res) {
    try {
        req.session.files = {};
        const financeOfficer = await ExemptionModel.findById(req.params.id);
        let isEditable = false;
        if (financeOfficer) {
            if (financeOfficer.status === "SAVED" || financeOfficer.status === "REQUESTED") {
                isEditable = true;
            }
        }
        res.render('finance-officer/open-view-form',
            {
                financeOfficer: financeOfficer,
                isEditable: isEditable,
            });

    } catch (e) {
        console.log(e);
        return res.status(500).end();
    }
};

exports.updateFinanceOfficer = async function (req, res) {
    try {
        let financeOfficer = await ExemptionModel.findById(req.params.id);

        if (financeOfficer) {
            console.log(financeOfficer._id);
            financeOfficer.transferorName = req.body.transferorName;
            financeOfficer.parcelNumber = req.body.parcelNumber;
            financeOfficer.parcelTextNumber = req.body.parcelTextNumber;
            financeOfficer.district = req.body.district;
            financeOfficer.island = req.body.island;
            financeOfficer.value = req.body.value;

            if (req.body.status === 'submit-application') {
                financeOfficer.status = "SUBMITTED";
                financeOfficer.comments.push({
                    email: req.session.user.username,
                    date: new Date(),
                    commentStatus: financeOfficer.status,
                    comment: req.body.comment
                });
            }
            financeOfficer.submittedOfficer = {
                username: req.session.user.name,
                email: req.session.user.username,
                submittedDate: new Date(),
                comment: req.body.comment
            };

            financeOfficer.files = setFilesInformation(financeOfficer.files, financeOfficer._id, req);
            await financeOfficer.save();

            if (financeOfficer.status === "SUBMITTED") {
                let email = MailFormatter.generateEmail(financeOfficer);
                await MailController.asyncSend([process.env.EMAIL_STAMP_DUTY_OFFICER_RECIPIENT],
                    'Stamp Duty Exemption Program',
                    email.textString,
                    email.htmlString
                );
            }
        }

        return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true });

    } catch (e) {
        console.log("error: ", e);
        res.redirect('/finance-officer/dashboard');
    }
};

