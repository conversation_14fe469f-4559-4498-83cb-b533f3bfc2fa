{"name": "qnamanagement", "version": "0.0.0", "private": true, "scripts": {"start": "node server.js", "devstart": "nodemon -r dotenv/config server.js", "build": "echo 'build script executed'", "gencsv": "node applicationCSV.js", "genSMBCSV": "node applicationSMBCSV.js", "updateCommentUsers": "node updateCommentUsers.js", "genDeclinedCSV": "node declinedApplicationsCSV.js", "updatepaid": "node paidApplications.js"}, "dependencies": {"@handlebars/allow-prototype-access": "^1.0.3", "@sendgrid/mail": "^7.1.0", "async": "^3.1.1", "azure-storage": "^2.10.3", "azure-storage-fs": "^0.4.0", "base64-stream": "^1.0.0", "body-parser": "latest", "connect-redis": "^5.0.0", "cookie-parser": "~1.4.4", "csv-express": "^1.2.2", "debug": "~4.1.1", "express": "~4.17.1", "express-async-errors": "^3.1.1", "express-handlebars": "^7.0.7", "express-session": "^1.17.0", "handlebars": "^4.7.3", "hbs": "^4.1.0", "http-errors": "^1.7.3", "json-csv": "^3.0.1", "moment": "^2.24.0", "mongoose": "^5.9.1", "morgan": "^1.9.1", "multer": "^1.4.2", "multer-azure-storage": "^0.2.0", "node-excel-export": "^1.4.4", "node-fetch": "^2.6.1", "nodemailer": "^6.4.17", "pdfmake": "^0.1.68", "redis": "^3.0.2", "uuid": "^8.3.1"}, "devDependencies": {"acorn": "^7.1.0", "dotenv": "^8.2.0", "eslint": "^8.14.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "nodemon": "^2.0.2", "prettier": "^2.6.2", "request": "^2.88.2", "rollup": "^1.31.1"}}