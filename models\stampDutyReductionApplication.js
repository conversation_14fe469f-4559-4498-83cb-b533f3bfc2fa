const mongoose = require('mongoose');

const commentsSchema = new mongoose.Schema({
    date: { type: Date, required: true },
    internalComments: { type: String, required: false },
    declineReason: { type: String, required: false },
    user: { type: String, required: true },
    status: { type: String, required: true },
});

const validationSchema = new mongoose.Schema({
    applicantDetails: { type: Boolean, required: false },
    companyDetails: { type: Boolean, required: false },
    additionalApplicants: { type: Boolean, required: false },
    propertyDetails: { type: Boolean, required: false },
    affidavit: { type: Boolean, required: false },
    marriageCertificates: { type: Boolean, required: false },
    birthCertificate: { type: Boolean, required: false },
    parentsDocuments: { type: Boolean, required: false },
    landRegister: { type: Boolean, required: false },
    valuation: { type: Boolean, required: false },
    statusCard: { type: Boolean, required: false },
    botcCertificate: { type: <PERSON>olean, required: false },
});

const requestInformationSchema = new mongoose.Schema({
    requests: [String],
    createdBy: { type: String, required: true },
    createdAt: { type: Date, required: true },
    internalComments: { type: String, required: false },
    managementComments: { type: String, required: false },
    userComments: { type: String, required: false },
    submittedAt: { type: Date, required: false },
});

const tierSchema = new mongoose.Schema({
    validations: validationSchema,
    approvedAt: { type: Date, required: false },
    completedAt: { type: Date, required: false },
    declinedAt: { type: Date, required: false },
    conflictedAt: { type: Date, required: false },
    comments: [commentsSchema],
    username: { type: String, required: false },
});

const fileSchema = new mongoose.Schema({
    fileId: { type: String, required: true },
    fieldName: { type: String, required: true },
    originalName: { type: String, required: true },
    encoding: { type: String, required: true },
    mimeType: { type: String, required: true },
    blobName: { type: String, required: true },
    container: { type: String, required: true },
    blob: { type: String, required: true },
    blobType: { type: String, required: true },
    size: { type: String, required: true },
    etag: { type: String, required: true },
    url: { type: String, required: true }
});

const propertyDetailsSchema = new mongoose.Schema({
    parcel: { type: String, required: true },
    district: { type: String, required: true },
    island: { type: String, required: true },
    value: { type: Number, required: true },
    valuationDate: { type: Date, required: false },
    notaryName: { type: String, required: true },
    notaryCountry: { type: String, required: true },
});

const applicantSchema = new mongoose.Schema({
    gender: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    dateOfBirth: { type: Date, required: true },
    address: { type: String, required: true },
    addressOptional: { type: String, required: false },
    island: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    isSpouse: { type: Boolean, required: true },
});

const beneficialOwnerSchema = new mongoose.Schema({
    gender: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    dateOfBirth: { type: Date, required: true },
    address: { type: String, required: true },
    addressOptional: { type: String, required: false },
    island: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    consent: [fileSchema]
});

const applicantDetailsSchema = new mongoose.Schema({
    gender: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    dateOfBirth: { type: Date, required: true },
    address: { type: String, required: true },
    addressOptional: { type: String, required: false },
    island: { type: String, required: true },
    phone: { type: String, required: true },
    email: { type: String, required: true },
    maritalStatus: { type: String, required: true },
    additionalApplicants: { type: Boolean, required: true },
    applicants: [applicantSchema],
});

const companyDetailsSchema = new mongoose.Schema({
    name: { type: String, required: true },
    address: { type: String, required: true },
    addressOptional: { type: String, required: false },
    island: { type: String, required: true },
    phone: { type: String, required: true },
    registrationNumber: { type: String, required: true },
    email: { type: String, required: true },
    beneficialOwners: [beneficialOwnerSchema],
});

const StampDutyReductionApplicationSchema = new mongoose.Schema(
    {
        referenceNr: { type: String, required: true, unique: true },
        status: { type: String, required: true },
        intentOfProperty: { type: String, required: true },
        filingBehalf: { type: String, required: true },
        documentType: { type: String, required: true },
        statusCardNumber: { type: String, required: false },
        applicantDetails: { type: applicantDetailsSchema, required: false },
        companyDetails: { type: companyDetailsSchema, required: false },
        propertyDetails: { type: propertyDetailsSchema, required: true },
        confirmation1: { type: Boolean, required: true },
        confirmation2: { type: Boolean, required: true },
        confirmation3: { type: Boolean, required: true },
        confirmation4: { type: Boolean, required: true },
        confirmation5: { type: Boolean, required: true },
        affidavit: [fileSchema],
        marriageCertificates: [fileSchema],
        birthCertificate: [fileSchema],
        parentsDocuments: [fileSchema],
        landRegister: [fileSchema],
        valuation: [fileSchema],
        statusCard: [fileSchema],
        botcCertificate: [fileSchema],
        // Management information
        remittedAmount: { type: Number, required: false, default: 0 },
        stampDutyOfficerTier: tierSchema,
        psOfficerTier: tierSchema,
        auditReady: { type: Boolean, required: false, default: false },
        informationRequests: [requestInformationSchema],
        remissionOrder: [fileSchema],
        additionalInformation: [fileSchema],
    },
    {
        timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
    }
);



//Export model
module.exports.ReductionApplicationModel = mongoose.model('stampdutyreductionapplications', StampDutyReductionApplicationSchema);

