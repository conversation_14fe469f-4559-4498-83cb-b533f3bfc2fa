const express = require('express');
const router = express.Router();


const psController = require('../controllers/psSubmissionController');
const uploadController = require('../controllers/uploadController');

//PAGES
router.get('/dashboard', ensureAuthenticated, psController.getDashboard);
router.post('/dashboard', ensureAuthenticated, psController.getDashboard);
router.get('/:submissionId/open', ensureAuthenticated, psController.getSubmissionsView);
router.post('/:submissionId/upload-document', ensureAuthenticated,
uploadController.uploadFile.fields([{name: 'fileUploaded', maxCount: 5}]), psController.uploadSignedDocument
);
router.post('/:submissionId/validate-remission', ensureAuthenticated, psController.validateRemission);

// Stamp Duty Exemption

router.get('/exemption-application/:submissionId/update', ensureAuthenticated, psController.getStampDutyExemptionApplicationView);
router.post('/exemption-application/:submissionId/update', ensureAuthenticated, psController.saveStampDutyExemptionApplication);
router.post('/exemption-application/:submissionId/approve', ensureAuthenticated, psController.approveStampDutyExemptionApplication);
router.post('/exemption-application/:submissionId/return', ensureAuthenticated, psController.returnStampDutyExemptionApplication);

// Stamp Duty Reduction

router.get('/reduction-application/:submissionId/update', ensureAuthenticated, psController.getReductionApplicationView);
router.post('/reduction-application/:submissionId/update', ensureAuthenticated, psController.saveReductionApplication);
router.post('/reduction-application/:submissionId/approve', ensureAuthenticated, psController.approveReductionApplication);
router.post('/reduction-application/:submissionId/return', ensureAuthenticated, psController.returnReductionApplication);

// Import Duty Waiver

router.get('/import-duty-application/:submissionId/update', ensureAuthenticated, psController.getImportDutyWaiverApplicationView);



function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isPsOfficer) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;
