const multer = require('multer');
const multerAzureStorage = require('multer-azure-storage');
const ExemptionModel = require('../models/exemptions');
const landOfficerController = require('../controllers/landOfficerController');
const fs = require('azure-storage-fs').blob(
  process.env.AZURE_STORAGE_ACCOUNT,
  process.env.AZURE_STORAGE_ACCESS_KEY,
  process.env.AZURE_STORAGE_CONTAINER_STAMP_DUTY
);


exports.uploadFile = multer({
  storage: new multerAzureStorage({
    azureStorageConnectionString: `DefaultEndpointsProtocol=https;AccountName=${process.env.AZURE_STORAGE_ACCOUNT};AccountKey=${process.env.AZURE_STORAGE_ACCESS_KEY};EndpointSuffix=core.windows.net`,
    containerName: process.env.AZURE_STORAGE_CONTAINER_STAMP_DUTY,
    containerSecurity: 'blob',
  }),
});

exports.uploadFileStampDutyApplication = multer({
  storage: new multerAzureStorage({
    azureStorageConnectionString: `DefaultEndpointsProtocol=https;AccountName=${process.env.AZURE_STORAGE_ACCOUNT};AccountKey=${process.env.AZURE_STORAGE_ACCESS_KEY};EndpointSuffix=core.windows.net`,
    containerName: process.env.AZURE_STORAGE_CONTAINER_HOME_OWNER_POLICY,
    containerSecurity: 'blob',
  }),
});

const doMoveUpload = async function (file, fileType, reviewId) {
  //moves the uploaded file in azure to a subfolder with the landofficer id
  if (file) {
    const path = reviewId ? reviewId + '\\' + file.blobName.replace('fileUploaded', fileType) :
      file.blobName.replace('fileUploaded', fileType);
    await fs.rename(file.blobName, path, function (err) {
      if (err) console.log('ERROR: ' + err);
    });
  }
};
exports.moveUpload = doMoveUpload;

exports.deleteFile = async function (req, res) {
  try {
    if (req.body.landOfficer) {
      const landOfficer = await ExemptionModel.findById(req.body.landOfficer);

      if (landOfficer) {
        if (req.body.fileGroup) {
          if (req.body.fileGroup === "signed-files") {
            const fileIndex = landOfficer.signedFiles.uploadFiles.find((uploadFile) =>
              uploadFile.fileId === req.body.fileId);
            if (fileIndex !== -1) {
              landOfficer.signedFiles.uploadFiles.splice(fileIndex, 1);
              landOfficer.markModified('signedFiles');
            }
          }
          else if (req.body.fileGroup === "transfer-files") {
            const fileIndex = landOfficer.transferCompletedFiles.uploadFiles.find((uploadFile) =>
              uploadFile.fileId === req.body.fileId);
            if (fileIndex !== -1) {
              landOfficer.transferCompletedFiles.uploadFiles.splice(fileIndex, 1);
              landOfficer.markModified('transferCompleteFiles');
            }
          }
          else if (req.body.fileGroup === "company-information-files") {
            const fileIndex = landOfficer.companyInformationFiles.uploadFiles.find((uploadFile) =>
              uploadFile.fileId === req.body.fileId);
            if (fileIndex !== -1) {
              landOfficer.companyInformationFiles.uploadFiles.splice(fileIndex, 1);
              landOfficer.markModified('companyInformationFiles');
            }
          }
          else if (req.body.fileGroup === 'stamp-duty-additional-information-files') {
            const fileIndex = landOfficer.stampDutyAdditionalFiles.uploadFiles.find((uploadFile) =>
              uploadFile.fileId === req.body.fileId);
            if (fileIndex !== -1) {
              landOfficer.stampDutyAdditionalFiles.uploadFiles.splice(fileIndex, 1);
              landOfficer.markModified('stampDutyAdditionalFiles');
            }
          }

        } else {
          const filesIdx = landOfficer.files.findIndex((file) => file.id === req.body.fileTypeId);
          if (filesIdx > -1) {
            const fileIndex = landOfficer.files[filesIdx].uploadFiles.find((uploadFile) =>
              uploadFile.fileId === req.body.fileId);
            if (fileIndex !== -1) {
              landOfficer.files[filesIdx].uploadFiles.splice(fileIndex, 1);
              landOfficer.markModified('files');

            }
          }
        }
        landOfficer.save();
      }
    }
    const tempFilesGroup = landOfficerController.getTempUploadFiles(req.session, req.body.fileTypeId);
    if (tempFilesGroup) {
      const fileIndex = tempFilesGroup.find((uploadFile) => uploadFile.fileId === req.body.fileId);
      if (fileIndex !== -1) {
        tempFilesGroup.splice(fileIndex, 1);
        landOfficerController.setTempUploadFiles(req, req.body.fileTypeId, tempFilesGroup);
      }
    }
    return res.json({ result: true });
  } catch (error) {
    console.log(error);
    return res.status(500).end();
  }
};
