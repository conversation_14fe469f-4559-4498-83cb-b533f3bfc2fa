<main class="">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class="card-box">
          <div class="card-title">
            <h2>Decline Notices</h2>
          </div>

          <div class="row mt-3">
            <div class="col-12 ml-1">
              <h4>Search filters:</h4>
            </div>

          </div>
          <div class="row p-1">
            <div class="col-md-12">
              <form method="GET" id="searchForm" action="/decline-notices">
                <div class="row my-2">
                  <div class="col-md-4">
                    <label for="applicationType">Application Type</label>
                    <select name="applicationType" id='applicationType' class="form-control custom-select">
                      <option value="" {{#unless filters.applicationType}}selected{{/unless}}>All</option>
                      <option value="STAMP_DUTY_EXEMPTION_APPLICATION" {{#ifEquals
                        filters.applicationType 'STAMP_DUTY_EXEMPTION_APPLICATION' }}selected{{/ifEquals}}>
                        Stamp Duty Exemption
                      </option>
                      <option value="IMPORT_DUTY_WAIVER" {{#ifEquals filters.applicationType 'IMPORT_DUTY_WAIVER'
                        }}selected{{/ifEquals}}>
                        Import Duty Waiver
                      </option>
                      <option value="REDUCTION_APPLICATION" {{#ifEquals filters.applicationType 'REDUCTION_APPLICATION'
                        }}selected{{/ifEquals}}>
                        Stamp Duty Reduction
                      </option>
                      <option value="EXEMPTIONS" {{#ifEquals filters.applicationType 'EXEMPTIONS'
                        }}selected{{/ifEquals}}>
                        Exemptions
                      </option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label for="status">Status</label>
                    <select name="status" id='statusFilter' class="form-control custom-select">
                      <option value="" {{#unless filters.status}}selected{{/unless}}>All</option>
                      <option value="SENT" {{#ifEquals filters.status 'SENT' }}selected{{/ifEquals}}>Sent</option>
                      <option value="FAILED" {{#ifEquals filters.status 'FAILED' }}selected{{/ifEquals}}>Failed</option>
                    </select>
                  </div>
                  <div class="col-md-4">
                    <label for="name">Name</label>
                    <input class='form-control' type='text' name='name' id='name'
                      placeholder="Enter at least 3 characters" value="{{filters.name}}" />
                    <small class="form-text text-muted">Searches applicant, transferor, and transferee names</small>
                  </div>
                </div>

                <div class="row my-2">
                  <div class="col-md-4">
                    <label for="referenceNumber">Reference Number</label>
                    <input class='form-control' type='text' name='referenceNumber' id='referenceNumber'
                      placeholder="Reference number" value="{{filters.referenceNumber}}" />
                  </div>
                  <div class="col-md-6">
                    <label for="declinedAfter">Declined Date</label>
                    <div class="input-group">
                      <div class="input-group-prepend">
                        <span class="input-group-text" id="date-addon-from">From</span>
                      </div>
                      <input class="form-control input-group-append" type="date" name="declinedAfter" id="declinedAfter"
                        value="{{filters.declinedAfter}}" />
                      <div class="input-group-append">
                        <span class="input-group-text" id="date-addon-to">To</span>
                      </div>
                      <input class="form-control input-group-append" type="date" name="declinedBefore"
                        id="declinedBefore" value="{{filters.declinedBefore}}" />
                    </div>
                  </div>
                  <div class="col-md-2 text-right align-self-end">
                    <button type="submit" class='btn btn-light btn-sm waves-effect'>Search</button>
                    <button type="button" class='btn btn-light btn-sm' id="clearFilters">Clear</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <div class="card-body">
            <div id="automatic-assign-table" class="row">
              <div class="table">
                <table id="scroll-horizontal-datatable" class="table table-striped w-100 nowrap">
                  <thead>
                    <tr>
                      <th class="tableYellow">Id</th>
                      <th class="tableYellow">Date declined</th>
                      <th class="tableYellow">Application type</th>
                      <th class="tableYellow">Reference number</th>
                      <th class="tableYellow">Transferor</th>
                      <th class="tableYellow">Transferee</th>
                      <th class="tableYellow">Status</th>
                      <th class="tableYellow">Parcel #</th>
                      <th class="tableYellow">District</th>
                      <th class="tableYellow">Island</th>
                    </tr>
                  </thead>
                  <tbody>
                    {{#each declineNotices}}
                    <tr class="decline-notice-row">
                      <td>{{ _id }}</td>
                      <td>{{formatDate declineDate "DD/MM/YYYY"}}</td>
                      <td>
                        {{#ifEquals applicationType 'STAMP_DUTY_EXEMPTION_APPLICATION'}}
                        Stamp Duty Exemption
                        {{/ifEquals}}
                        {{#ifEquals applicationType 'IMPORT_DUTY_WAIVER'}}
                        Import Duty Waiver
                        {{/ifEquals}}
                        {{#ifEquals applicationType 'REDUCTION_APPLICATION'}}
                        Stamp Duty Reduction
                        {{/ifEquals}}
                        {{#ifEquals applicationType 'EXEMPTIONS'}}
                        Exemptions
                        {{/ifEquals}}
                      </td>
                      <td>{{ applicationDetails.referenceNumber }}</td>
                      <td>{{ applicationDetails.transferorName }}</td>
                      <td>{{ applicationDetails.transfereeName }}</td>
                      <td>{{ status }}</td>
                      <td>{{ applicationDetails.parcelNumber }}</td>
                      <td>{{ applicationDetails.district }}</td>
                      <td>{{ applicationDetails.island }}</td>
                    </tr>
                    {{/each}}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <!-- CONTENT END -->
          <div class="row mt-2 justify-content-between ">
            <a href="/" class="btn btn-secondary width-lg waves-effect waves-light">
              Back
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
{{>modals/decline-notice-modal}}
{{>modals/resend-notice-modal}}
<script type="text/javascript">
  let table;
  let currentSelectedApplicationId;
  $(document).ready(function () {
    //used for stacking modals
    $(document).on('show.bs.modal', '.modal', function () {
      var zIndex = 1040 + (10 * $('.modal:visible').length);
      $(this).css('z-index', zIndex);
      setTimeout(function () {
        $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
      }, 0);
    });

    table = $("#scroll-horizontal-datatable").DataTable({
      "columnDefs": [{ "visible": false, "targets": [0] }],
      scrollX: !0,
      select: { style: "single" },
      language: {
        paginate: {
          previous: "<i class='mdi mdi-chevron-left'>",
          next: "<i class='mdi mdi-chevron-right'>"
        }
      },
      drawCallback: function () {
        $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
      }
    });
    table.on('select', function (e, dt, type, indexes) {
      if (type === 'row') {
        let selectedRowData = table.rows(indexes).data();
        currentSelectedApplicationId = selectedRowData[0][0];
        if (table.row(indexes[0]).node().className.includes('decline-notice-row')) {
          getDeclineNotice();
        }
      }
    });
  });

  function getDeclineNotice() {
    $.ajax({
      type: "GET",
      url: "/decline-notices/" + currentSelectedApplicationId,
      success: function (data) {
        // Populate modal with decline notice data
        populateDeclineNoticeModal(data);

        // Show the modal
        $('#decline-notice-modal').modal();
        $('#decline-notice-modal').on('shown.bs.modal', function () {
          $('#decline-notice-modal').trigger('focus');
        });
        $('#decline-notice-modal').find('.modal-title').text('Decline Notice Details');
      },
      error: function (error) {
        toastr["error"]('Failed to load decline notice details', 'Error!');
      }
    });
  }

  // Function to populate the decline notice modal with data
  function populateDeclineNoticeModal(data) {
    // Populate general information
    $('#decline-date').text(formatDate(data.declineDate));

    // Format application type for display
    let appType = '';
    switch (data.applicationType) {
      case 'STAMP_DUTY_EXEMPTION_APPLICATION':
        appType = 'Stamp Duty Exemption';
        break;
      case 'IMPORT_DUTY_WAIVER':
        appType = 'Import Duty Waiver';
        break;
      case 'REDUCTION_APPLICATION':
        appType = 'Stamp Duty Reduction';
        break;
      case 'EXEMPTIONS':
        appType = 'Exemptions';
        break;
      default:
        appType = data.applicationType;
    }

    let applicationTypeHtml = `<span>${appType}</span>`;
    if (data.applicationSubType) {
      applicationTypeHtml += `<br><span class="text-muted font-italic">(${data.applicationSubType})</span>`;
    }
    $('#application-type').html(applicationTypeHtml);
    $('#decline-reason').text(data.declineReason);

    // Format status with color
    if (data.status === 'SENT') {
      $('#status-sent').show();
      $('#status-failed').hide();
    } else {
      $('#status-sent').hide();
      $('#status-failed').show();
    }

    $('#email').text(data.email);
    $('#user').text(data.user);

    // Populate application details
    $('#applicant-name').text(data.applicationDetails?.applicantName || 'N/A');
    $('#transferor-name').text(data.applicationDetails?.transferorName || 'N/A');
    $('#transferee-name').text(data.applicationDetails?.transfereeName || 'N/A');
    $('#reference-number').text(data.applicationDetails?.referenceNumber || 'N/A');
    $('#parcel-number').text(data.applicationDetails?.parcelNumber || 'N/A');
    $('#district').text(data.applicationDetails?.district || 'N/A');
    $('#island').text(data.applicationDetails?.island || 'N/A');

    // 30 days to appeal from the date of first successful send attempt
    if (data.sendAttempts && data.sendAttempts.length > 0) {
      const firstSentAttempt = data.sendAttempts.find(attempt => attempt.status === 'SENT');
      if (firstSentAttempt) {
        const daysToAppeal = moment(firstSentAttempt.date).add(30, 'days').diff(moment(), 'days');
        if (daysToAppeal > 0) {
          $('#days-to-appeal-value').text(daysToAppeal + ' days left');
          $('#days-to-appeal-value').addClass('badge-info');
          $('#days-to-appeal-value').removeClass('badge-warning');
        } else {
          $('#days-to-appeal-value').text('Appeal period has ended');
          $('#days-to-appeal-value').addClass('badge-warning');
          $('#days-to-appeal-value').removeClass('badge-info');
        }

        $('#days-to-appeal-row').show();
      }
    }

    // Populate send attempts as bullet list
    const sendAttemptsList = $('#send-attempts-list');
    sendAttemptsList.empty();

    if (data.sendAttempts && data.sendAttempts.length > 0) {
      const sortedAttempts = [...data.sendAttempts].sort((a, b) => {
        return new Date(b.date) - new Date(a.date);
      });

      sortedAttempts.forEach(attempt => {
        const listItem = $('<li>');
        listItem.addClass('mb-2');
        listItem.css('list-style-type', 'disc');

        // Create bullet point with the specified format
        const dateFormatted = formatDateWithTime(attempt.date);

        // Format status with badge
        let statusBadge;
        if (attempt.status === 'SENT') {
          statusBadge = '<span class="badge badge-success">SENT</span>';
        } else {
          statusBadge = '<span class="badge badge-danger">FAILED</span>';
        }

        let bulletText = `${dateFormatted} - ${statusBadge} email attempt to <span class="font-weight-bold">${attempt.email}<span>`;

        if (attempt.errorMessage) {
          bulletText += `<br><span class="text-muted font-italic" style="font-size:0.9em;">Error: ${attempt.errorMessage}</span>`;
        }

        $(listItem).html(bulletText);

        sendAttemptsList.append(listItem);
      });
    } else {
      sendAttemptsList.append('<li class="text-muted">No send attempts found</li>');
    }
  }

  // Format date helper function (date only)
  function formatDate(dateString) {
    if (!dateString) return 'N/A';
    return moment(dateString).format('DD/MM/YYYY');
  }

  // Format date with time helper function (for the specified format)
  function formatDateWithTime(dateString) {
    if (!dateString) return 'N/A';
    return moment(dateString).format('DD/MM/YYYY HH:mm');
  }

  // Handle resend notice button click to open confirmation modal
  $('#resend-notice-btn').on('click', function () {
    // Set the current email in the confirmation text
    const currentEmail = $('#email').text();
    $('#resend-email-address').text(currentEmail);

    // Pre-populate the new email field with the current email
    $('#new-email-input').val(currentEmail);

    // Show the confirmation modal
    $('#resend-notice-confirm-modal').modal('show');
  });

  // Handle clear filters button
  $('#clearFilters').on('click', function () {
    // Clear all form inputs
    $('#searchForm .form-control').val('');

    // Submit the form to reload with no filters
    $('#searchForm').submit();
  });

  // Format date inputs on page load if they contain values
  $(document).ready(function () {
    // If there are date values in the URL, format them for the date inputs
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.has('declinedAfter')) {
      const declinedAfter = moment(urlParams.get('declinedAfter')).format('YYYY-MM-DD');
      if (declinedAfter !== 'Invalid date') {
        $('#declinedAfter').val(declinedAfter);
      }
    }

    if (urlParams.has('declinedBefore')) {
      const declinedBefore = moment(urlParams.get('declinedBefore')).format('YYYY-MM-DD');
      if (declinedBefore !== 'Invalid date') {
        $('#declinedBefore').val(declinedBefore);
      }
    }
  });
</script>