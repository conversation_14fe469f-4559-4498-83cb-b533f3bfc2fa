const express = require('express');
const router = express.Router();

const auditorController = require('../controllers/auditorController');

//PAGES
router.get('/dashboard', ensureAuthenticated, auditorController.getDashboard);
router.post('/dashboard', ensureAuthenticated, auditorController.getDashboard);

router.get('/stamp-duty/:submissionId/view', ensureAuthenticated, auditorController.getSubmissionsView);
router.get('/exemption-application/:submissionId/view', ensureAuthenticated, auditorController.getStampDutyExemptionApplicationView);
router.get('/reduction-application/:submissionId/view', ensureAuthenticated, auditorController.getReductionApplicationView);
router.get('/import-duty-waiver/:submissionId/view', ensureAuthenticated, auditorController.getImportDutyWaiverApplicationView);



function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication) {
        if (req.session.authentication.isAuditor) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;
