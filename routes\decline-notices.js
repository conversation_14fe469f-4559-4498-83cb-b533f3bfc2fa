const express = require('express');
const router = express.Router();
const declineNoticesController = require('../controllers/declineNoticesController');

//PAGES
router.get('/', ensureAuthenticated, declineNoticesController.getDashboard);
router.get('/:id', ensureAuthenticated, declineNoticesController.getDeclineNotice);
router.post('/:id/resend', ensureAuthenticated, declineNoticesController.resendDeclineNotice);

function ensureAuthenticated(req, res, next) {
  if (req.session && req.session.authentication) {
    if (req.session.authentication.isStampDuty) {
      return next();
    } else {
      res.redirect('/not-authorized');
    }
  } else {
    res.redirect('/login');
  }
}

module.exports = router;
