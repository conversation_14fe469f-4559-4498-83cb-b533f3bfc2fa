const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const commentSchema = new mongoose.Schema({
  email: { type: String, required: false },
  commentStatus: { type: String, required: false },
  comment: { type: String, required: false },
  date: { type: Date, required: false },
});

const fileSchema = new mongoose.Schema({
  fileId: { type: String, required: true, default: uuidv4 },
  fieldName: { type: String, required: true },
  originalName: { type: String, required: true },
  encoding: { type: String, required: true },
  mimeType: { type: String, required: true },
  blobName: { type: String, required: true },
  container: { type: String, required: true },
  blob: { type: String, required: true },
  blobType: { type: String, required: true },
  size: { type: String, required: true },
  etag: { type: String, required: true },
  url: { type: String, required: true },
});

// File Type Schema
const fileTypeSchema = new mongoose.Schema({
  id: { type: String, required: true, default: uuidv4 },
  internal: { type: String, required: true },
  external: { type: String, required: true },
  fileGroup: { type: String, required: true },
  present: { type: Boolean, required: true, default: false },
  explanation: { type: String, required: false, max: 100 },
  comments: { type: String, required: false },
  validated: { type: Boolean, required: true, default: false },
  uploadFiles: [{ type: fileSchema, required: true, default: [] }]
});

const personalDetailsSchema = new mongoose.Schema({
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  firm: { type: String, required: false },
  phone: { type: String, required: true },
  email: { type: String, required: true },
});

const applicantSchema = new mongoose.Schema({
  gender: { type: String, required: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  dateOfBirth: { type: Date, required: true },
  address: { type: String, required: true },
  addressOptional: { type: String, required: false },
  island: { type: String, required: true },
  phone: { type: String, required: true },
  email: { type: String, required: true },
  isSpouse: { type: Boolean, required: true },
});

const clientDetailsSchema = new mongoose.Schema({
  referenceNr: { type: String, required: true, unique: true },
  filingYourself: { type: Boolean, required: true },
  applicants: [applicantSchema],
  personalInformation: personalDetailsSchema,
  // Applicant
  gender: { type: String, required: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  dateOfBirth: { type: Date, required: true },
  address: { type: String, required: true },
  addressOptional: { type: String, required: false },
  island: { type: String, required: true },
  phone: { type: String, required: true },
  email: { type: String, required: true },
  maritalStatus: { type: String, required: true },
  documentType: { type: String, required: true },
  statusCardNumber: { type: String, required: false },
  additionalApplicants: { type: Boolean, required: true },
  confirmation: { type: Boolean, required: true },
});

const exemptionSchema = new mongoose.Schema({
  status: { type: String, required: false, },
  createdAt: { type: Date, required: true, default: new Date() },
  completedAt: { type: Date, required: false },
  instrumentNumber: { type: String, required: false },
  transferorName: { type: String, required: false },
  transfereeName: { type: String, required: false },
  parcelNumber: { type: String, required: false },
  parcelTextNumber: { type: String, required: false },
  district: { type: String, required: false },
  island: { type: String, required: false },
  value: { type: String, required: false },
  validatedInformation: { type: Boolean, required: false },
  exemptionType: { type: String, required: false },
  type: { type: String, required: false },
  submittedOfficer: {
    username: { type: String, required: false },
    email: { type: String, required: false },
    comment: { type: String, required: false },
    submittedDate: { type: Date, required: false }
  },
  stampDutyOfficer: {
    username: { type: String, required: false },
    email: { type: String, required: false },
    comment: { type: String, required: false },
    submittedDate: { type: Date, required: false }
  },
  psOfficer: {
    username: { type: String, required: false },
    email: { type: String, required: false },
    comment: { type: String, required: false },
    submittedDate: { type: Date, required: false }
  },
  comments: [commentSchema],
  files: [fileTypeSchema],
  signedFiles: { type: fileTypeSchema, required: true },
  transferCompletedFiles: { type: fileTypeSchema, required: false },
  companyInformationFiles: { type: fileTypeSchema, required: false },
  stampDutyAdditionalFiles: { type: fileTypeSchema, required: false },
  hasInterestConflicts: { type: Boolean, required: false, default: false },
  auditReady: { type: Boolean, required: false, default: false },
  // Client fields
  clientDetails: { type: clientDetailsSchema, required: false },
});


//Export model
module.exports = mongoose.model('stampdutyexemptions', exemptionSchema);
