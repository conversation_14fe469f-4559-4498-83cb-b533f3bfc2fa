<div
        id="downloadFileModal"
        class="modal fade "
        tabindex="-1"
        role="dialog"
        aria-labelledby="downloadFileModal"
        style="display: none; z-index: 2000; border: #1a1e24 2px solid;"
        aria-hidden="true"
>
    <div class="modal-dialog modal-md contour container-fluid">
        <div class="modal-content border rounded border-secondary" style="">
            <div class="modal-header">
                <h4 class="modal-title" id="download-modal-title">
                    Download file <span id="download-modal-file-label" class="font-weight-bold"></span>
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">

                            <div id="downloadFiles" class=" my-2 mx-1 text-left text-muted">
                                No files found
                            </div>
                        </div>
                        <!-- end card-body-->
                        <div class="modal-footer justify-content-end pb-0">
                            <button type="button" class="btn solid royal-blue" data-dismiss="modal">
                                <i class="mdi mdi-send mr-1"></i>Close
                            </button>
                        </div>
                    </div>
                    <!-- end card-->
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->
        </div>
    </div>
</div>
<script type="text/javascript">
    $('#downloadFileModal').on('show.bs.modal', function (event) {
        $("#downloadFiles").html('No files found');
        let button = $(event.relatedTarget); // Button that triggered the modal
        const officerId = button.data('land-officer-id');
        const fileTypeId =  button.data('file-id');
        const submissionFileType = button.data('file-group');
        $.ajax({
            type: 'GET',
            url: '/land-officer/files',
            data: {fileTypeId: fileTypeId, landOfficer: officerId, fileGroup: submissionFileType ? submissionFileType : ''},
            success: (data) => {
                if (data.success === false){
                    toastr["warning"](data.message);
                }
                else{
                    let strFileLinks = "";
                    if (data.files.length > 0) {
                        for (let idx = 0; idx < data.files.length; idx++) {
                            const url = "/land-officer/" + officerId + "/files/" + fileTypeId + "/" + data.files[idx].fileId + "/download";
                            strFileLinks += "<a style='font-size: medium' href='"+ url + "' target='_blank'>" +  '<i class="fa fa-arrow-circle-down" aria-hidden="true"></i>  '+
                                    data.files[idx].originalName + "</a><br> <hr>"

                        }
                        if (strFileLinks){
                            $("#downloadFiles").html(strFileLinks);
                        }
                    }

                }
            },
            error: (err) => {
                Swal.fire('Error', 'There was an error downloading the file', 'error');

                $('#downloadFileModal').modal('hide');
            },
        });
    });

    $('#downloadFileModal').on('hide.bs.modal', function (event) {
        $('.modal').css('overflow-y', 'auto');
        $("#downloadFiles").html('');
    });

</script>
