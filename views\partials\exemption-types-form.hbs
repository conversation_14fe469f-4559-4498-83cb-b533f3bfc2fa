<div class="card">
    <div class="card-body">
        <h4 class="justify-content-md-left" style="color:#f2aa0f; font: 200% sans-serif;">New
            application</h4> <br><br><br>

        {{#if isLandSumission}}
            <!-- INSTRUMENT NUMBER -->
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="instrument-number">Instrument Number:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" name="instrumentNumber" id="instrument-number" class="form-control" required>
                    </div>
                </div>
            </div>
            <!-- TRANSFEROR NAME-->
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transferorName">Transferor:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" name="transferorName" id="transferorName" class="form-control" required>
                    </div>
                </div>
            </div>
            <!-- TRANSFEREE NAME-->
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transfereeName">Transferee:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" name="transfereeName" id="transfereeName" class="form-control" required>
                    </div>
                </div>
            </div>
        {{else}}
            <!-- APPLICANT -->
            <div class="row justify-content-md-center">
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="transferorName">Applicant:</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <input type="text" name="transferorName" id="transferorName" class="form-control" required>
                    </div>
                </div>
            </div>
        {{/if}}

        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="parcel-number">Parcel
                        #:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input name="parcelNumber" id="parcel-number" type="number" maxlength="5" required
                        class="form-control">
                    <span class="input-group-text">/</span>
                    <input name="parcelTextNumber" id="parcel-text-number" type="text" required class="form-control">
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="district">District:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <input type="text" name="district" id="district" class="form-control" required>
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="island" id="island-label">Island
                    </label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-3">
                    <select class="custom-select" id="island" name="island" aria-labelledby="island-label"
                        aria-required="true" required>
                        <option value="">Select</option>
                        <option>Grand Turk</option>
                        <option>Salt Cay</option>
                        <option>Pine Cay</option>
                        <option>North Caicos</option>
                        <option>Middle Caicos</option>
                        <option>South Caicos</option>
                        <option>Ambergris Cay</option>
                        <option>Parrot Cay</option>
                        <option>Providenciales</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="inputValue">Value:</label>
                </div>
            </div>
            <div class="col-md-6">
                <div class="input-group form-group mb-3">
                    <div class="input-group-prepend">
                        <span class="input-group-text" id="basic-addon1">$</span>
                    </div>
                    <input type="text" name="value" id="inputValue" pattern="^[0-9.,]*$"
                           placeholder="0,00" data-type="currency"  class="form-control" required>
                </div>
            </div>
        </div>
        <br>

        <div class="row justify-content-md-center">
            <div class="col-md-4">
                <div class="form-group">
                    <label>Uploaded Files:</label>
                </div>

            </div>
            <div class="col-md-6">
                {{#each files}}
                <div class="row p-1">
                    <div class="col-md-10">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" name="files[{{id}}][present]" class="custom-control-input"
                                id="{{internal}}" form="submitForm" />
                            <label class="custom-control-label" for="{{internal}}">{{external}}</label>
                        </div>
                    </div>

                    <div class="col-md-0">
                        <button type="button" form="submitForm" class="btn solid royal-blue upload-button"
                            data-toggle="modal" data-target="#upload-modal" id="btn-{{internal}}"
                            data-file-type-id="{{id}}" data-row="1" data-field="{{internal}}"
                            data-file-group="{{fileGroup}}" style=" height:35px">
                            Upload
                        </button>
                    </div>
                </div>
                {{/each}}
            </div>
        </div>


        <br>
    </div>
</div>

<script type="text/javascript">

    const parcelInput = document.getElementById('parcel-number');
    parcelInput.addEventListener('input', function () {
        if (this.value.length > 5)
            this.value = this.value.slice(0, 5);
    });

    $("input[data-type='currency']").on({
        keyup: function() {
            formatCurrency($(this));
        },
        blur: function() {
            formatCurrency($(this), "blur");
        }
    });

    $("#parcel-number").on('keydown', function (e) {
        const invalidChars = ["-", "+", "e", ".", ","];
        if (invalidChars.includes(e.key)) {
            e.preventDefault();
        }
    })

</script>
