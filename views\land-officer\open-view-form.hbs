<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-body">
                        <form id="submitForm" method="POST" autocomplete="off">
                            <div>
                                {{>form-review/open-land-officer-form files=landOfficer.files}}
                            </div>
                        </form>
                        <div class="row justify-content-between">
                            <div class="col-2">
                                <a href="/land-officer/dashboard"
                                    class="btn btn-secondary width-lg waves-effect waves-light">
                                    Back
                                </a>
                            </div>
                            {{#ifCond isEditable '==' true}}
                                <div class="col-2">
                                    <button
                                        style="color: rgb(255, 255, 255); background-color: rgb(243, 161, 8); border-color: #f0b906;"
                                        id="saveButton" type="submit" form="submitForm"
                                        class="btn btn-secondary width-lg waves-effect waves-light">
                                        Save
                                    </button>
                                </div>
                                <div class="col-2">
                                    <button
                                        style="color: rgb(255, 255, 255); background-color: rgb(230, 7, 7); border-color: #d10f0f;"
                                        type="button" id="deleteButton" data-status="delete-form" data-toggle="modal"
                                        data-target="#confirmDeleteModal" data-application-id="{{landOfficer._id}}"
                                        class="btn btn-secondary width-lg waves-effect waves-light">
                                        Delete
                                    </button>
                                </div>
                                <div class="col-2">
                                    <div class="col-2">
                                        <div class="col-2">
                                            <button
                                                style="color: rgb(255, 255, 255); background-color: rgb(18, 163, 4); border-color: #0ba713;"
                                                type="button" data-status="submit-application" data-toggle="modal"
                                                data-target="#submitReviewModal" data-id="{{landOfficer._id}}"
                                                class="btn btn-secondary width-lg waves-effect waves-light">
                                                Submit
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            {{/ifCond}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>modals/upload-file-modal}}
{{>modals/submit-application-modal}}
{{>modals/submit-review-application-modal}}
{{>modals/confirm-delete-modal}}
{{>modals/download-file-modal}}
<script type="text/javascript">
    let idValue = "{{landOfficer._id}}";
    const myFormValidate = $("#submitForm")[0];
    $("#saveButton").click(function () {
        $('#submitForm').submit(function (event) {
            const saveBtn = $("#saveButton");
            saveBtn.prop('disabled', true);
            event.preventDefault();

            if (!myFormValidate.checkValidity()) {
                if (myFormValidate.reportValidity) {
                    myFormValidate.reportValidity();
                    return;
                }
            }
            $.ajax({
                url: '/land-officer/' + idValue + '/update',
                type: 'POST',
                timeout: 5000,
                data: $(this).serialize(),
                success: function () {
                    Swal.fire('Success', 'The form has been saved successfully', 'success').then(() => {
                        location.href = '/land-officer/dashboard';
                    });
                },
                error: function () {
                    Swal.fire('Error', 'There was an error saving the form', 'error').then(() => {
                        location.href = '/land-officer/dashboard';
                    });
                },
            });
        });
    });
</script>
