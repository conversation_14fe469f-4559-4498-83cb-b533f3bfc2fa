const express = require('express');
const router = express.Router();
const LandOfficerModel = require('../models/exemptions');
const ExemptionModel = require('../models/exemptions');
const ImportDutyWaiverApplicationModel = require('../models/importDutyWaiverApplication').ImportDutyWaiverApplicationModel;
const StampDutyApplicationModel = require('../models/stampDutyExemptionApplication').StampDutyApplicationModel;
const ReductionApplicationModel = require('../models/stampDutyReductionApplication').ReductionApplicationModel;

/* GET home page. */
router.get('/', ensureAuthenticated, async function (req, res) {
    let submitted;
    let saved;
    let requested;
    let pendingPS;
    let declined;
    let approvedPS;
    let transferPending;
    let transferCompleted;
    let completed;
    let countDocuments;
    let naturalLove;
    let sectionExemption;
    let charitableInstitutions;
    let homeOwnerPolicy;
    let countByType;
    let conflict;

    // Counters for officer dashboards
    let financeOfficerCount = 0;
    let customsOfficerCount = 0;
    let deputyCommissionerCount = 0;
    let psSubmissionsCount = 0;
    let landOfficerCount = 0;

    countDocuments = await LandOfficerModel.aggregate([
        {
            $match: {
                "status": {
                    $in: ['SUBMITTED', 'SAVED', 'REQUESTED', 'PENDING PS', 'DECLINED', 'APPROVED PS', 'TRANSFER PENDING', 'TRANSFER COMPLETED', 'COMPLETED', 'CONFLICT']
                }
            },
        },
        { $group: { _id: "$status", count: { $sum: 1 } } }
    ]);
    if (countDocuments) {
        submitted = countDocuments.find((document) => document._id === "SUBMITTED");
        saved = countDocuments.find((document) => document._id === "SAVED");
        requested = countDocuments.find((document) => document._id === "REQUESTED");
        pendingPS = countDocuments.find((document) => document._id === "PENDING PS");
        declined = countDocuments.find((document) => document._id === "DECLINED");
        approvedPS = countDocuments.find((document) => document._id === "APPROVED PS");
        transferPending = countDocuments.find((document) => document._id === "TRANSFER PENDING");
        transferCompleted = countDocuments.find((document) => document._id === "TRANSFER COMPLETED");
        completed = countDocuments.find((document) => document._id === "COMPLETED");
        conflict = countDocuments.find((document) => document._id === "CONFLICT");
    }

    countByType = await LandOfficerModel.aggregate([
        {
            $match: {
                "exemptionType": {
                    $in: ['Natural Love & Affection', 'Section 23- Companies & 28- Bodies Corporate', 'Transfers to Charitable Institutions', 'Home Owner Policy']
                }
            },
        },
        { $group: { _id: "$exemptionType", count: { $sum: 1 } } }
    ]);
    if (countByType) {
        naturalLove = countByType.find((documentType) => documentType._id === "Natural Love & Affection");
        sectionExemption = countByType.find((documentType) => documentType._id === "Section 23- Companies & 28- Bodies Corporate");
        charitableInstitutions = countByType.find((documentType) => documentType._id === "Transfers to Charitable Institutions");
        homeOwnerPolicy = countByType.find((documentType) => documentType._id === "Home Owner Policy");
    }

    // Get Finance Officer pending submissions count
    try {
        financeOfficerCount = await ExemptionModel.countDocuments({
            $and: [
                { "status": { $in: ['SUBMITTED', 'SAVED', 'REQUESTED', 'TRANSFER PENDING'] } },
                { "type": 'FINANCE' }
            ]
        });
    } catch (e) {
        console.log("Error counting finance officer submissions: ", e);
        financeOfficerCount = 0;
    }

    // Get Customs Officer pending submissions count
    try {
        customsOfficerCount = await ImportDutyWaiverApplicationModel.countDocuments({
            status: { $in: ['NOT STARTED', 'PENDING CUSTOMS', 'SAVED CUSTOMS', 'DECLINED BY DEPUTY COMMISSIONER'] }
        });
    } catch (e) {
        console.log("Error counting customs officer submissions: ", e);
        customsOfficerCount = 0;
    }

    // Get Deputy Commissioner pending submissions count
    try {
        deputyCommissionerCount = await ImportDutyWaiverApplicationModel.countDocuments({
            status: { $in: ['PENDING DEPUTY COMMISSIONER', 'SAVED DEPUTY COMMISSIONER', 'CONFLICTED BY CUSTOMS OFFICER', 'CONFLICTED BY DEPUTY COMMISSIONER'] }
        });
    } catch (e) {
        console.log("Error counting deputy commissioner submissions: ", e);
        deputyCommissionerCount = 0;
    }

    // Get PS Submissions pending actions count
    try {
        // Count ExemptionModel submissions with actionable statuses
        const exemptionActionableCount = await ExemptionModel.countDocuments({
            status: { $in: ['PENDING PS', 'CONFLICTED BY STAMP DUTY OFFICER', 'SAVED PS OFFICER', 'CONFLICT'] }
        });

        // Count StampDutyApplicationModel submissions with actionable statuses
        const stampDutyActionableCount = await StampDutyApplicationModel.countDocuments({
            status: { $in: ['PENDING PS', 'CONFLICTED BY STAMP DUTY OFFICER', 'SAVED PS OFFICER'] }
        });

        // Count ReductionApplicationModel submissions with actionable statuses
        const reductionActionableCount = await ReductionApplicationModel.countDocuments({
            status: { $in: ['PENDING PS', 'CONFLICTED BY STAMP DUTY OFFICER', 'SAVED PS OFFICER'] }
        });

        // Also count assigned submissions (APPROVED PS with psOfficer.email)
        // Note: This would require a more complex query, but for now we'll count the main actionable ones
        psSubmissionsCount = exemptionActionableCount + stampDutyActionableCount + reductionActionableCount;
    } catch (e) {
        console.log("Error counting PS submissions: ", e);
        psSubmissionsCount = 0;
    }

    // Get Land Officer pending actions count
    try {
        landOfficerCount = await ExemptionModel.countDocuments({
            $and: [
                { "status": { $in: ['SAVED', 'REQUESTED'] } },
                { "type": "LAND SUBMISSION" }
            ]
        });
    } catch (e) {
        console.log("Error counting land officer submissions: ", e);
        landOfficerCount = 0;
    }

    res.render('index',
        {
            user: req.session.user,
            authentication: req.session.authentication,
            submitted: submitted ? submitted.count : 0,
            saved: saved ? saved.count : 0,
            requested: requested ? requested.count : 0,
            pendingPS: pendingPS ? pendingPS.count : 0,
            declined: declined ? declined.count : 0,
            approvedPS: approvedPS ? approvedPS.count : 0,
            transferPending: transferPending ? transferPending.count : 0,
            transferCompleted: transferCompleted ? transferCompleted.count : 0,
            completed: completed ? completed.count : 0,
            conflict: conflict ? conflict.count : 0,
            naturalLove: naturalLove ? naturalLove.count : 0,
            sectionExemption: sectionExemption ? sectionExemption.count : 0,
            charitableInstitutions: charitableInstitutions ? charitableInstitutions.count : 0,
            homeOwnerPolicy: homeOwnerPolicy ? homeOwnerPolicy.count : 0,
            // Officer dashboard counters
            financeOfficerCount: financeOfficerCount,
            customsOfficerCount: customsOfficerCount,
            deputyCommissionerCount: deputyCommissionerCount,
            psSubmissionsCount: psSubmissionsCount,
            landOfficerCount: landOfficerCount,
        });
});

function ensureAuthenticated(req, res, next) {
    if (req.session && req.session.authentication && req.session.user) {
        if (req.session.authentication.isStampDuty || req.session.authentication.isLandOfficer ||
            req.session.authentication.isPsOfficer || req.session.authentication.isFinance || req.session.authentication.isAuditor ||
            req.session.authentication.isCustoms || req.session.authentication.isDeputyCommissioner) {
            return next();
        } else {
            res.redirect('/not-authorized');
        }
    } else {
        res.redirect('/login');
    }
}

module.exports = router;
