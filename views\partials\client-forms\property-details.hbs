<h3>Property details:</h3>
<br>
<!-- PARCEL NUMBER -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="parcelFirstPart">Parcel#</label>
            <label for="parcelSecondPart" style="display: none;"></label>
        </div>
    </div>
    <div class="col-md-8">
        <div class=" form-group mb-3 input-group">
            <input name="parcelFirstPart" id="parcelFirstPart" type="text" pattern="[0-9]+" maxlength="5"
                class="form-control" value="{{application.propertyDetails.parcelFirstPart}}" disabled required>
            <div class="input-group-prepend">
                <span class="input-group-text">/</span>
            </div>
            <input name="parcelSecondPart" id="parcelSecondPart" type="text" pattern="[0-9]+" class="form-control"
                value="{{application.propertyDetails.parcelSecondPart}}" disabled required>
        </div>
    </div>
</div>
<!-- DISTRICT -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="district">District</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="district" id="district" class="form-control"
                value="{{application.propertyDetails.district}}" disabled required>
        </div>
    </div>
</div>
<!-- PROPERTY ISLAND -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="propertyIsland">Island</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <select class="custom-select" id="propertyIsland" name="propertyIsland" aria-required="true" disabled
                required>
                {{#each islands}}
                <option {{#ifEquals ../application.propertyDetails.island name}} selected{{/ifEquals}}>{{name}}
                </option>
                {{/each}}
            </select>
        </div>
    </div>
</div>
<!-- VALUE -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="propertyValue">Market value of the
                property</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="input-group mb-3">
            <div class="input-group-prepend">
                <span class="input-group-text" id="money-addon">USD</span>
            </div>
            <input type="number" name="propertyValue" id="propertyValue" class="form-control" min="0"
                value="{{application.propertyDetails.value}}" disabled required>
        </div>
    </div>
</div>
<!-- VALUATION DATE -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-0">
            <label for="valuationDateDayControl">Valuation Date</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="date" name="valuationDate" id="valuationDate" class="form-control"
                value="{{formatDate application.propertyDetails.valuationDate 'YYYY-MM-DD'}}" disabled>
        </div>
    </div>
</div>
<!-- NAME OF NOTARY -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="notaryName">Name of Notary</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="notaryName" id="notaryName" class="form-control"
                value="{{application.propertyDetails.notaryName}}" disabled required>
        </div>
    </div>
</div>
<!-- COUNTRY OF NOTARY -->
<div class="row">
    <div class="col-md-4">
        <div class="form-group mb-3">
            <label class="mb-2" for="notaryCountry">Country of Notary*</label>
        </div>
    </div>
    <div class="col-md-8">
        <div class="form-group mb-3">
            <input type="text" name="notaryCountry" id="notaryCountry" class="form-control"
                value="{{application.propertyDetails.notaryCountry}}" disabled required>
        </div>
    </div>
</div>