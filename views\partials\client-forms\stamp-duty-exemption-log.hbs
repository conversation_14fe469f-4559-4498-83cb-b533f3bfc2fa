<h3>
    Application log
</h3>
{{#if application.stampDutyOfficerTier.comments}}
<label class="mt-1 font-18"><u>Stamp duty officer</u></label>
<ul class="ml-3" style="list-style: disc;">
    {{#each application.stampDutyOfficerTier.comments }}
    <li class="mt-1"><b class="text-warning">{{user}}</b> at {{formatLocalDate date 'DD/MM/YYYY hh:mm a'}}
        has <b>{{status}}</b> the application. <br>
        {{#if internalComments}}
        <u>Internal comments:</u> {{internalComments}}<br>
        {{/if}}
    </li>
    {{/each}}
</ul>
{{/if}}
{{#if application.psOfficerTier.comments}}
<label class="mt-1 font-18"><u>PS officer</u></label>
<ul class="ml-3" style="list-style: disc;">
    {{#each application.psOfficerTier.comments }}
    <li class="mt-1"><b class="text-warning">{{user}}</b> at {{formatLocalDate date 'DD/MM/YYYY hh:mm a'}}
        has <b>{{status}}</b> the application.<br>
        {{#if declineReason}}
        <u>Decline reason:</u> {{declineReason}}<br>
        {{/if}}
        {{#if internalComments}}
        <u>Internal comments:</u> {{internalComments}}<br>
        {{/if}}
    </li>
    {{/each}}
</ul>
{{/if}}
{{#if application.informationRequests}}
<label class="mt-1 font-18"><u>Information requests</u></label>
<ul class="ml-3" style="list-style: disc;">
    {{#each application.informationRequests }}
    <li class="mt-1"><b class="text-warning">{{createdBy}}</b> requested additional information at {{formatLocalDate createdAt 'DD/MM/YYYY hh:mm
        a'}}.<br>
        {{#if managementComments}}
        <u>Comments to applicant:</u> {{managementComments}}<br>
        {{/if}}
        {{#if internalComments}}
        <u>Internal comments:</u> {{internalComments}}<br>
        {{/if}}
        <u>Information requested:</u> {{convertInformationNames requests}}.<br>
        {{#if userComments}}
        <u><b>Applicant comments:</b></u> {{userComments}}<br>
        {{/if}}
        {{#if submittedAt}}
        <b><u>Application re-submitted at:</u></b> {{formatLocalDate submittedAt 'DD/MM/YYYY hh:mm a'}}<br>
        {{/if}}
    </li>
    {{/each}}
</ul>
{{/if}}