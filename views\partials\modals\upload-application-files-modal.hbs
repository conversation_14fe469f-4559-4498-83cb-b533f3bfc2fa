<div id="upload-files-modal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="uploadModal"
    style="display: none;" aria-hidden="true">
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="upload-modal-temp-title">
                    Upload file: <span id="upload-modal-temp-label" class="font-weight-bold"></span>
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-body">
                            <div {{#if isComplete}}style="display: none;" {{/if}}>
                                <p>
                                    Maximum of <span id="maxUploadTemp"></span> File(s), PDF/JPEG/GIF/PNG only. File
                                    must not be
                                    password protected.
                                </p>
                                <div id="uploadModalTempForm" class="dropzone">
                                    <div class="fallback">
                                        <input name="fileUploaded" type="file" multiple />
                                    </div>
                                    <div class="dz-message needsclick">
                                        <i class="h1 text-muted dripicons-cloud-upload"></i>
                                        <h3>Drop files here or click to upload.</h3>
                                        <span class="text-muted">Files will be automatically uploaded</span>
                                    </div>
                                </div>
                            </div>
                            <div id="uploadedTempFiles" class="mt-2 text-center text-muted"></div>
                        </div>
                        <!-- end card-body-->
                        <div class="modal-footer justify-content-end pb-0">
                            <button type="button" class="btn solid royal-blue" data-dismiss="modal">
                                <i class="mdi mdi-send mr-1"></i>Close
                            </button>
                        </div>
                    </div>
                    <!-- end card-->
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->
        </div>
    </div>
</div>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/uploadedapplicationfiles.precompiled.js"></script>

<script type="text/javascript">
    Dropzone.autoDiscover = false;

    let rowindex;
    let button = '';
    let fileId = '';
    let fileGroup = '';
    let updatingFiles;

    let maxFiles;
    let minFiles;

    $(function () {
        let field = '';
        let name = '';
        let tempDropzone = new Dropzone('#uploadModalTempForm', {
            url: '/',
            acceptedFiles: 'application/pdf,image/jpeg,image/png,image/gif',
            autoProcessQueue: true,
            parallelUploads: 3,
            maxFiles: maxFiles,
            maxFilesize: 10,
            paramName: function () {
                return 'fileUploaded';
            },
            uploadMultiple: true,
            init: function () {
                this.on('processing', function () {
                    this.options.url = '/stamp-duty/{{stampDutyType}}-application/{{application._id}}/upload-documents/' + fileGroup;
                });
                this.on('success', function () {
                    refreshUploadedFiles(fileGroup);
                    if (updatingFiles) {
                        $(".updating-files").prop('disabled', false);
                    }
                });
                this.on('sending', function (file, xhr, formData) {
                    if (!formData.has('fileName')) {
                        formData.append('fileName', field);
                    }
                    if (!formData.has('fileGroup')) {
                        formData.append('fileGroup', fileGroup);
                    }
                });

                this.on('errormultiple', function (files, response) {
                });

                this.on('maxfilesexceeded', function (file) {
                });

                this.on('resetFiles', function () {
                    if (this.files.length !== 0) {
                        for (i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                            this.files = [];
                        }
                    }
                    $('#maxUploadTemp').text(this.options.maxFiles);
                });
            },
        });

        $('#upload-files-modal').on('show.bs.modal', function (event) {
            button = $(event.relatedTarget); // Button that triggered the modal
            name = button.data('field'); //name of the file
            field = name.replace(/[\s\’\'\/\(\)]/g, ''); //formatted name with no special chars
            fileGroup = button.data('group');
            maxFiles = Number(button.data('maxfiles'));
            minFiles = Number(button.data('minfiles'));
            updatingFiles = button.data('updating-files');
            $('#maxUploadTemp').text(maxFiles);
            $('#upload-modal-temp-label').text(name);
            $.get('/stamp-duty/{{stampDutyType}}-application/{{application._id}}/uploaded-files',
                { fileGroup: fileGroup },
                function (data) {
                    console.log('{{isComplete}}')
                    let template = Handlebars.templates.uploadedapplicationfiles;
                    let d = {
                        files: data.files ? data.files : [],
                        hideDelete: '{{isComplete}}' == 'true',
                        group: fileGroup,
                        baseUrl: '/stamp-duty/{{stampDutyType}}-application/{{application._id}}'
                    };
                    let html = template(d);

                    $('#uploadedTempFiles').html(html);
                }
            );
            var modal = $(this);
            const objDZ = Dropzone.forElement('#uploadModalTempForm');
            objDZ.emit('resetFiles');
        });

        $('#upload-files-modal').on('hide.bs.modal', function (event) {
            var objDZ = Dropzone.forElement('#uploadModalTempForm');
            objDZ.removeAllFiles(true);
            $('#uploadedTempFiles').html('');
        });
    });

    function downloadFile(fileGroup, fileId) {
        let url = "/stamp-duty/{{stampDutyType}}-application/{{application._id}}/download-document/" + fileGroup + "/" + fileId;

        window.open(url, "_blank");
        return false;
    }

    function deleteFile(fileId, fileGroup, name) {
        $.ajax({
            type: 'DELETE',
            url: '/stamp-duty/{{stampDutyType}}-application/{{application._id}}/delete-document',
            data: {
                group: fileGroup,
                fileId: fileId,
            },
            success: function (res) {
                if (res.status === 200) {
                    refreshUploadedFiles(fileGroup);
                    const objDZ = Dropzone.forElement('#uploadModalTempForm');
                    const file = objDZ.files.find((file) => file.name === name);
                    if (file) {
                        file.previewElement.remove();
                        objDZ.files = objDZ.files.filter((fl) => fl.upload.uuid !== file.upload.uuid);
                    }

                }
            },
            dataType: 'json',
        });
        return false;
    }

    function refreshUploadedFiles(fileGroup) {
        $.get('/stamp-duty/{{stampDutyType}}-application/{{application._id}}/uploaded-files', { fileGroup: fileGroup },
            function (data) {
                let template = Handlebars.templates.uploadedapplicationfiles;
                let d = {
                    files: data.files ? data.files : [],
                    group: fileGroup,
                    hideDelete: '{{isComplete}}' === 'true',
                    baseUrl: '/stamp-duty/{{stampDutyType}}-application/{{application._id}}'
                };
                let html = template(d);
                remissionCount = data.files.length;
                $('#uploadedTempFiles').html(html);
                if (data.files.length < minFiles) {
                    button.css({
                        'background-color': '#f7b84b',
                        'border-color': '#f7b84b',
                    });
                    button.html('Upload');
                    if ('{{isComplete}}' === 'true') {
                        button.prop('disabled', true);
                    }
                }
                else {
                    button.css({
                        'background-color': '#159a80',
                        'border-color': '#148f77',
                    });
                    button.html('Uploaded documents');
                }

            }
        );
    }
</script>