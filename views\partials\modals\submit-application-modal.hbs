{{!-- CONFIRM MODAL --}}
<div class="modal fade" id="submitApplicationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" style="text-transform: capitalize;">Confirmation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3 text-justify">
                <div id="message_modal">

                </div>
                <div id="commentTextarea" class="mt-1 text-justify">
                    <label for="modalInputComment"><small></small></label> <br>
                    <textarea class="form-control" name="modalInputComment" id="modalInputComment"
                        placeholder="Add comment..." rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Cancel
                </button>
                <button type="button" class="btn solid royal-blue" id="sendButton">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    const modalInfo = {
        "submit-application": {
            "modalMessage": 'Are you sure you want to submit?',
            "successMessage": 'The form has been submitted successfully',
            "errorMessage": 'There was an error submitting the form'
        },
        "save-application": {
            "modalMessage": 'Are you sure you want to save and continue later?',
            "successMessage": 'The form has been saved successfully',
            "errorMessage": 'There was an error saving the form'
        }
    };
    const myForm = $("#submitForm")[0];
    let status = '';
    let comment = '';
    //let typeSelected = $('#relationType option:selected').val();
    $('#submitApplicationModal').on('show.bs.modal', function (event) {
        let button = $(event.relatedTarget); // Button that triggered the modal
        status = button.data('status');
        reviewId = button.data('id');
        $('#message_modal').html(modalInfo[status].modalMessage);
        if (status === "save-application") {
            $('#commentTextarea').hide();
        } else {
            $('#commentTextarea').show();
        }

    });
    $('#sendButton').on('click', function () {
        comment = $('#modalInputComment').val();
        if (!myForm.checkValidity()) {
            if (myForm.reportValidity) {
                myForm.reportValidity();
                return;
            }
        }
        $.ajax({
            url: '/land-officer/create',
            type: 'POST',
            data: $("#submitForm").serialize() + '&comment=' + comment + '&status=' + status,
            timeout: 3000,
            success: function () {
                Swal.fire('Success', 'The form has been submitted successfully', 'success').then(() => {
                    location.href = '/land-officer/dashboard';
                });
            },
            error: function () {
                Swal.fire('Error', 'There was an error submitting the form', 'error').then(() => {
                    location.href = '/land-officer/dashboard';
                });
            },
        });
    });

</script>
