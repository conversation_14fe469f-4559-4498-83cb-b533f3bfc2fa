@charset "UTF-8";
/* #Imports
================================================== */
/* #Typography Variables
================================================== */
/* #Colour Variables
================================================== */
/* #Width Variables
================================================== */
/* 1232 */
/* 1025 */
/* 767 */
/* 
* Set a rem font size with pixel fallback
*   -Usage: @include font-size(14px)
*/
/* 
* Breakpoints
*   -Usage: @include bp-small {
*       -css  
*     }
*/
/* 
* SVG background images with PNG and retina fallback
*   -Usage: @include background-image('img_name');
*/
/* 
* Animations and keyframes
*   -Usage: 
* @include keyframes(slide-down) {
*   0% { opacity: 1; }
*   90% { opacity: 0; }
* }
* .element {
*   Animation Name / Duration / Repeating times (leave blank for once)
*   @include animation('slide-down 5s 3');
* }
*/
/* 
* Transitions
*   -Usage: 
* .element {
*   @include transition(color .3s ease);
* }
*/
/* 
* Transforms
*/
/* 
* Rotate mixin
*   -Usage: @include rotate(25);
*/
/* 
* Cross browser opacity (IE5)
*   -Usage:  @include opacity(0.8);
*/
/* 
* Cross browser inline-block (IE6)
*   -Usage:  @include inline-block;
*/
/* 
* Cross browser border radius
*   -Usage:  @include border-radius(10px);
*/
/* 
* Cross browser box-shadow
*   -Usage:  @include box-shadow(0, 1px, 2px, rgba(0, 0, 0, 0.2));
*/
/* 
* Cross gradient
*   -Usage:  @include gradient(#000000, #dddddd);
*/
/* 
* Col Builder
*   -Usage:  @include col(1, 3, 1em, top, $first: true)
*/
/* 
* centerer
*   -Usage:  @include centerer(true, false);
*/
/* 
* Icomoon
*   -Usage:  @include icomoon;
*/
/* 
* Aspect Ratio
*   -Usage:  @include aspect-ratio(16, 9);
*/
/*! normalize.css v1.0.0 | MIT License | git.io/normalize */
/* =============================================================================
   HTML5 display definitions
   ========================================================================== */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block; }

audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  *zoom: 1; }

audio:not([controls]) {
  display: none; }

[hidden] {
  display: none; }

/* =============================================================================
   Base
   ========================================================================== */
/*
 * 1. Correct text resizing oddly in IE6/7 when body font-size is set using em units
 * 2. Force vertical scrollbar in non-IE
 * 3. Prevent iOS text size adjust on device orientation change, without disabling user zoom: h5bp.com/g
 */
*, *:before, *:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -o-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box; }


html {
  font-size: 100%;
  overflow-y: scroll;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%; }

body {
  margin: 0;
  padding: 0; }

input[type=button],
input[type=submit],
input[type=text],
input[type=search],
input[type=password],
select,
button,
textarea {
  margin: 0;
  cursor: pointer;
  outline: none;
  vertical-align: top; }

input[type=submit] {
  border: none 0;
  -webkit-appearance: none;
  -webkit-border-radius: 0px; }

input[type=radio],
input[type=checkbox] {
  margin: 0;
  padding: 0; }

input[type=text],
input[type=search],
select,
textarea {
  cursor: default; }

button {
  border: none 0; }

*:first-child + html input.button {
  overflow: visible;
  /* remove padding from left/right */
  width: auto !important; }

/*
 * Remove text-shadow in selection highlight: h5bp.com/i
 * These selection declarations have to be separate
 * Also: hot pink! (or customize the background color to match your design)
 */
::-moz-selection {
  color: #fff;
  background-color: #328efd; }
::selection {
  color: #fff;
  background-color: #328efd; }

::-moz-selection {
  color: #fff;
  background-color: #328efd; }

a {
  -webkit-tap-highlight-color: transparent; }

a:hover, a:focus, a:active {
  text-decoration: none; }

a:hover, a:active {
  outline: 0;
  text-decoration: none; }

/* =============================================================================
   Typography
   ========================================================================== */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  padding: 0; }

blockquote {
  margin: 1em 40px; }

hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ccc;
  margin: 1em 0;
  padding: 0; }

/* =============================================================================
   Lists
   ========================================================================== */
ul, ol {
  margin: 0;
  padding: 0; }

ul {
  list-style: none; }

dd {
  margin: 0 0 0 40px; }

nav ul,
nav ol {
  list-style: none;
  margin: 0;
  padding: 0; }

/* =============================================================================
   Embedded content
   ========================================================================== */
/*
 * Improve image quality when scaled in IE7
 * code.flickr.com/blog/2008/11/12/on-ui-quality-the-little-things-client-side-image-resizing/
 */
img {
  border: 0;
  -ms-interpolation-mode: bicubic; }

/*
 * Correct overflow displayed oddly in IE9
 */
svg:not(:root) {
  overflow: hidden; }

/* =============================================================================
   Figures
   ========================================================================== */
figure {
  margin: 0; }

/* =============================================================================
   Forms
   ========================================================================== */
form {
  margin: 0; }

fieldset {
  border: 0;
  margin: 0;
  padding: 0; }

/* Indicate that 'label' will shift focus to the associated form element */
label {
  cursor: pointer; }

/*
 * 1. Correct color not inheriting in IE6/7/8/9
 * 2. Correct alignment displayed oddly in IE6/7
 */
legend {
  border: 0;
  *margin-left: -7px;
  padding: 0; }

/*
 * 1. Correct font-size not inheriting in all browsers
 * 2. Remove margins in FF3/4 S5 Chrome
 * 3. Define consistent vertical alignment display in all browsers
 */
button, input, select, textarea {
  font-size: 100%;
  margin: 0;
  vertical-align: baseline;
  *vertical-align: middle; }

/* Select styling in Firefox */
@-moz-document url-prefix() {
  select {
    padding: 0.8em 0.75em; } }

/*
 * 1. Define line-height as normal to match FF3/4 (set using !important in the UA stylesheet)
 */
button,
input {
  line-height: normal; }

*:first-child + html input.button {
  overflow: visible;
  /* remove padding from left/right */
  width: auto !important; }

/*
 * 1. Display hand cursor for clickable form elements
 * 2. Allow styling of clickable form elements in iOS
 * 3. Correct inner spacing displayed oddly in IE7 (doesn't effect IE6)
 */
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  cursor: pointer;
  -webkit-appearance: button;
  *overflow: visible; }

input[type=submit] {
  -webkit-appearance: none;
  -webkit-border-radius: 0px; }

/*
 * Consistent box sizing and appearance
 */
input[type="checkbox"],
input[type="radio"] {
  margin: 0;
  padding: 0; }

input[type="search"] {
  -webkit-appearance: textfield;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box; }

input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none; }

/*
 * Remove inner padding and border in FF3/4: h5bp.com/l
 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0; }

/*
 * 1. Remove default vertical scrollbar in IE6/7/8/9
 * 2. Allow only vertical resizing
 */
textarea {
  overflow: auto;
  vertical-align: top;
  resize: vertical; }

/* Colors for form validity */
/* =============================================================================
   Tables
   ========================================================================== */
table {
  border-collapse: collapse;
  border-spacing: 0; }

/* =============================================================================
   Non-semantic helper classes
   Please define your styles before this section.
   ========================================================================== */
/* Hide from both screenreaders and browsers: h5bp.com/u */
.hidden {
  display: none !important;
  visibility: hidden; }

/* Hide only visually, but have it available for screenreaders: h5bp.com/v */
.visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px; }

/* Extends the .visuallyhidden class to allow the element to be focusable when navigated to via the keyboard: h5bp.com/p */
.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto; }

/* Hide visually and from screenreaders, but maintain layout */
.invisible {
  visibility: hidden; }

/* Contain floats: h5bp.com/q */
.clearfix:before,
.clearfix:after {
  content: "";
  display: table; }

.clearfix:after {
  clear: both; }

.clearfix {
  *zoom: 1; }

/* This stylesheet generated by Transfonter (http://transfonter.org) on September 22, 2016 10:48 AM */
/* DO NOT USE THESE FONTS COMMERCIALLY. THEY REQUIRE A LICENCE! WE ARE JUST USING THESE FOR DEVELOPMENT PURPOSES */
@font-face {
  font-family: 'Galaxie Polaris Med';
  src: url("./fonts/GalaxiePolaris-Medium.eot");
  src: url("./fonts/GalaxiePolaris-Medium.eot?#iefix") format("embedded-opentype"), url("./fonts/GalaxiePolaris-Medium.woff2") format("woff2"), url("./fonts/GalaxiePolaris-Medium.woff") format("woff"), url("./fonts/GalaxiePolaris-Medium.ttf") format("truetype"), url("./fonts/GalaxiePolaris-Medium.svg#GalaxiePolaris-Medium") format("svg");
  font-weight: 500;
  font-style: normal; }

@font-face {
  font-family: 'Galaxie Polaris Bold';
  src: url("./fonts/GalaxiePolaris-Bold.eot");
  src: url("./fonts/GalaxiePolaris-Bold.eot?#iefix") format("embedded-opentype"), url("./fonts/GalaxiePolaris-Bold.woff2") format("woff2"), url("./fonts/GalaxiePolaris-Bold.woff") format("woff"), url("./fonts/GalaxiePolaris-Bold.ttf") format("truetype"), url("./fonts/GalaxiePolaris-Bold.svg#GalaxiePolaris-Bold") format("svg");
  font-weight: bold;
  font-style: normal; }

@font-face {
  font-family: 'Galaxie Polaris Book';
  src: url("./fonts/GalaxiePolaris-Book.eot");
  src: url("./fonts/GalaxiePolaris-Book.eot?#iefix") format("embedded-opentype"), url("./fonts/GalaxiePolaris-Book.woff2") format("woff2"), url("./fonts/GalaxiePolaris-Book.woff") format("woff"), url("./fonts/GalaxiePolaris-Book.ttf") format("truetype"), url("./fonts/GalaxiePolaris-Book.svg#GalaxiePolaris-Book") format("svg");
  font-weight: normal;
  font-style: normal; }

h1, .h1, div.letterhead div.col a.mega-link, div.hero-banner div.container div.flexslider ul.slides li figure figcaption h2, h2, .h2, h3, .h3, ul.page-links.intranet-links span, .login-form form legend, .reset-password-form section form h2, h4, .h4, #Header2, #Header3 {
  font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  line-height: 1.65;
  font-weight: normal; }

h1, .h1, div.letterhead div.col a.mega-link, div.hero-banner div.container div.flexslider ul.slides li figure figcaption h2, h2, .h2, #Header2 {
  font-size: 22px;
  font-size: 1.375rem;
  color: #f2aa0f; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    h1, .h1, div.letterhead div.col a.mega-link, div.hero-banner div.container div.flexslider ul.slides li figure figcaption h2, h2, .h2, #Header2 {
      font-size: 28px;
      font-size: 1.75rem; } }
  @media only screen and (min-width: 64.063em) {
    h1, .h1, div.letterhead div.col a.mega-link, div.hero-banner div.container div.flexslider ul.slides li figure figcaption h2, h2, .h2, #Header2 {
      font-size: 50px;
      font-size: 3.125rem; } }

h3, .h3, ul.page-links.intranet-links span, .login-form form legend, .reset-password-form section form h2, #Header3 {
  font-size: 20px;
  font-size: 1.25rem; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    h3, .h3, ul.page-links.intranet-links span, .login-form form legend, .reset-password-form section form h2, #Header3 {
      font-size: 22px;
      font-size: 1.375rem; } }
  @media only screen and (min-width: 64.063em) {
    h3, .h3, ul.page-links.intranet-links span, .login-form form legend, .reset-password-form section form h2, #Header3 {
      font-size: 24px;
      font-size: 1.5rem; } }

h4, .h4, #Header4 {
  font-size: 18px;
  font-size: 1.125rem; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    h4, .h4, #Header4 {
      font-size: 20px;
      font-size: 1.25rem; } }
  @media only screen and (min-width: 64.063em) {
    h4, .h4, #Header4 {
      font-size: 22px;
      font-size: 1.375rem; } }

#Header2, #Header3, #Header4 {
  display: block; }

#Header4 {
  font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif; }

strong, b {
  font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif; }

p {
  font-size: 14px;
  font-size: 0.875rem;
  line-height: 1.45; }
  @media only screen and (min-width: 64.063em) {
    p {
      font-size: 18px;
      font-size: 1.125rem; } }
  p.intro {
    margin: 0;
    font-size: 16px;
    font-size: 1rem; }
    @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
      p.intro {
        font-size: 18px;
        font-size: 1.125rem; } }
    @media only screen and (min-width: 64.063em) {
      p.intro {
        font-size: 24px;
        font-size: 1.5rem; } }

a {
  text-decoration: none;
  color: #0081B4;
  -webkit-transition: color 0.25s linear;
  -moz-transition: color 0.25s linear;
  -ms-transition: color 0.25s linear;
  -o-transition: color 0.25s linear;
  transition: color 0.25s linear; }
  a:hover {
    text-decoration: underline;
    color: #005c81; }

#Blockquote {
  display: block;
  font-style: italic;
  padding: 12px 24px; }
  #Blockquote:before {
    content: open-quote; }
  #Blockquote:after {
    content: close-quote; }

@font-face {
  font-family: 'icomoon';
  src: url("./fonts/icomoon.eot?9pebtl");
  src: url("./fonts/icomoon.eot?9pebtl#iefix") format("embedded-opentype"), url("./fonts/icomoon.ttf?9pebtl") format("truetype"), url("./fonts/icomoon.woff?9pebtl") format("woff"), url("./fonts/icomoon.svg?9pebtl#icomoon") format("svg");
  font-weight: normal;
  font-style: normal; }

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.icon-menu:before {
  content: "\e908"; }

.icon-cross:before {
  content: "\e900"; }

.icon-down-arr:before {
  content: "\e901"; }

.icon-left-arr:before {
  content: "\e902"; }

.icon-linked-in:before {
  content: "\e903"; }

.icon-right-arr:before {
  content: "\e904"; }

.icon-search:before {
  content: "\e905"; }

.icon-twitter:before {
  content: "\e906"; }

.icon-up-arr:before {
  content: "\e907"; }

.icon-darr:before {
  content: "\e909"; }

/* =============================================================================
   Grid
   ========================================================================== */
body {
  font-size: 95%;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  line-height: 1.35;
  font-weight: normal;
  font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #000; }

.container {
  width: 100%;
  padding: 0 3.5%; }
  @media only screen and (min-width: 64.063em) {
    .container {
      padding: 0 24px;
      margin: 0 auto;
      max-width: 78.5em; } }
  .container:before, .container:after {
    content: "";
    display: table; }
  .container:after {
    clear: both; }

@media only screen and (min-width: 47.938em) {
  .col.span-1-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 16.66667%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-2-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 33.33333%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-3-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 50%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-4-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 66.66667%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-5-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 83.33333%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-6-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 100%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-1-8 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 12.5%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-2-8 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 25%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-3-8 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 37.5%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-4-8 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 50%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-5-8 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 62.5%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-6-8 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 75%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-7-8 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 87.5%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .col.span-8-8 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 0;
    width: 100%;
    vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .row.gutter .col.span-1-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 3%;
    width: 14.16517%;
    vertical-align: top; }
    .row.gutter .col.span-1-6:nth-child(6n+1) {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      margin-left: 0;
      width: 14.16517%;
      vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .row.gutter .col.span-2-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 3%;
    width: 31.33034%;
    vertical-align: top; }
    .row.gutter .col.span-2-6:nth-child(3n+1) {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      margin-left: 0;
      width: 31.33034%;
      vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .row.gutter .col.span-3-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 3%;
    width: 48.49552%;
    vertical-align: top; }
    .row.gutter .col.span-3-6:nth-child(2n+1) {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      margin-left: 0;
      width: 48.49552%;
      vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .row.gutter .col.span-4-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 2%;
    width: 65.66069%;
    vertical-align: top; }
    .row.gutter .col.span-4-6:nth-child(1.5n+1) {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      margin-left: 0;
      width: 65.66069%;
      vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .row.gutter .col.span-5-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 3%;
    width: 82.82586%;
    vertical-align: top; }
    .row.gutter .col.span-5-6:nth-child(1.2n+1) {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      margin-left: 0;
      width: 82.82586%;
      vertical-align: top; } }

@media only screen and (min-width: 47.938em) {
  .row.gutter .col.span-6-6 {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin-left: 3%;
    width: 99.99103%;
    vertical-align: top; }
    .row.gutter .col.span-6-6:nth-child(1n+1) {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      margin-left: 0;
      width: 99.99103%;
      vertical-align: top; } }

div.main-logo figure {
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
  width: 214px;
  height: 24px; }
  @media only screen and (min-width: 64.063em) {
    div.main-logo figure {
      width: 428px;
      height: 50px; } }
  div.main-logo figure img {
    width: 100%;
    display: block; }

input, textarea, select {
  font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #000;
  line-height: 1;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  background: transparent;
  -webkit-box-shadow: 0 0 0 transparent;
  -moz-box-shadow: 0 0 0 transparent;
  box-shadow: 0 0 0 transparent;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  border-radius: 0px;
  font-size: 14px;
  font-size: 0.875rem; }
  input:focus, textarea:focus, select:focus {
    outline: 0;
     }

div.select-styled {
  position: relative;
  width: 100%;
  background-color: #dadada;
  overflow: visible; }
  div.select-styled:hover select {
    cursor: pointer; }
  div.select-styled:hover:after {
    background: #005c81; }
  div.select-styled:after {
    content: "\e909";
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 8px;
    line-height: 40px;
    color: #fff;
    width: 36px;
    height: 36px;
    position: absolute;
    pointer-events: none;
    right: 0;
    top: 0;
    background: #0081B4;
    text-align: center; }
    @media only screen and (min-width: 64.063em) {
      div.select-styled:after {
        width: 48px;
        height: 48px;
        font-size: 10px;
        line-height: 50px; } }
  div.select-styled select {
    width: 100%;
    height: 36px;
    padding: 0 12px;
    display: block;
    line-height: 1.6;
    color: #0081B4;
    font-size: 14px;
    font-size: 0.875rem; }
    @media only screen and (min-width: 64.063em) {
      div.select-styled select {
        height: 48px;
        font-size: 16px;
        font-size: 1rem; } }

.btn {
  text-align: center;
  padding: 8px 16px; }
  .btn.solid.royal-blue, .reset-password-form section form div.controls button.btn {
    color: #fff;
    background: #0081B4;
    border: 1px solid #0081B4; }
    .btn.solid.royal-blue:hover, .reset-password-form section form div.controls button.btn:hover, .btn.solid.royal-blue:active, .reset-password-form section form div.controls button.btn:active {
      background: #005c81;
      border: 1px solid #005c81; }
  .btn:hover {
    text-decoration: none; }

figure img {
  width: 100%; }

ol.flex-control-paging li {
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
  margin: 0 5px 0 0; }
  ol.flex-control-paging li a {
    display: block;
    text-indent: -9999px;
    width: 12px;
    height: 12px;
    position: relative; }
    ol.flex-control-paging li a:after {
      position: absolute;
      top: 0;
      left: 0;
      content: '';
      display: block;
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 12px 12px 0 0;
      border-color: #b2b2b2 transparent transparent transparent; }
    ol.flex-control-paging li a.flex-active:after {
      border-color: #0081B4 transparent transparent transparent; }

@media only screen and (max-width: 47.938em) {
  div.letterhead {
    display: -webkit-box;
    /* OLD - iOS 6-, Safari 3.1-6, BB7 */
    display: -ms-flexbox;
    /* TWEENER - IE 10 */
    display: -webkit-flex;
    /* NEW - Safari 6.1+. iOS 7.1+, BB10 */
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap; } }

div.letterhead div.col {
  border-top: 1px solid #616161; }
  @media only screen and (max-width: 47.938em) {
    div.letterhead div.col.span-2-6 {
      -webkit-box-ordinal-group: 3;
      -ms-flex-order: 2;
      order: 2;
      -webkit-box-flex: 1;
      -ms-flex: 1 100%;
      flex: 1 100%; }
    div.letterhead div.col.span-4-6 {
      -webkit-box-ordinal-group: 2;
      -ms-flex-order: 1;
      order: 1;
      -webkit-box-flex: 1;
      -ms-flex: 1 100%;
      flex: 1 100%; } }
  div.letterhead div.col h1, div.letterhead div.col h2, div.letterhead div.col h3, div.letterhead div.col p.intro, div.letterhead div.col a.mega-link {
    padding: 24px 0; }
  div.letterhead div.col h1 {
    padding: 24px 0 48px; }
  div.letterhead div.col h2 {
    padding-top: 0;
    font-size: 18px;
    font-size: 1.125rem; }
    div.letterhead div.col h2.eletric {
      color: #00269A; }
    @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
      div.letterhead div.col h2 {
        font-size: 20px;
        font-size: 1.25rem; } }
    @media only screen and (min-width: 64.063em) {
      div.letterhead div.col h2 {
        font-size: 36px;
        font-size: 2.25rem; } }
  div.letterhead div.col h3 {
    font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    color: #0081B4; }
  div.letterhead div.col p.intro {
    margin: 0; }
  div.letterhead div.col a.mega-link {
    display: block;
    position: relative;
    -webkit-transition: left, 0.35s, linear;
    -moz-transition: left, 0.35s, linear;
    -ms-transition: left, 0.35s, linear;
    -o-transition: left, 0.35s, linear;
    transition: left, 0.35s, linear;
    left: 0; }
    div.letterhead div.col a.mega-link:hover {
      color: #005c81;
      left: 5px; }
  div.letterhead div.col figure {
    margin: 24px 0; }
    div.letterhead div.col figure img {
      display: block; }
  div.letterhead div.col img {
    max-width: 100%; }
  div.letterhead div.col ul {
    padding: 0 0 24px; }
    div.letterhead div.col ul.page-links li {
      line-height: 1.5; }
      @media only screen and (min-width: 64.063em) {
        div.letterhead div.col ul.page-links li {
          font-size: 24px;
          font-size: 1.5rem; } }
      div.letterhead div.col ul.page-links li a {
        color: #000; }
        div.letterhead div.col ul.page-links li a.active, div.letterhead div.col ul.page-links li a:hover {
          color: #0081B4; }
    div.letterhead div.col ul.feed-links {
      margin: 14px 0;
      border-top: 1px solid #616161; }
      @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
        div.letterhead div.col ul.feed-links {
          margin: 28px 0; } }
      @media only screen and (min-width: 64.063em) {
        div.letterhead div.col ul.feed-links {
          margin: 44px 0 0 0;
          padding: 32px 0 0 0; } }
      div.letterhead div.col ul.feed-links li h3 {
        padding: 0 0 10px 0;
        color: #0081B4; }
        @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
          div.letterhead div.col ul.feed-links li h3 {
            font-size: 18px;
            font-size: 1.125rem; } }
        @media only screen and (min-width: 64.063em) {
          div.letterhead div.col ul.feed-links li h3 {
            padding: 0 0 24px 0; } }
      div.letterhead div.col ul.feed-links li a {
        color: #000;
        display: block;
        line-height: 1.5;
        font-size: 14px;
        font-size: 0.875rem; }
        @media only screen and (min-width: 64.063em) {
          div.letterhead div.col ul.feed-links li a {
            padding: 0 0 5px 0; } }
        div.letterhead div.col ul.feed-links li a:after {
          content: " >";
          display: -moz-inline-stack;
          display: inline-block;
          zoom: 1;
          *display: inline;
          margin: 0 0 0 5px; }
  div.letterhead div.col article p {
    line-height: 1.5; }
    div.letterhead div.col article p.intro {
      padding-top: 0; }

ul.page-links {
  padding: 0 0 24px; }
  ul.page-links.intranet-links li {
    margin: 0 0 24px 0; }
  ul.page-links.intranet-links span {
    color: #00269A;
    display: block; }
  ul.page-links li {
    line-height: 1.5; }
    @media only screen and (min-width: 64.063em) {
      ul.page-links li {
        font-size: 24px;
        font-size: 1.5rem; } }
    ul.page-links li a {
      color: #000; }
      ul.page-links li a.active, ul.page-links li a:hover {
        color: #0081B4; }

.tab {
  line-height: 1;
  color: #fff;
  display: block;
  font-size: 16px;
  font-size: 1rem;
  padding: 12px; }
  @media only screen and (min-width: 64.063em) {
    .tab {
      font-size: 20px;
      font-size: 1.25rem;
      padding: 20px 10px;
      border-left: 1px solid #fff; } }
  .tab:hover {
    text-decoration: none;
    color: #fff; }
  .tab.royal-blue, .reset-password-form section form div.controls button.tab {
    background: #0090CF; }
    .tab.royal-blue:hover, .reset-password-form section form div.controls button.tab:hover, .tab.royal-blue:active, .reset-password-form section form div.controls button.tab:active {
      background: #007eb6; }
  .tab.light-blue {
    background: #00a0e0; }
    .tab.light-blue:hover, .tab.light-blue:active {
      background: #008ec7; }
  .tab.black {
    background: #000; }
    .tab.black:hover, .tab.black:active {
      background: #1a1a1a; }
  .tab.alternative-blue {
    background: #6ec4e9; }
    .tab.alternative-blue:hover, .tab.alternative-blue:active {
      background: #9ad6f0; }
  .tab.active {
    background-color: #074598 !important; }

.contourMessageOnSubmit {
  padding: 12px;
  color: #fff;
  background: #6ebf58; }


div.contour form {
  
  border: 1px solid #ddd;
  padding: 24px; }
  div.contour form .dropdown div {
    position: relative;
    background-color: #dadada;
    overflow: visible; }
    div.contour form .dropdown div:hover select {
      cursor: pointer; }
    div.contour form .dropdown div:hover:after {
      background: #005c81; }
    div.contour form .dropdown div:after {
      content: "\e909";
      /* use !important to prevent issues with browser extensions that change fonts */
      font-family: 'icomoon' !important;
      speak: none;
      font-style: normal;
      font-weight: normal;
      font-variant: normal;
      text-transform: none;
      line-height: 1;
      /* Better Font Rendering =========== */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      font-size: 8px;
      line-height: 40px;
      color: #fff;
      width: 36px;
      height: 36px;
      position: absolute;
      pointer-events: none;
      right: 0;
      top: 0;
      background: #0081B4;
      text-align: center; }
      @media only screen and (min-width: 64.063em) {
        div.contour form .dropdown div:after {
          width: 48px;
          height: 48px;
          font-size: 10px;
          line-height: 50px; } }
    div.contour form .dropdown div select {
      height: 36px;
      padding: 0 12px;
      line-height: 1.6;
      color: #0081B4;
      font-size: 14px;
      font-size: 0.875rem; }
      @media only screen and (min-width: 64.063em) {
        div.contour form .dropdown div select {
          height: 48px;
          font-size: 16px;
          font-size: 1rem; } }

div.contour label {
  margin: 0 0 12px 0;
  display: block;
  color: #0081B4; }

div.contour input, div.contour select, div.contour textarea {
  padding: 8px 12px;
  display: block;
  width: 100%;
  margin: 0 0 24px 0;
  border: 1px solid #ddd; }
  div.contour input.input-validation-error, div.contour select.input-validation-error, div.contour textarea.input-validation-error {
    border-color: #bb3939; }

.input-group input {
  margin: 0px!important;
}

div.contour .field-validation-error {
  padding: 0px 0 12px 0;
  color: #bb3939;
  display: block; }

div.contour input[type="submit"] {
  color: #fff;
  background: #0081B4;
  border: 1px solid #0081B4;
  width: auto;
  padding: 12px 24px; }
  div.contour input[type="submit"]:hover, div.contour input[type="submit"]:active {
    background: #005c81;
    border: 1px solid #005c81; }

.login-status {
  margin: 12px 0; }
  @media only screen and (min-width: 64.063em) {
    .login-status div.container {
      text-align: right; } }
  .login-status div.container p, .login-status div.container form, .login-status div.container button {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline; }
  .login-status div.container p {
    margin: 0 12px 0 0; }

#contact-locations .col.span-2-6 {
  border-top: 0 !important;
  padding: 0 0 12px 0; }
  @media only screen and (min-width: 64.063em) {
    #contact-locations .col.span-2-6 {
      padding: 0 0 24px 0; } }
  #contact-locations .col.span-2-6 a figure.location {
    margin: 0 0 12px !important; }
  #contact-locations .col.span-2-6 a h3 {
    padding: 0 0 12px !important;
    font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif; }

input.error {
  color: red !important;
  border-color: red !important; }

/* 
* Animations and keyframes
*   -Usage: 
* @include keyframes(slide-down) {
*   0% { opacity: 1; }
*   90% { opacity: 0; }
* }
* .element {
*   @include animation('slide-down 5s 3');
* }
*/
@-webkit-keyframes fadeIn {
  0% {
    opacity: 0;
    filter: alpha(opacity=0); }
  100% {
    opacity: 1;
    filter: alpha(opacity=100); } }

@-moz-keyframes fadeIn {
  0% {
    opacity: 0;
    filter: alpha(opacity=0); }
  100% {
    opacity: 1;
    filter: alpha(opacity=100); } }

@-ms-keyframes fadeIn {
  0% {
    opacity: 0;
    filter: alpha(opacity=0); }
  100% {
    opacity: 1;
    filter: alpha(opacity=100); } }

@-o-keyframes fadeIn {
  0% {
    opacity: 0;
    filter: alpha(opacity=0); }
  100% {
    opacity: 1;
    filter: alpha(opacity=100); } }

@keyframes fadeIn {
  0% {
    opacity: 0;
    filter: alpha(opacity=0); }
  100% {
    opacity: 1;
    filter: alpha(opacity=100); } }

div.desktop-search {
  padding: 12px 0;
  display: none; }
  div.desktop-search.active {
    display: block; }
  div.desktop-search div.container {
    text-align: right; }
    div.desktop-search div.container form.search {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      text-align: left; }
      div.desktop-search div.container form.search input {
        border: 1px solid #616161;
        padding: 12px;
        width: 300px; }

@media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
  header.main-header {
    padding: 14px 0 0 0; } }

@media only screen and (min-width: 64.063em) {
  header.main-header {
    padding: 24px 0 48px 0; } }

header.main-header div.container div.main-logo {
  padding: 14px 0;
  position: relative; }
  @media only screen and (min-width: 47.938em) {
    header.main-header div.container div.main-logo {
      float: left;
      padding: 0; } }
  header.main-header div.container div.main-logo.active {
    margin: 14px 0 0 0;
    padding: 14px 14px 0px 14px;
    background: #0081B4; }
    header.main-header div.container div.main-logo.active figure {
      background-image: url(../assets/img/static/trident-logo-white.svg);
      background-size: 100%; }
      .no-svg header.main-header div.container div.main-logo.active figure {
        background-image: url(../assets/img/static/trident-logo-white.png); }
        @media only screen and (-moz-min-device-pixel-ratio: 1.5), only screen and (-o-min-device-pixel-ratio: 3 / 2), only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-device-pixel-ratio: 1.5) {
          .no-svg header.main-header div.container div.main-logo.active figure {
            background-image: url(../assets/img/static/<EMAIL>); } }
      header.main-header div.container div.main-logo.active figure img {
        display: none; }
    header.main-header div.container div.main-logo.active .menu-btn {
      right: 10px;
      color: #fff; }
  header.main-header div.container div.main-logo a {
    display: block; }
    header.main-header div.container div.main-logo a.menu-btn {
      position: absolute;
      right: 0;
      top: 10px;
      color: #00269A; }
      @media only screen and (min-width: 47.938em) {
        header.main-header div.container div.main-logo a.menu-btn {
          display: none; } }
      header.main-header div.container div.main-logo a.menu-btn:hover {
        text-decoration: none; }
      header.main-header div.container div.main-logo a.menu-btn i {
        font-size: 32px;
        font-size: 2rem; }
    header.main-header div.container div.main-logo a img {
      padding: 5px 0 0 0;
      display: block; }

header.main-header div.container div.navigation {
  padding: 14px;
  background: #0081B4;
  display: none;
  box-orient: vertical;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column; }
  @media only screen and (min-width: 47.938em) {
    header.main-header div.container div.navigation {
      display: block;
      background: transparent;
      padding: 0; } }
  header.main-header div.container div.navigation.active {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex; }
  @media only screen and (min-width: 47.938em) {
    header.main-header div.container div.navigation nav ul {
      line-height: 1; } }
  @media only screen and (min-width: 47.938em) {
    header.main-header div.container div.navigation nav ul li {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline; } }
  header.main-header div.container div.navigation nav ul li a {
    color: #fff;
    display: block;
    padding: 8px 0; }
    @media only screen and (min-width: 47.938em) {
      header.main-header div.container div.navigation nav ul li a {
        color: #000;
        padding: 0; } }
  header.main-header div.container div.navigation nav.sub-nav {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2; }
    @media only screen and (min-width: 47.938em) {
      header.main-header div.container div.navigation nav.sub-nav {
        float: right;
        text-align: right;
        width: 515px; } }
    @media only screen and (min-width: 47.938em) {
      header.main-header div.container div.navigation nav.sub-nav ul:first-child {
        margin: 0 0 16px 0; }
        header.main-header div.container div.navigation nav.sub-nav ul:first-child li:first-child a {
          border-left: 0;
          padding-left: 0; }
        header.main-header div.container div.navigation nav.sub-nav ul:first-child li:last-child a {
          padding-right: 0; }
        header.main-header div.container div.navigation nav.sub-nav ul:first-child li a {
          border-left: 1px solid #0081B4;
          padding: 0 10px; } }
    @media only screen and (min-width: 47.938em) and (min-width: 64.063em) {
      header.main-header div.container div.navigation nav.sub-nav ul:first-child li a {
        padding: 0 16px; } }
    header.main-header div.container div.navigation nav.sub-nav ul li.desktop-search {
      display: none; }
      @media only screen and (min-width: 47.938em) {
        header.main-header div.container div.navigation nav.sub-nav ul li.desktop-search {
          display: -moz-inline-stack;
          display: inline-block;
          zoom: 1;
          *display: inline; } }
    header.main-header div.container div.navigation nav.sub-nav ul li a {
      font-size: 12px;
      font-size: 0.75rem; }
      @media only screen and (min-width: 47.938em) {
        header.main-header div.container div.navigation nav.sub-nav ul li a {
          color: #0081B4;
          line-height: 1; } }
      @media only screen and (min-width: 64.063em) {
        header.main-header div.container div.navigation nav.sub-nav ul li a {
          font-size: 16px;
          font-size: 1rem; } }
      header.main-header div.container div.navigation nav.sub-nav ul li a i {
        display: -moz-inline-stack;
        display: inline-block;
        zoom: 1;
        *display: inline;
        padding: 0 8px 0 0; }
      @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
        header.main-header div.container div.navigation nav.sub-nav ul li a.social-link {
          padding: 0 10px; } }
      @media only screen and (min-width: 64.063em) {
        header.main-header div.container div.navigation nav.sub-nav ul li a.social-link {
          padding: 0 5px; } }
      header.main-header div.container div.navigation nav.sub-nav ul li a.social-link:hover {
        text-decoration: none;
        color: #005c81; }
      header.main-header div.container div.navigation nav.sub-nav ul li a.social-link i {
        display: none; }
        @media only screen and (min-width: 47.938em) {
          header.main-header div.container div.navigation nav.sub-nav ul li a.social-link i {
            display: block;
            font-size: 18px;
            font-size: 1.125rem;
            padding: 0; } }
        @media only screen and (min-width: 64.063em) {
          header.main-header div.container div.navigation nav.sub-nav ul li a.social-link i {
            font-size: 22px;
            font-size: 1.375rem; } }
      header.main-header div.container div.navigation nav.sub-nav ul li a.social-link span {
        display: block; }
        @media only screen and (min-width: 47.938em) {
          header.main-header div.container div.navigation nav.sub-nav ul li a.social-link span {
            display: none; } }
      header.main-header div.container div.navigation nav.sub-nav ul li a.btn {
        color: #fff;
        text-align: left; }
        @media only screen and (min-width: 47.938em) {
          header.main-header div.container div.navigation nav.sub-nav ul li a.btn {
            text-align: center;
            padding: 8px 16px;
            margin: 0 0 0 16px; } }
        @media only screen and (min-width: 64.063em) {
          header.main-header div.container div.navigation nav.sub-nav ul li a.btn {
            padding: 4px 24px 8px;
            margin: 0 0 0 24px; } }
  header.main-header div.container div.navigation nav.main-nav-items {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1; }
    @media only screen and (min-width: 47.938em) {
      header.main-header div.container div.navigation nav.main-nav-items {
        clear: both;
        padding: 24px 0 0 0; } }
    @media only screen and (min-width: 64.063em) {
      header.main-header div.container div.navigation nav.main-nav-items {
        padding: 48px 0 0 0; } }
    @media only screen and (min-width: 47.938em) {
      header.main-header div.container div.navigation nav.main-nav-items ul li {
        width: 110px;
        margin: 0 0 0 30px; }
        header.main-header div.container div.navigation nav.main-nav-items ul li:first-child {
          margin: 0; } }
    @media only screen and (min-width: 64.063em) {
      header.main-header div.container div.navigation nav.main-nav-items ul li {
        display: -moz-inline-stack;
        display: inline-block;
        zoom: 1;
        *display: inline;
        margin-left: 5%;
        width: 15.98636%;
        vertical-align: top;
        position: relative; }
        header.main-header div.container div.navigation nav.main-nav-items ul li:hover .dropdown, header.main-header div.container div.navigation nav.main-nav-items ul li.active-drop .dropdown {
          display: block; } }
    @media only screen and (min-width: 47.938em) {
      header.main-header div.container div.navigation nav.main-nav-items ul li.mobile-search {
        display: none; } }
    header.main-header div.container div.navigation nav.main-nav-items ul li a {
      border-bottom: 1px solid #fff; }
      @media only screen and (min-width: 47.938em) {
        header.main-header div.container div.navigation nav.main-nav-items ul li a {
          padding: 0 0 8px 0;
          border-color: #000; } }
      @media only screen and (min-width: 64.063em) {
        header.main-header div.container div.navigation nav.main-nav-items ul li a {
          font-size: 24px;
          font-size: 1.5rem; } }
      header.main-header div.container div.navigation nav.main-nav-items ul li a:hover, header.main-header div.container div.navigation nav.main-nav-items ul li a.active {
        text-decoration: none; }
        @media only screen and (min-width: 64.063em) {
          header.main-header div.container div.navigation nav.main-nav-items ul li a:hover, header.main-header div.container div.navigation nav.main-nav-items ul li a.active {
            color: #0081B4; } }
    header.main-header div.container div.navigation nav.main-nav-items ul li .dropdown {
      padding: 10px; }
      header.main-header div.container div.navigation nav.main-nav-items ul li .dropdown.active {
        display: block; }
      @media only screen and (min-width: 47.938em) {
        header.main-header div.container div.navigation nav.main-nav-items ul li .dropdown {
          display: none;
          width: 100%;
          padding: 4px 14px 14px 14px;
          background: #0081B4;
          position: absolute;
          z-index: 999; } }
      header.main-header div.container div.navigation nav.main-nav-items ul li .dropdown ul li {
        width: 100%;
        margin: 0;
        display: block; }
        header.main-header div.container div.navigation nav.main-nav-items ul li .dropdown ul li a {
          color: #fff; }
          @media only screen and (min-width: 47.938em) {
            header.main-header div.container div.navigation nav.main-nav-items ul li .dropdown ul li a {
              padding: 14px 0;
              border-color: #fff;
              font-size: 14px;
              font-size: 0.875rem; }
              header.main-header div.container div.navigation nav.main-nav-items ul li .dropdown ul li a:hover {
                background: #006f9b; } }
    header.main-header div.container div.navigation nav.main-nav-items ul li input {
      font-size: 16px;
      font-size: 1rem;
      width: 100%;
      padding: 8px 0;
      color: #00269A;
      border-bottom: 1px solid #fff; }
      header.main-header div.container div.navigation nav.main-nav-items ul li input::-webkit-input-placeholder {
        color: #00269A; }

footer.main-footer div.container div.footer-items {
  border-top: 1px solid #000;
  border-bottom: 1px solid #000;
  padding: 24px 0;
  margin: 24px 0; }
  @media only screen and (min-width: 64.063em) {
    footer.main-footer div.container div.footer-items {
      position: relative;
      margin: 96px 0 24px;
      min-height: 200px; } }
  @media only screen and (min-width: 64.063em) {
    footer.main-footer div.container div.footer-items nav.sitemap, footer.main-footer div.container div.footer-items div.site-meta {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      vertical-align: top; } }
  @media only screen and (min-width: 64.063em) {
    footer.main-footer div.container div.footer-items nav.sitemap {
      width: 75.00%; } }
  footer.main-footer div.container div.footer-items nav.sitemap ul {
    padding: 0 0 16px 0; }
    @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
      footer.main-footer div.container div.footer-items nav.sitemap ul {
        display: -moz-inline-stack;
        display: inline-block;
        zoom: 1;
        *display: inline;
        margin-left: 0;
        width: 40%;
        vertical-align: top;
        padding: 16px 0; } }
    @media only screen and (min-width: 64.063em) {
      footer.main-footer div.container div.footer-items nav.sitemap ul {
        display: -moz-inline-stack;
        display: inline-block;
        zoom: 1;
        *display: inline;
        vertical-align: top;
        padding: 0 0 0 32px; }
        footer.main-footer div.container div.footer-items nav.sitemap ul:first-child {
          padding-left: 0; } }
    @media only screen and (min-width: 64.063em) {
      footer.main-footer div.container div.footer-items nav.sitemap ul li {
        font-size: 15px;
        font-size: 0.9375rem; } }
    @media only screen and (min-width: 64.063em) {
      footer.main-footer div.container div.footer-items nav.sitemap ul li h3, footer.main-footer div.container div.footer-items nav.sitemap ul li a {
        font-size: 15px;
        font-size: 0.9375rem; } }
    footer.main-footer div.container div.footer-items nav.sitemap ul li h3 {
      color: #0081B4;
      padding: 0 0 10px 0; }
      @media only screen and (min-width: 64.063em) {
        footer.main-footer div.container div.footer-items nav.sitemap ul li h3 {
          padding: 0 0 24px 0; } }
    footer.main-footer div.container div.footer-items nav.sitemap ul li a {
      color: #000;
      display: block;
      padding: 0 0 5px 0; }
  footer.main-footer div.container div.footer-items div.site-meta {
    text-align: right;
    padding: 16px 0; }
    @media only screen and (min-width: 64.063em) {
      footer.main-footer div.container div.footer-items div.site-meta {
        width: 25.00%;
        padding: 0; } }
    footer.main-footer div.container div.footer-items div.site-meta a.scroll-top {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      color: #000;
      text-align: right; }
      footer.main-footer div.container div.footer-items div.site-meta a.scroll-top div {
        -webkit-border-radius: 50%;
        -moz-border-radius: 50%;
        -ms-border-radius: 50%;
        border-radius: 50%;
        display: -moz-inline-stack;
        display: inline-block;
        zoom: 1;
        *display: inline;
        background-color: #000;
        position: relative;
        width: 30px;
        height: 30px; }
        footer.main-footer div.container div.footer-items div.site-meta a.scroll-top div i {
          color: #fff;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          -ms-transform: translate(-50%, -50%);
          -webkit-transform: translate(-50%, -50%);
          font-size: 9px; }
      footer.main-footer div.container div.footer-items div.site-meta a.scroll-top span {
        display: block; }
    @media only screen and (min-width: 64.063em) {
      footer.main-footer div.container div.footer-items div.site-meta div.main-logo {
        position: absolute;
        bottom: 24px;
        right: 0;
        width: 300px; } }
    footer.main-footer div.container div.footer-items div.site-meta div.main-logo a figure {
      padding: 16px 0; }
      @media only screen and (min-width: 64.063em) {
        footer.main-footer div.container div.footer-items div.site-meta div.main-logo a figure {
          width: 100%; } }

footer.main-footer div.container div.sub-foot {
  clear: both;
  padding: 12px 0;
  overflow: auto; }
  @media only screen and (min-width: 64.063em) {
    footer.main-footer div.container div.sub-foot {
      padding: 12px 0 24px 0; } }
  @media only screen and (min-width: 64.063em) {
    footer.main-footer div.container div.sub-foot div.misc-items {
      float: left; } }
  footer.main-footer div.container div.sub-foot div.misc-items ul li {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline; }
    @media only screen and (min-width: 64.063em) {
      footer.main-footer div.container div.sub-foot div.misc-items ul li {
        margin: 0 12px 0 0; } }
    footer.main-footer div.container div.sub-foot div.misc-items ul li a {
      color: #000; }
  @media only screen and (min-width: 64.063em) {
    footer.main-footer div.container div.sub-foot div.site-by {
      text-align: right;
      float: right; } }
  @media only screen and (min-width: 64.063em) {
    footer.main-footer div.container div.sub-foot div.site-by p {
      margin: 0;
      font-size: 14px;
      font-size: 0.875rem; } }

/*
 * jQuery FlexSlider v2.6.3
 * http://www.woothemes.com/flexslider/
 *
 * Copyright 2012 WooThemes
 * Free to use under the GPLv2 and later license.
 * http://www.gnu.org/licenses/gpl-2.0.html
 *
 * Contributing author: Tyler Smith (@mbmufffin)
 *
 */
/* ====================================================================================================================
 * RESETS
 * ====================================================================================================================*/
.flex-container a:hover,
.flex-slider a:hover {
  outline: none; }

.slides,
.slides > li,
.flex-control-nav,
.flex-direction-nav {
  margin: 0;
  padding: 0;
  list-style: none; }

.flex-pauseplay span {
  text-transform: capitalize; }

/* ====================================================================================================================
 * BASE STYLES
 * ====================================================================================================================*/
.flexslider {
  margin: 0;
  padding: 0; }

.flexslider .slides > li {
  display: none;
  -webkit-backface-visibility: hidden;
  overflow: hidden; }
  .flexslider .slides > li.flex-active-slide {
    overflow: visible; }

.flexslider .slides img {
  width: 100%;
  display: block; }

.flexslider .slides:after {
  content: "\0020";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0; }

html[xmlns] .flexslider .slides {
  display: block; }

* html .flexslider .slides {
  height: 1%; }

.no-js .flexslider .slides > li:first-child {
  display: block; }

/* ====================================================================================================================
 * RESPONSIVE
 * ====================================================================================================================*/
@media screen and (max-width: 860px) {
  .flex-direction-nav .flex-prev {
    opacity: 1;
    left: 10px; }
  .flex-direction-nav .flex-next {
    opacity: 1;
    right: 10px; } }

div.hero-banner {
  margin: 12px 0; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    div.hero-banner {
      margin: 24px 0; } }
  @media only screen and (min-width: 64.063em) {
    div.hero-banner {
      margin: 48px 0;
      min-height: 565px; } }
  div.hero-banner div.container div.flexslider {
    position: relative; }
    div.hero-banner div.container div.flexslider ul.slides li figure {
      position: relative;
      /* &:before{
                     @include centerer(true, true);
                     width: 100%;
                     height: 100%;
                     outline: 1px solid #fff;
                     outline-offset: -14px;
                     outline: 1px solid #fff;
                     @include bp-large{
                        outline-offset: -24px;
                        background: -moz-linear-gradient(top, rgba(0,0,0,0.65) 2%, rgba(0,0,0,0) 100%); 
                        background: -webkit-linear-gradient(top, rgba(0,0,0,0.65) 2%,rgba(0,0,0,0) 100%); 
                        background: linear-gradient(to bottom, rgba(0,0,0,0.65) 2%,rgba(0,0,0,0) 100%); 
                        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a6000000', endColorstr='#00000000',GradientType=0 );
                     }        
                  }
                  &:after{
                     width: 0;
                     height: 0;
                     //background: #000;
                     border-style: solid;
                     border-width: 44px 44px 0 0;
                     border-color: transparent $royal-blue transparent transparent;
                     position: absolute;
                     bottom: 12px;
                     right: 13px;
                     z-index: 5;
                     @include bp-large{
                        bottom: 23px;
                        right: 23px;
                        border-width: 74px 74px 0 0;
                     }
                  }*/ }
      div.hero-banner div.container div.flexslider ul.slides li figure:after, div.hero-banner div.container div.flexslider ul.slides li figure:before {
        content: ""; }
      div.hero-banner div.container div.flexslider ul.slides li figure:before {
        width: 100%;
        height: 100%;
        background: url(../assets/img/static/banner-border-mobile.png) no-repeat;
        background: url(../assets/img/static/banner-border-mobile.svg) no-repeat;
        background-position: center center;
        background-size: 92.5%;
        position: absolute;
        z-index: 10;
        top: 0;
        left: 0; }
        @media only screen and (min-width: 64.063em) {
          div.hero-banner div.container div.flexslider ul.slides li figure:before {
            background: url(../assets/img/static/banner-border.png) no-repeat;
            background-position: center center;
            background-size: 96.5%; } }
      div.hero-banner div.container div.flexslider ul.slides li figure:after {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 9;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        /* @include bp-large{
                        background: -moz-linear-gradient(top, rgba(0,0,0,0.65) 2%, rgba(0,0,0,0) 100%); 
                        background: -webkit-linear-gradient(top, rgba(0,0,0,0.65) 2%,rgba(0,0,0,0) 100%); 
                        background: linear-gradient(to bottom, rgba(0,0,0,0.65) 2%,rgba(0,0,0,0) 100%); 
                        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a6000000', endColorstr='#00000000',GradientType=0 );
                     }*/ }
      div.hero-banner div.container div.flexslider ul.slides li figure img.mobile-version {
        display: block; }
        @media only screen and (min-width: 64.063em) {
          div.hero-banner div.container div.flexslider ul.slides li figure img.mobile-version {
            display: none; } }
      div.hero-banner div.container div.flexslider ul.slides li figure img.desktop-version {
        display: none; }
        @media only screen and (min-width: 64.063em) {
          div.hero-banner div.container div.flexslider ul.slides li figure img.desktop-version {
            display: block; } }
      div.hero-banner div.container div.flexslider ul.slides li figure figcaption {
        max-width: 480px;
        position: absolute;
        z-index: 10; }
        @media only screen and (max-width: 64.063em) {
          div.hero-banner div.container div.flexslider ul.slides li figure figcaption {
            top: 7.5%;
            left: 7.5%; } }
        @media only screen and (min-width: 64.063em) {
          div.hero-banner div.container div.flexslider ul.slides li figure figcaption {
            top: 30px;
            left: 60px;
            padding: 10px 0; } }
        div.hero-banner div.container div.flexslider ul.slides li figure figcaption h1, div.hero-banner div.container div.flexslider ul.slides li figure figcaption h2 {
          color: #0081B4; }
        div.hero-banner div.container div.flexslider ul.slides li figure figcaption p {
          font-size: 12px;
          font-size: 0.75rem;
          color: #fff;
          max-width: 260px;
          line-height: 1.5; }
          @media only screen and (min-width: 64.063em) {
            div.hero-banner div.container div.flexslider ul.slides li figure figcaption p {
              max-width: none;
              padding: 0;
              font-size: 22px;
              font-size: 1.375rem; } }
    div.hero-banner div.container div.flexslider ol.flex-control-paging {
      z-index: 5; }
      @media only screen and (max-width: 64.063em) {
        div.hero-banner div.container div.flexslider ol.flex-control-paging {
          position: absolute;
          bottom: 7.5%;
          left: 7.5%; } }
      @media only screen and (min-width: 64.063em) {
        div.hero-banner div.container div.flexslider ol.flex-control-paging {
          position: absolute;
          top: 50%;
          transform: translate(0, -50%);
          -ms-transform: translate(0, -50%);
          -webkit-transform: translate(0, -50%);
          right: 39px; } }
      @media only screen and (min-width: 64.063em) {
        div.hero-banner div.container div.flexslider ol.flex-control-paging li {
          display: block;
          margin: 0 0 12px 0; } }

div.news-feed {
  margin: 24px 0 0 0; }
  @media only screen and (min-width: 64.063em) {
    div.news-feed {
      margin: 38px 0 0 0; } }
  div.news-feed div.container h2 {
    padding: 0 0 24px 0; }
    @media only screen and (min-width: 64.063em) {
      div.news-feed div.container h2 {
        padding: 0 0 48px 0; } }
  div.news-feed div.container div.row div.col a figure p {
    color: #000;
    font-size: 14px;
    font-size: 0.875rem; }
    @media only screen and (min-width: 47.938em) {
      div.news-feed div.container div.row div.col a figure p {
        font-size: 14px;
        font-size: 0.875rem; } }
  div.news-feed div.container div.row div.col a figure img {
    display: block;
    margin: 24px 0; }
    @media only screen and (min-width: 64.063em) {
      div.news-feed div.container div.row div.col a figure img {
        margin: 38px 0; } }

@media only screen and (min-width: 64.063em) {
  main.news-page div.row .col.span-4-6 article {
    margin: 0 !important; } }

@media only screen and (min-width: 64.063em) {
  main.news-page div.team-locations {
    padding: 0 1%; } }

main.news-page div.team-locations div.col.span-2-6 {
  padding: 0 0 12px 0; }
  @media only screen and (min-width: 47.938em) {
    main.news-page div.team-locations div.col.span-2-6 {
      padding: 0 0 24px 0; } }
  main.news-page div.team-locations div.col.span-2-6 a.btn {
    padding: 12px 16px 13px; }

main.news-page div.news-result div.col.span-2-6 {
  border-top: 0 !important; }
  @media only screen and (min-width: 64.063em) {
    main.news-page div.news-result div.col.span-2-6 {
      margin: 0 1% !important; } }
  main.news-page div.news-result div.col.span-2-6 h3 {
    margin: 0;
    padding: 0;
    font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    line-height: 1.25;
    font-size: 18px;
    font-size: 1.125rem; }
  main.news-page div.news-result div.col.span-2-6 p {
    color: #000;
    font-size: 14px;
    font-size: 0.875rem; }
  main.news-page div.news-result div.col.span-2-6 a.read-more {
    display: block;
    margin: 0 0 24px; }

div.service-feed, div.about-service-feed {
  margin: 24px 0 0 0; }
  @media only screen and (min-width: 64.063em) {
    div.service-feed, div.about-service-feed {
      margin: 38px 0 0 0; } }
  div.service-feed h2, div.about-service-feed h2 {
    padding: 0 0 24px 0; }
    @media only screen and (min-width: 64.063em) {
      div.service-feed h2, div.about-service-feed h2 {
        padding: 0 0 48px 0; } }
  div.service-feed div.col figure div.fig-img, div.about-service-feed div.col figure div.fig-img {
    position: relative; }
    div.service-feed div.col figure div.fig-img:before, div.about-service-feed div.col figure div.fig-img:before {
      -webkit-transition: background, 0.25s, linear;
      -moz-transition: background, 0.25s, linear;
      -ms-transition: background, 0.25s, linear;
      -o-transition: background, 0.25s, linear;
      transition: background, 0.25s, linear;
      pointer-events: none;
      content: "";
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: transparent;
      z-index: 10; }
  div.service-feed div.col figure figcaption, div.about-service-feed div.col figure figcaption {
    margin: 12px 0 24px 0; }
    @media only screen and (min-width: 64.063em) {
      div.service-feed div.col figure figcaption, div.about-service-feed div.col figure figcaption {
        margin: 24px 0 48px 0; } }
    div.service-feed div.col figure figcaption h3, div.about-service-feed div.col figure figcaption h3 {
      background: #0081B4;
      padding: 10px;
      color: #fff;
      line-height: 1;
      font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif; }
      @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
        div.service-feed div.col figure figcaption h3, div.about-service-feed div.col figure figcaption h3 {
          font-size: 16px;
          font-size: 1rem; } }
    div.service-feed div.col figure figcaption p, div.about-service-feed div.col figure figcaption p {
      color: #000;
      padding: 8px 0; }
      @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
        div.service-feed div.col figure figcaption p, div.about-service-feed div.col figure figcaption p {
          font-size: 14px;
          font-size: 0.875rem; } }
  div.service-feed div.col figure img, div.about-service-feed div.col figure img {
    display: block; }
  div.service-feed div.col a:hover, div.about-service-feed div.col a:hover {
    text-decoration: none; }
    div.service-feed div.col a:hover figure div.fig-img:before, div.about-service-feed div.col a:hover figure div.fig-img:before {
      background: rgba(0, 130, 203, 0.35); }

div.about-service-feed {
  border-top: 1px solid #616161; }
  div.about-service-feed h2 {
    padding: 12px 0 24px 0 !important; }
    @media only screen and (min-width: 64.063em) {
      div.about-service-feed h2 {
        padding: 24px 0 !important; } }
  div.about-service-feed div.row div.col.span-3-6 {
    border-top: 0 !important;
    padding-top: 0 !important; }
    div.about-service-feed div.row div.col.span-3-6 figure figcaption {
      margin: 12px 0 24px 0; }
      @media only screen and (min-width: 64.063em) {
        div.about-service-feed div.row div.col.span-3-6 figure figcaption {
          margin: 24px 0 0px 0; } }

div.accolades {
  margin: 24px 0 0 0; }
  @media only screen and (min-width: 64.063em) {
    div.accolades {
      margin: 38px 0 0 0; } }
  div.accolades div.container div.row {
    position: relative; }
    div.accolades div.container div.row div.col blockquote {
      margin: 0; }
      div.accolades div.container div.row div.col blockquote p {
        line-height: 1.5;
        padding-bottom: 16px; }
        @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
          div.accolades div.container div.row div.col blockquote p {
            font-size: 14px;
            font-size: 0.875rem;
            padding-bottom: 40px; } }
        @media only screen and (min-width: 64.063em) {
          div.accolades div.container div.row div.col blockquote p {
            padding-bottom: 40px; } }
      div.accolades div.container div.row div.col blockquote cite {
        font-style: normal;
        font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        display: block;
        padding: 10px 0;
        color: #00269A; }
      @media only screen and (min-width: 64.063em) {
        div.accolades div.container div.row div.col blockquote a {
          position: absolute;
          bottom: 0; } }

main.page-article {
  margin: 24px 0 0 0; }
  @media only screen and (min-width: 64.063em) {
    main.page-article {
      margin: 38px 0 0 0; } }
  main.page-article div.container div.row div.col {
    border-top: 1px solid #616161;
    padding: 24px 0 0 0;
    margin-top: 24px; }
    @media only screen and (min-width: 47.938em) {
      main.page-article div.container div.row div.col {
        margin-top: 0; } }
    main.page-article div.container div.row div.col:first-child {
      margin-top: 0; }
    main.page-article div.container div.row div.col aside h2 {
      font-size: 18px;
      font-size: 1.125rem; }
      @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
        main.page-article div.container div.row div.col aside h2 {
          font-size: 20px;
          font-size: 1.25rem; } }
      @media only screen and (min-width: 64.063em) {
        main.page-article div.container div.row div.col aside h2 {
          font-size: 36px;
          font-size: 2.25rem; } }
    main.page-article div.container div.row div.col aside ul {
      padding: 14px 0; }
      @media only screen and (min-width: 64.063em) {
        main.page-article div.container div.row div.col aside ul {
          padding: 0 0 48px; } }
      main.page-article div.container div.row div.col aside ul.page-links li {
        line-height: 1.5; }
        @media only screen and (min-width: 64.063em) {
          main.page-article div.container div.row div.col aside ul.page-links li {
            font-size: 24px;
            font-size: 1.5rem; } }
        main.page-article div.container div.row div.col aside ul.page-links li a {
          color: #000; }
          main.page-article div.container div.row div.col aside ul.page-links li a.active, main.page-article div.container div.row div.col aside ul.page-links li a:hover {
            color: #0081B4; }
      main.page-article div.container div.row div.col aside ul.feed-links {
        margin: 12px 0;
        border-top: 1px solid #616161; }
        @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
          main.page-article div.container div.row div.col aside ul.feed-links {
            margin: 20px 0; } }
        @media only screen and (min-width: 64.063em) {
          main.page-article div.container div.row div.col aside ul.feed-links {
            margin: 0;
            padding: 24px 0 0 0; } }
        main.page-article div.container div.row div.col aside ul.feed-links li h3 {
          padding: 0 0 10px 0;
          color: #0081B4; }
          @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
            main.page-article div.container div.row div.col aside ul.feed-links li h3 {
              font-size: 18px;
              font-size: 1.125rem; } }
          @media only screen and (min-width: 64.063em) {
            main.page-article div.container div.row div.col aside ul.feed-links li h3 {
              padding: 0 0 24px 0; } }
        main.page-article div.container div.row div.col aside ul.feed-links li a {
          color: #000;
          display: block;
          line-height: 1.5;
          font-size: 14px;
          font-size: 0.875rem; }
          @media only screen and (min-width: 64.063em) {
            main.page-article div.container div.row div.col aside ul.feed-links li a {
              padding: 0 0 5px 0; } }
          main.page-article div.container div.row div.col aside ul.feed-links li a:after {
            content: " >";
            display: -moz-inline-stack;
            display: inline-block;
            zoom: 1;
            *display: inline;
            margin: 0 0 0 5px; }
    main.page-article div.container div.row div.col article {
      margin: 0 0 12px 0; }
      @media only screen and (min-width: 64.063em) {
        main.page-article div.container div.row div.col article {
          margin: 0 0 36px 0; } }
      main.page-article div.container div.row div.col article.no-margin {
        margin: 0; }
        @media only screen and (min-width: 64.063em) {
          main.page-article div.container div.row div.col article.no-margin {
            margin: 0; } }
      main.page-article div.container div.row div.col article h3 {
        padding: 20px 0 10px 0; }
      main.page-article div.container div.row div.col article #Header3 {
        font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        color: #0081B4; }
      main.page-article div.container div.row div.col article p {
        padding: 0 0 10px 0;
        margin-top: 0; }
        main.page-article div.container div.row div.col article p.intro {
          padding: 0 0 14px 0; }
          @media only screen and (min-width: 64.063em) {
            main.page-article div.container div.row div.col article p.intro {
              padding: 0 0 24px 0; } }
      main.page-article div.container div.row div.col article ul, main.page-article div.container div.row div.col article ol {
        padding: 10px 0;
        margin: 0 0 0 8px; }
      main.page-article div.container div.row div.col article ul li {
        list-style-type: none;
        position: relative;
        padding: 0 0 0 24px;
        font-size: 14px;
        font-size: 0.875rem; }
        @media only screen and (min-width: 64.063em) {
          main.page-article div.container div.row div.col article ul li {
            font-size: 18px;
            font-size: 1.125rem; } }
        main.page-article div.container div.row div.col article ul li:before {
          display: -moz-inline-stack;
          display: inline-block;
          zoom: 1;
          *display: inline;
          content: "—";
          position: absolute;
          left: 0; }
      main.page-article div.container div.row div.col article figure {
        margin: 14px 0; }
      main.page-article div.container div.row div.col article ul.vacancy-list {
        padding: 0;
        margin: 24px 0; }
        main.page-article div.container div.row div.col article ul.vacancy-list li {
          border-bottom: 1px solid #0081B4;
          padding: 0 0 24px;
          margin: 0 0 24px; }
          main.page-article div.container div.row div.col article ul.vacancy-list li:before {
            display: none; }
          main.page-article div.container div.row div.col article ul.vacancy-list li h3 {
            color: #000;
            font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
            padding: 0 0 20px; }
          main.page-article div.container div.row div.col article ul.vacancy-list li p {
            padding: 0; }
          main.page-article div.container div.row div.col article ul.vacancy-list li a {
            display: block; }
  main.page-article div.container div.row.service-feed div.span-2-6, main.page-article div.container div.row.people-feed div.span-2-6 {
    border-top: 0 !important; }
  main.page-article div.container div.row.service-feed div.col h3 {
    line-height: 1.2;
    /* min-height: 73px; */
    padding: 4px 10px 10px 10px; }

a.return-link {
  display: block;
  padding: 12px 0;
  font-size: 18px;
  font-size: 1.125rem; }
  a.return-link:before {
    content: "<";
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin: 0 5px 0 0; }

.team-locations {
  margin: 0 0 12px; }
  @media only screen and (min-width: 64.063em) {
    .team-locations {
      margin: 0 0 24px; } }
  .team-locations div.col {
    border-top: 0 !important; }
  .team-locations a.btn {
    display: block; }
    .team-locations a.btn.active {
      background-color: #616161; }

.personnel h2 {
  padding-top: 0 !important; }

.personnel ul li {
  padding: 0 0 6px 0; }
  @media only screen and (min-width: 64.063em) {
    .personnel ul li {
      font-size: 18px;
      font-size: 1.125rem; } }

div.personnel:not(:first-child) {
  padding-top: 12px;
  border-top: 1px solid #616161; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    div.personnel:not(:first-child) {
      padding-top: 18px; } }
  @media only screen and (min-width: 64.063em) {
    div.personnel:not(:first-child) {
      padding-top: 24px; } }

div.fund-services div.fund-item {
  padding: 14px 0; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    div.fund-services div.fund-item {
      padding: 14px 0 0 0; } }
  div.fund-services div.fund-item h3, div.fund-services div.fund-item #Header3 {
    font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    color: #0081B4; }
  div.fund-services div.fund-item p a.more-info:after {
    content: ">";
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin: 0 0 0 5px; }

div.carousel {
  position: relative; }
  div.carousel figure {
    margin: 0 !important; }
  div.carousel img {
    margin: 0 0 12px 0 !important; }
    @media only screen and (min-width: 64.063em) {
      div.carousel img {
        margin: 0 0 24px 0 !important; } }
  div.carousel figcaption h2,
  div.carousel figcaption h3 {
    padding: 0 0 10px 0 !important; }
  div.carousel figcaption #Header3 {
    font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    color: #0081B4; }
  div.carousel figcaption p {
    padding: 0 0 10px 0;
    margin-top: 0; }
    div.carousel figcaption p.intro {
      padding: 0 0 14px 0; }
      @media only screen and (min-width: 64.063em) {
        div.carousel figcaption p.intro {
          padding: 0 0 24px 0; } }
  div.carousel figcaption ul, div.carousel figcaption ol {
    padding: 10px 0;
    margin: 0 0 0 8px; }
  div.carousel figcaption ul li {
    list-style-type: none;
    position: relative;
    padding: 0 0 0 24px;
    font-size: 14px;
    font-size: 0.875rem; }
    @media only screen and (min-width: 64.063em) {
      div.carousel figcaption ul li {
        font-size: 18px;
        font-size: 1.125rem; } }
    div.carousel figcaption ul li:before {
      display: -moz-inline-stack;
      display: inline-block;
      zoom: 1;
      *display: inline;
      content: "—";
      position: absolute;
      left: 0; }
  div.carousel figcaption figure {
    margin: 14px 0; }
  div.carousel .flex-control-nav {
    position: absolute;
    bottom: 0;
    z-index: 99; }
    div.carousel .flex-control-nav li {
      margin: 0 10px 0 0; }
      div.carousel .flex-control-nav li a {
        width: 20px;
        height: 20px; }
        div.carousel .flex-control-nav li a:after {
          border-width: 20px 20px 0 0; }

div.people-feed div.col {
  border-top: 0;
  padding: 0 0 12px 0; }
  @media only screen and (min-width: 64.063em) {
    div.people-feed div.col {
      padding: 0 0 24px 0; } }
  div.people-feed div.col a:hover {
    text-decoration: none; }
    div.people-feed div.col a:hover figure img {
      -webkit-filter: grayscale(0);
      filter: grayscale(0); }
    div.people-feed div.col a:hover figure:before {
      background: transparent; }
  div.people-feed div.col a figure {
    position: relative;
    margin: 0; }
    div.people-feed div.col a figure img {
      display: block;
      -webkit-filter: grayscale(100%);
      filter: grayscale(100%);
      -webkit-transition: filter, 0.25s, linear;
      -moz-transition: filter, 0.25s, linear;
      -ms-transition: filter, 0.25s, linear;
      -o-transition: filter, 0.25s, linear;
      -webkit-transition: 0.25s, linear, -webkit-filter;
      transition: 0.25s, linear, -webkit-filter;
      transition: filter, 0.25s, linear;
      transition: filter, 0.25s, linear, -webkit-filter; }
    div.people-feed div.col a figure:hover:before {
      -webkit-transition: background, 0.25s, linear;
      -moz-transition: background, 0.25s, linear;
      -ms-transition: background, 0.25s, linear;
      -o-transition: background, 0.25s, linear;
      transition: background, 0.25s, linear;
      pointer-events: none;
      content: "";
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: rgba(0, 130, 203, 0.35);
      z-index: 10; }
  div.people-feed div.col a ul {
    padding: 10px 0 24px; }
    @media only screen and (min-width: 64.063em) {
      div.people-feed div.col a ul {
        padding: 14px 0 36px; } }
    div.people-feed div.col a ul li {
      color: #000;
      padding: 5px 0;
      line-height: 1; }
      @media only screen and (min-width: 64.063em) {
        div.people-feed div.col a ul li {
          font-size: 24px;
          font-size: 1.5rem; } }
      div.people-feed div.col a ul li h3 {
        font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        color: #0081B4;
        font-size: 16px;
        font-size: 1rem;
        padding: 0;
        margin: 0; }
        @media only screen and (min-width: 64.063em) {
          div.people-feed div.col a ul li h3 {
            font-size: 24px;
            font-size: 1.5rem; } }

.people-article {
  margin: 12px 0; }
  @media only screen and (min-width: 64.063em) {
    .people-article {
      margin: 24px 0; } }
  .people-article div.container div.row div.col {
    border-top: 0 !important; }
    .people-article div.container div.row div.col h1 {
      padding: 24px 0; }
    .people-article div.container div.row div.col h2 {
      padding: 0 0 24px 0; }
    .people-article div.container div.row div.col dl.details {
      margin: 0;
      padding: 24px 0 0 0; }
      .people-article div.container div.row div.col dl.details dt, .people-article div.container div.row div.col dl.details dl, .people-article div.container div.row div.col dl.details dd {
        width: 100%;
        display: block;
        margin: 0; }
        @media only screen and (min-width: 64.063em) {
          .people-article div.container div.row div.col dl.details dt, .people-article div.container div.row div.col dl.details dl, .people-article div.container div.row div.col dl.details dd {
            font-size: 24px;
            font-size: 1.5rem; } }
      .people-article div.container div.row div.col dl.details dt {
        font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif; }
      .people-article div.container div.row div.col dl.details dd {
        padding: 0 0 14px 0; }
        @media only screen and (min-width: 64.063em) {
          .people-article div.container div.row div.col dl.details dd {
            padding: 0 0 36px 0; } }
        .people-article div.container div.row div.col dl.details dd a {
          color: #000; }
        .people-article div.container div.row div.col dl.details dd ul.social-links {
          margin: 14px 0; }
          .people-article div.container div.row div.col dl.details dd ul.social-links li {
            display: -moz-inline-stack;
            display: inline-block;
            zoom: 1;
            *display: inline; }
            .people-article div.container div.row div.col dl.details dd ul.social-links li a {
              color: #0081B4;
              display: block;
              margin: 0 10px 0 0; }
              .people-article div.container div.row div.col dl.details dd ul.social-links li a:hover {
                text-decoration: none;
                color: #005c81; }
              .people-article div.container div.row div.col dl.details dd ul.social-links li a i {
                position: relative;
                top: -10px; }

div.location-filter div.container h1 {
  padding: 0 0 24px 0; }
  @media only screen and (min-width: 64.063em) {
    div.location-filter div.container h1 {
      padding: 0 0 48px 0; } }

div.location-filter div.container div.filter-options div.row {
  margin: 0 0 12px 0; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    div.location-filter div.container div.filter-options div.row {
      margin: 0 0 24px 0; } }
  @media only screen and (min-width: 64.063em) {
    div.location-filter div.container div.filter-options div.row {
      margin: 0 0 48px 0; } }
  div.location-filter div.container div.filter-options div.row div.col {
    padding: 0 0 16px 0; }
    @media only screen and (min-width: 47.938em) {
      div.location-filter div.container div.filter-options div.row div.col {
        padding: 0; } }

.map-images {
  margin: 12px 0; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    .map-images {
      margin: 24px 0; } }
  @media only screen and (min-width: 64.063em) {
    .map-images {
      margin: 48px 0;
      min-height: 565px; } }

.map-images img {
  margin: 0 auto;
  max-width: 953px;
  width: 100%;
  display: none; }

.map-images img.active {
  display: block; }

.map-images img, map area {
  outline: none; }

div.location-feed div.container div.row div.iso-item a:hover {
  text-decoration: none; }
  div.location-feed div.container div.row div.iso-item a:hover figure:before {
    background: rgba(0, 130, 203, 0.35); }

div.location-feed div.container div.row div.iso-item a figure {
  position: relative; }
  div.location-feed div.container div.row div.iso-item a figure img {
    display: block;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    -webkit-transition: filter, 0.25s, linear;
    -moz-transition: filter, 0.25s, linear;
    -ms-transition: filter, 0.25s, linear;
    -o-transition: filter, 0.25s, linear;
    -webkit-transition: 0.25s, linear, -webkit-filter;
    transition: 0.25s, linear, -webkit-filter;
    transition: filter, 0.25s, linear;
    transition: filter, 0.25s, linear, -webkit-filter; }
  div.location-feed div.container div.row div.iso-item a figure:before {
    -webkit-transition: background, 0.25s, linear;
    -moz-transition: background, 0.25s, linear;
    -ms-transition: background, 0.25s, linear;
    -o-transition: background, 0.25s, linear;
    transition: background, 0.25s, linear;
    pointer-events: none;
    content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: transparent;
    z-index: 10; }

div.location-feed div.container div.row div.iso-item a h3 {
  color: #0081B4;
  margin: 12px 0 24px 0; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    div.location-feed div.container div.row div.iso-item a h3 {
      font-size: 16px;
      font-size: 1rem; } }
  @media only screen and (min-width: 64.063em) {
    div.location-feed div.container div.row div.iso-item a h3 {
      margin: 32px 0 48px 0; } }

div.isotope {
  width: 100%; }

.iso-item, .grid-sizer {
  width: 100%;
  float: left;
  height: 295px; }
  @media only screen and (min-width: 47.938em) {
    .iso-item, .grid-sizer {
      width: 31.33%;
      height: 330px; } }

.gutter-sizer {
  width: 0; }
  @media only screen and (min-width: 47.938em) {
    .gutter-sizer {
      width: 3%; } }

#map {
  width: 100%;
  margin: 12px 0;
  height: 320px; }
  @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
    #map {
      margin: 24px 0;
      height: 520px; } }
  @media only screen and (min-width: 64.063em) {
    #map {
      margin: 48px 0;
      height: 650px; } }

.phoney {
  color: #fff;
  font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 24px;
  font-size: 1.5rem;
  overflow-y: hidden;
  overflow-x: hidden;
  white-space: nowrap;
  padding: 20px 10px; }

@media only screen and (min-width: 64.063em) {
  div.location-article div.container div.tab-options {
    margin: 0 0 48px 0; } }

div.location-article div.container div.tab-options p {
  color: #0081B4; }
  @media only screen and (min-width: 64.063em) {
    div.location-article div.container div.tab-options p {
      font-size: 24px;
      font-size: 1.5rem;
      margin: 0 0 36px 0; } }

div.location-article div.container div.tab-options ul li {
  margin: 0 0 16px 0; }

div.location-article div.container div.tab-content {
  display: none; }
  div.location-article div.container div.tab-content.active {
    display: block; }
  div.location-article div.container div.tab-content div.row {
    margin: 0 0 16px 0; }
    @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
      div.location-article div.container div.tab-content div.row {
        margin: 0 0 24px 0; } }
    @media only screen and (min-width: 64.063em) {
      div.location-article div.container div.tab-content div.row {
        margin: 0 0 64px 0; } }
    div.location-article div.container div.tab-content div.row div.col {
      border-top: 1px solid #616161; }
      div.location-article div.container div.tab-content div.row div.col h2, div.location-article div.container div.tab-content div.row div.col article, div.location-article div.container div.tab-content div.row div.col div.advisories {
        padding: 24px 0; }
      div.location-article div.container div.tab-content div.row div.col aside .location-article-controls .select-styled {
        margin: 32px 0 0 0; }
      div.location-article div.container div.tab-content div.row div.col aside .location-article-controls ul.page-links {
        margin: 16px 0; }
      div.location-article div.container div.tab-content div.row div.col div.locations-articles .location-article {
        display: none; }
        div.location-article div.container div.tab-content div.row div.col div.locations-articles .location-article.active {
          display: block; }
      div.location-article div.container div.tab-content div.row div.col article h2 {
        padding: 0 0 24px 0; }
      div.location-article div.container div.tab-content div.row div.col article h3 {
        padding: 10px 0; }
      div.location-article div.container div.tab-content div.row div.col article p {
        padding: 0 0 10px 0; }
        div.location-article div.container div.tab-content div.row div.col article p.intro {
          padding: 0; }
      div.location-article div.container div.tab-content div.row div.col article ul, div.location-article div.container div.tab-content div.row div.col article ol {
        padding: 10px 0; }
      div.location-article div.container div.tab-content div.row div.col article ul, div.location-article div.container div.tab-content div.row div.col article ol {
        padding: 10px 0;
        margin: 0 0 0 8px; }
      div.location-article div.container div.tab-content div.row div.col article ul li {
        list-style-type: none;
        position: relative;
        padding: 0 0 0 24px;
        font-size: 14px;
        font-size: 0.875rem; }
        @media only screen and (min-width: 64.063em) {
          div.location-article div.container div.tab-content div.row div.col article ul li {
            font-size: 18px;
            font-size: 1.125rem; } }
        div.location-article div.container div.tab-content div.row div.col article ul li:before {
          display: -moz-inline-stack;
          display: inline-block;
          zoom: 1;
          *display: inline;
          content: "—";
          position: absolute;
          left: 0; }
      div.location-article div.container div.tab-content div.row div.col article figure {
        margin: 14px 0; }
      div.location-article div.container div.tab-content div.row div.col article a.dl-link {
        display: block;
        font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        padding: 12px 0; }
        div.location-article div.container div.tab-content div.row div.col article a.dl-link:after {
          content: ">";
          display: -moz-inline-stack;
          display: inline-block;
          zoom: 1;
          *display: inline;
          margin: 0 0 0 5px; }
      div.location-article div.container div.tab-content div.row div.col ul.language-links li {
        margin: 0 0 12px 0; }
        @media only screen and (min-width: 64.063em) {
          div.location-article div.container div.tab-content div.row div.col ul.language-links li {
            display: -moz-inline-stack;
            display: inline-block;
            zoom: 1;
            *display: inline;
            margin: 0 12px 0 0; } }
        div.location-article div.container div.tab-content div.row div.col ul.language-links li a.btn {
          display: block; }
      div.location-article div.container div.tab-content div.row div.col div.advisories {
        margin: 12px 0; }
        @media only screen and (min-width: 47.938em) {
          div.location-article div.container div.tab-content div.row div.col div.advisories {
            margin: 0; } }
        div.location-article div.container div.tab-content div.row div.col div.advisories ul li a:after {
          content: ">";
          display: -moz-inline-stack;
          display: inline-block;
          zoom: 1;
          *display: inline;
          margin: 0 0 0 5px; }
        div.location-article div.container div.tab-content div.row div.col div.advisories h3 {
          font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
          padding: 12px 0;
          color: #0081B4; }
        div.location-article div.container div.tab-content div.row div.col div.advisories a.dl-link {
          font-family: "Galaxie Polaris Bold", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
          display: block;
          color: #000; }
          div.location-article div.container div.tab-content div.row div.col div.advisories a.dl-link:after {
            content: ">";
            display: -moz-inline-stack;
            display: inline-block;
            zoom: 1;
            *display: inline;
            margin: 0 0 0 5px; }

main.search-page {
  margin: 24px 0; }
  @media only screen and (min-width: 64.063em) {
    main.search-page {
      margin: 48px 0; } }
  main.search-page div.container div.select-styled {
    margin: 24px 0; }
    @media only screen and (min-width: 64.063em) {
      main.search-page div.container div.select-styled {
        margin: 103px 0 0 0; } }
  main.search-page div.container div.search-results div.search-intro h1 {
    font-size: 18px;
    font-size: 1.125rem;
    color: #0081B4; }
    @media only screen and (min-width: 47.938em) and (max-width: 64.063em) {
      main.search-page div.container div.search-results div.search-intro h1 {
        font-size: 28px;
        font-size: 1.75rem; } }
    @media only screen and (min-width: 64.063em) {
      main.search-page div.container div.search-results div.search-intro h1 {
        font-size: 36px;
        font-size: 2.25rem; } }
    main.search-page div.container div.search-results div.search-intro h1 span {
      font-family: "Galaxie Polaris Med", -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif; }
  main.search-page div.container div.search-results ul {
    max-width: 750px;
    border: 1px solid #ddd;
    padding: 24px;
    background: #F5F7F8; }
    main.search-page div.container div.search-results ul li.item {
      border-bottom: 1px solid #b7cddf;
      padding: 0 0 24px 0;
      margin: 0 0 12px 0; }
      main.search-page div.container div.search-results ul li.item a {
        text-decoration: underline; }
        main.search-page div.container div.search-results ul li.item a h3 {
          padding: 12px 0; }
      main.search-page div.container div.search-results ul li.item p {
        margin: 0; }

#intranet-filter h3 {
  padding: 0 0 12px 0; }

#intranet-filter .filter-options .filter-field {
  border-top: 0 !important; }

#intranet-filter .filter-options .search, #intranet-filter .filter-options #resetbtn {
  display: block;
  display: -moz-inline-stack;
  display: inline-block;
  zoom: 1;
  *display: inline;
  vertical-align: middle; }

#intranet-filter .filter-options input[type="text"].search {
  border: 1px solid #616161;
  padding: 12px;
  width: 100%;
  margin: 12px 0; }
  @media only screen and (min-width: 64.063em) {
    #intranet-filter .filter-options input[type="text"].search {
      width: 320px; } }

@media only screen and (min-width: 64.063em) {
  #intranet-filter .filter-options button#resetbtn {
    margin: 0 0 0 12px; } }

#intranet-filter table#document-list {
  margin: 24px 0;
  width: 100%; }
  #intranet-filter table#document-list th, #intranet-filter table#document-list td {
    padding: 12px;
    border: 1px solid #ddd; }
  #intranet-filter table#document-list thead {
    text-align: left; }
    #intranet-filter table#document-list thead tr th {
      font-family: 'Galaxie Polaris Bold';
      color: #333; }

#intranet-filter ul.pagination {
  padding: 12px 0; }
  #intranet-filter ul.pagination li {
    display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
    margin: 0 6px 0 0; }
    #intranet-filter ul.pagination li a {
      display: block;
      padding: 12px;
      border: 1px solid #ddd; }
    #intranet-filter ul.pagination li.active a {
      background-color: #00269A;
      color: #fff; }

.intranet-newsfeed div.col {
  border-top: 0;
  padding-top: 0; }

.login-form form {
  margin: 48px auto;
  max-width: 500px;
  border: 1px solid #ddd;
  padding: 24px; }
  @media only screen and (min-width: 64.063em) {
    .login-form form {
      margin: 96px auto; } }
  .login-form form legend {
    margin: 0 0 12px; }
  .login-form form label {
    margin: 0 0 12px 0;
    display: block;
    color: #0081B4; }
  .login-form form input[type="text"], .login-form form input[type="password"] {
    padding: 8px 12px;
    display: block;
    width: 100%;
    margin: 0 0 24px 0;
    border: 1px solid #ddd; }

.field-validation-error {
  color: #FF0000; }

.validation-summary-errors ul li {
  color: #FF0000; }

div#divRss h3 {
  font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont,  "Segoe UI", "Roboto", "Oxygen",  "Ubuntu", "Cantarell", "Fira Sans",  "Droid Sans", "Helvetica Neue", sans-serif; }

div#divRss .rss-item ul.feedEkList {
  padding-bottom: 24px; }
  div#divRss .rss-item ul.feedEkList li {
    padding: 0 0 12px 0; }
    div#divRss .rss-item ul.feedEkList li .itemContent {
      padding: 12px 0; }
      div#divRss .rss-item ul.feedEkList li .itemContent p {
        margin: 0;
        font-size: 14px;
        font-size: 0.875rem; }

.intranet-newsfeed div.col.span-3-6 {
  border-top: 0 !important;
  padding-top: 0 !important; }
  .intranet-newsfeed div.col.span-3-6 figure.news-item {
    margin-top: 0 !important; }
    .intranet-newsfeed div.col.span-3-6 figure.news-item figcaption a span h3 {
      font-family: "Galaxie Polaris Book", -apple-system, BlinkMacSystemFont,  "Segoe UI", "Roboto", "Oxygen",  "Ubuntu", "Cantarell", "Fira Sans",  "Droid Sans", "Helvetica Neue", sans-serif; }
    .intranet-newsfeed div.col.span-3-6 figure.news-item figcaption p {
      font-size: 14px;
      font-size: 0.875rem;
      margin-top: 0 !important; }

.reset-password-form section form {
  margin: 48px auto;
  max-width: 500px;
  border: 1px solid #ddd;
  padding: 24px; }
  @media only screen and (min-width: 64.063em) {
    .reset-password-form section form {
      margin: 96px auto; } }
  .reset-password-form section form h2 {
    margin: 0 0 24px 0; }
  .reset-password-form section form div.controls label {
    margin: 0 0 12px 0;
    display: block;
    color: #0081B4; }
  .reset-password-form section form div.controls input[type="text"], .reset-password-form section form div.controls input[type="email"], .reset-password-form section form div.controls input[type="password"] {
    padding: 8px 12px;
    display: block;
    width: 100%;
    margin: 0 0 24px 0;
    border: 1px solid #ddd; }
  .reset-password-form section form .help-block {
    margin: 0 0 24px 0; }

div.intranet-location div.container div.tab-content div.row div.col {
  border-top: 0 !important; }

/* #Cross Browser
================================================== */
.lt-ie8 * {
  behavior: url(boxsizing.htc); }

/*# sourceMappingURL=style.css.map */

.checkbox {
  padding-left: 12px
}
.page-item.active .page-link {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d; }

.tableYellow {
  color: #fff;
  background-color: rgb(252, 172, 0);
  width: 10%;
}

.notification-title{
  font-family: 'Polaris Book Bold', serif;
  font-size:24.0pt; 
  color:#f2aa0f;
}

.notification-text{
  font-family: 'Polaris Book', serif;
  font-size:20.0pt;
  color:#0c0c0c;
}

.form-control:disabled, .form-control[readonly] {
    background-color: rgb(240 242 244);
    opacity: 1; 
}

.text-red {
    color: rgb(248, 21, 21);
}

.text-orange {
    color: rgb(253, 114, 0);
}

.text-green {
    color: rgb(35, 201, 35);
}

.tooltip-inner {
    max-width: 350px;
}

.tooltip-wrapper .btn[disabled] {
    pointer-events: none;
    display: inline-block;
}

.tooltip-wrapper {
    display: inline-block;
}