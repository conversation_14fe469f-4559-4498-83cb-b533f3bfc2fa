const ApplicationModel = require('../../models/importDutyWaiverApplication').ImportDutyWaiverApplicationModel;


exports.getApplicationFiles = async function (req, res) {
    try {
        const application = await ApplicationModel.findOne({ _id: req.params.id });
        if (!application) {
            return res.status(404).end();
        }
        const filesInformation = {
            affidavit: { name: "Affidavit", files: application.affidavit, filesCount: application.affidavit.length },
            marriageCertificates: { name: "Marriage Certificates", files: application.marriageCertificates, filesCount: application.marriageCertificates.length },
            birthCertificate: { name: "Copy of applicant s TCI birth certificate and a valid Government issued identification", files: application.birthCertificate, filesCount: application.birthCertificate.length },
            parentsDocuments: { name: "Parent’s documents", files: application.parentsDocuments, filesCount: application.parentsDocuments.length },
            itemsInvoices: { name: "Copies of invoices", files: application.itemsInvoices, filesCount: application.itemsInvoices.length },
            buildingPictures: { name: "Building photographs", files: application.buildingPictures, filesCount: application.buildingPictures.length },
            buildingPermit: { name: "Building permit", files: application.buildingPermit, filesCount: application.buildingPermit.length },
            landRegister: { name: "A certified copy of the Land Register Extract", files: application.landRegister, filesCount: application.landRegister.length },
            valuation: { name: "Valuation", files: application.valuation, filesCount: application.valuation.length },
            statusCard: { name: "Turks & Caicos Islander Status Card", files: application.statusCard, filesCount: application.statusCard.length },
            botcCertificate: { name: "Copy of BOTC certificate along with a Government issued identification", files: application.botcCertificate, filesCount: application.botcCertificate.length },
        }
        return res.render('home-owner-policy/file-list', { application, filesInformation, formType: 'import-duty-waiver' });
    } catch (error) {
        console.log("error ", error);
        return res.status(500).end();
    }
};